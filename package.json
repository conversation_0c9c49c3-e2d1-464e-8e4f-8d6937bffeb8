{"name": "shida-vue-admin-template", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.6.8", "compression-webpack-plugin": "^11.1.0", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "default-passive-events": "^2.0.0", "element-ui": "^2.15.14", "image-conversion": "^2.1.1", "js-cookie": "^3.0.5", "less": "^4.2.0", "less-loader": "^12.2.0", "nprogress": "^0.2.0", "umy-ui": "^1.1.6", "unocss": "0.58.0", "v-viewer": "^1.7.4", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "wangeditor": "^4.7.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@unocss/webpack": "0.58.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/multi-word-component-names": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}