.flexd{
    display: flex;
    align-items: center;

}
.w100{
    width: 100%;

}
.w108{
   width: 108px;
    height: 108px;
    display: block;
    margin-left: 24px;
}
.schoolImg{
    width: 200px;
    height: 150px;
    margin-bottom: 24px;
    display: block;
    box-shadow: 0 0 4px gainsboro;
}
.center{
    text-align: center;
    justify-content: center;
}
.html{
width: 500px;
    height: 800px;
    overflow: auto;
}
.flexd{
    display: flex;
    align-items: center;
 }
 .wrap{
     flex-wrap: wrap;
 }
 .bottomNav{
     position: fixed;
     bottom:0;
     left: 0;
     width: 100%;
     box-shadow: 0 0 4px gainsboro;
     background: white;
     padding-top: 12px;}
 .w100{
     width: 100%;
 }
 .w50{
     width: 50%;
 }
 .w30{
     width: 30%;
 }
 .w70{
     width: 70%;
 }
 .flex1{
     flex:1
 }
 .right{
     text-align: right;
     justify-content: flex-end;
 }
 .h5 img{
     max-width: 100%;
 }
 .mb12{
     margin-bottom: 12px;
 }
 .center{
     text-align: center;
     justify-content: center;
 }
 .bold{
     font-weight: bold;
 }
 .black{
     color:#333
 }
 .bigBlack{
     color:#000
 }
 .f16{
     font-size: 16px;
 }
 .elli1{
     white-space: nowrap;
     width: 100%;
     overflow: hidden;
     text-overflow:ellipsis;
 }
 .elli2{
     width:100%;
     word-break:break-all;
     display:-webkit-box;
     -webkit-line-clamp:2;
     -webkit-box-orient:vertical;
     overflow:hidden;
 }
 .normal{
     color:#666
 }
 .grey{
     color:#999
 }
 .red{
     color:red
 }
 *{
     box-sizing: border-box;
 }
 ::-webkit-scrollbar {/*滚动条整体样式*/

     /*width: 5px;     !*高宽分别对应横竖滚动条的尺寸*!*/

     /*height: 1px;*/

 }
 .radius{
     border-radius: 8px;
 }
 ::-webkit-scrollbar-thumb {/*滚动条里面小方块*/

     border-radius: 10px;

      -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);

     background: #26a07f;

 }

 ::-webkit-scrollbar-track {/*滚动条里面轨道*/

     -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);

     border-radius: 10px;

     background: #EDEDED;

 }
 .el-drawer__container *{
     outline: 0!important;
 }
.el-dialog__body img{
    max-width: 600px;
    max-height: 800px;
}
