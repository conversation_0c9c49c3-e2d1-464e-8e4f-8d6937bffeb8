import Vue from 'vue'
import App from './App.vue'
// 将所有的事件监听器设置为被动模式（passive mode）, 被动模式的事件监听器可以提升滚动性能
import 'default-passive-events'
// 引入router路由
import router from '@/router/index.js'
// 样式
import '@/styles/index.scss'
// 引入vuex
import store from '@/store'
// 引入路由守卫
import '@/permissions.js'
// 引入加载进度条
import 'nprogress/nprogress.css'
// 引入element-ui
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import UmyUi from 'umy-ui'
import 'umy-ui/lib/theme-chalk/index.css'
// 下载文件
import { download } from "@/utils/request.js"
// 引入unocss
import 'uno.css'
// 注册侧边栏组件
import SideBarItem from '@/layouts/SideBar/SideBarItem.vue'
// 引入工具类
import Utils from '@/utils/index'
import dayjs from 'dayjs';
Vue.use(UmyUi);
Vue.use(Utils)
Vue.use(ElementUI, { size: 'default' })
Vue.component('SideBarItem', SideBarItem)

// 引入自定义指令
// import HasPermission from '@/directives/HasPermission/HasPermission.js'
// Vue.directive('has-perm', HasPermission)

// 默认每页个数
Vue.prototype.$pageSizes = [10, 20, 30, 40, 50]
// 公用下载
Vue.prototype.$download = download
Vue.prototype.$dayJs = dayjs
import Viewer from 'v-viewer'
import 'viewerjs/dist/viewer.css'
Vue.use(Viewer);
Viewer.setDefaults({
  Options: { 'inline': true, 'button': true, 'navbar': true, 'title': true, 'toolbar': true, 'tooltip': true, 'movable': true, 'zoomable': true, 'rotatable': true, 'scalable': true, 'transition': true, 'fullscreen': true, 'keyboard': true, 'url': 'data-source', zIndexInline: 100  }
});
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
