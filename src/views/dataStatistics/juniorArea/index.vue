<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div>
        <el-button
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="exportStatistics"
          :loading="exportLoading"
          >导出</el-button
        >
      </div>
      <div>
<!--        <el-form :model="search" :inline="true">-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.nature"-->
<!--              placeholder="学校性质"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="乡镇学校" :value="1"></el-option>-->
<!--              <el-option label="城区学校" :value="2"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-button-->
<!--              size="small"-->
<!--              type="primary"-->
<!--              icon="el-icon-search"-->
<!--              @click="searchSubmit"-->
<!--            ></el-button>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
      </div>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学校性质"
        prop="natureName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名学校"
        prop="schoolName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="计划招生数"
        prop="planNum"
      ></el-table-column>
      <el-table-column
      	v-for="colItem, colIdx in columnObj"
      	:key="colItem.name"
        align="center"
        :label="colItem.nameStr"
        :prop="colItem.name"
      ></el-table-column>
      <el-table-column align="center" label="合计" prop="totalCount">
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import { schoolStatistics, schoolStatisticsColumn } from "@/api/statistics";
import { pref } from "@/utils/common";
export default {
  mixins: [TableMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      search: {
        nature: "2",
        period: "3",
      },
      exportLoading: false,
      columnObj: [],
    };
  },
  created() {},
  methods: {
    // 列表
    async getTableData() {
      this.tableLoading = true;
      this.columnObj = await schoolStatisticsColumn(
        { period: this.search.period, nature: this.search.nature },
        this.prefixDeptCode
      );
      schoolStatistics(this.search, this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        })
    },
    // 导出
    exportStatistics() {
      this.$message.info('正在导出，数据量较大，请耐心等待')
      this.exportLoading = true;
      let params = {
        period: this.search.period,
        nature: this.search.nature,
      };
      let url = ""
      if (this.prefixDeptCode == "130503" || this.prefixDeptCode == "130502") {
        url = `${pref}${this.prefixDeptCode}/biz/statistics/exportXinDu`
      } else {
        url = `${pref}${this.prefixDeptCode}/biz/statistics/export`
      }
      this.$download(
        url,
        params,
        "xls",
        "城区初中统计.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>