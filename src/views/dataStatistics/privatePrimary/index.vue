<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div>
        <el-button
            size="small"
            type="warning"
            icon="el-icon-download"
            @click="exportStatistics"
            :loading="exportLoading"
        >导出</el-button
        >
      </div>
      <div>
        <el-form :model="search" :inline="true">
<!--          <el-form-item>
            <el-select
                size="small"
                v-model="search.nature"
                placeholder="学校性质"
                clearable
            >
              <el-option label="乡镇学校" :value="1"></el-option>
              <el-option label="城区学校" :value="2"></el-option>
            </el-select>
          </el-form-item>-->
          <el-form-item>
            <el-select
                size="small"
                v-model="search.nature"
                placeholder="学校名称"
                clearable
                filterable
            >
             <el-option
                 v-for="item in schoolList"
                 :label="item.deptName"
                 :value="item.id"
                 :key="item.id"
             ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
          align="center"
          label="序号"
          width="50"
          type="index"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学校类型"
          prop="nature"
      >
        <template slot-scope="{row}">
          {{row.type==1?'公办':row.type==2?'民办':''}}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="学校性质"
          prop="nature"
      >
        <template slot-scope="{row}">
          {{row.nature==1?'乡镇':row.nature==2?'城区':''}}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="报名学校"
          prop="schoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="招生计划数"
          prop="planNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报名人数"
          prop="signNum"
      ></el-table-column>
<!--      <el-table-column
          v-for="colItem, colIdx in columnObj"
          :key="colItem.name"
          align="center"
          :label="colItem.nameStr"
          :prop="colItem.name"
      ></el-table-column>-->
<!--      <el-table-column align="center" label="合计" prop="totalCount">-->
<!--      </el-table-column>-->
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import {privateTongJi} from "@/api/statistics";
import { pref } from "@/utils/common";
import {getDepts} from "@/api/common";
export default {
  mixins: [TableMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      schoolList:[],
      search: {
        nature: "",
        period: "2",
        type:2
      },
      exportLoading: false,
      columnObj: [],
    };
  },
  created() {
    let primarySchoolParams = {
      level: 3,
      period: "2",
      type:2
    };
    getDepts(primarySchoolParams).then((res) => {
      this.schoolList = res;
    });
    },
  methods: {
    // 列表
    async getTableData() {
      this.tableLoading = true;
     /* this.columnObj = await schoolStatisticsColumn(
          { period: this.search.period, nature: this.search.nature },
          this.prefixDeptCode
      );*/
      privateTongJi(this.search, this.prefixDeptCode)
          .then((res) => {
            this.tableData = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    // 导出
    exportStatistics() {
        this.$message.info('正在导出，数据量较大，请耐心等待')
      this.exportLoading = true;
      let params = {
        period: this.search.period,
        nature: this.search.nature,
        type: this.search.type,
      };
      this.$download(
          `${pref}${this.prefixDeptCode}/biz/studentPrivateeStatistics/exportStudent`,
          params,
          "xls",
          "民办小学统计.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
