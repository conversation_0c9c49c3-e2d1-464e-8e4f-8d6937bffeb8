<template>
  <div>
    <div class="search-form sd-m-b-30 sd-m-t-30">{{ schoolName }}</div>
    <el-table :data="tableData" border stripe v-loading="tableLoading" v-if="this.type==1">
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名学校"
        prop="schoolName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="计划招生数"
        prop="planNum"
      ></el-table-column>
      <el-table-column
          v-for="colItem, colIdx in columnObj"
          :key="colItem.name"
          align="center"
          :label="colItem.nameStr"
          :prop="colItem.name"
      ></el-table-column>
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="房户一致"-->
<!--        prop="householderAccordanceNum"-->
<!--        v-if="columnObj.householderAccordanceNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="户口"-->
<!--        prop="registeredPermanentResidenceNum"-->
<!--        v-if="columnObj.registeredPermanentResidenceNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="房产"-->
<!--        prop="housePropertyNum"-->
<!--        v-if="columnObj.housePropertyNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="随迁子女"-->
<!--        prop="migrantChildrenNum"-->
<!--        v-if="columnObj.migrantChildrenNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="优抚对象"-->
<!--        prop="entitledGroupNum"-->
<!--        v-if="columnObj.entitledGroupNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="本县经商"-->
<!--        prop="countyMercantileNum"-->
<!--        v-if="columnObj.countyMercantileNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="本县务工"-->
<!--        prop="countyWorkerNum"-->
<!--        v-if="columnObj.countyWorkerNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="其他"-->
<!--        prop="elseNum"-->
<!--        v-if="columnObj.elseNum"-->
<!--      ></el-table-column>-->
      <el-table-column align="center" label="合计" prop="totalCount">
      </el-table-column>
    </el-table>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading" v-if="this.type==2">
      <el-table-column
          align="center"
          label="序号"
          width="60"
          type="index"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报名学校"
          prop="schoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="计划招生数"
          prop="planNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报名人数"
          prop="signNum"
      ></el-table-column>
      <el-table-column align="center" label="合计" prop="signNum">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  currentSchoolStatistics, privateTongJi,
  schoolStatisticsColumn,
} from "@/api/statistics";
export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      search: {
        key: this.$store.getters.deptId,
      },
      tableData: [],
      schoolName: this.$store.getters.userInfo.deptName,
      tableLoading: false,
      columnObj: [],
      type:this.$store.getters.userInfo.deptInfo.type,
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
    // 列表
    async getTableData() {
      this.tableLoading = true;
      if(this.type==1){
        this.columnObj = await schoolStatisticsColumn(
            {
              period: this.$store.getters.period,
              nature: this.$store.getters.userInfo.deptInfo.nature,
            },
            this.prefixDeptCode
        );
        // 按照批次顺序排序
        if (this.columnObj && this.columnObj.length > 0) {
          const batchOrder = [
            "第一批次",
            "第二批次",
            "第三批次",
            "第四批次",
            "第五批次",
            "第六批次",
            "第七批次"
          ];

          this.columnObj.sort((a, b) => {
            const indexA = batchOrder.indexOf(a.nameStr);
            const indexB = batchOrder.indexOf(b.nameStr);

            // 如果都找到了索引，按索引排序
            if (indexA !== -1 && indexB !== -1) {
              return indexA - indexB;
            }

            // 如果只有A找到了索引，A排在前面
            if (indexA !== -1 && indexB === -1) {
              return -1;
            }

            // 如果只有B找到了索引，B排在前面
            if (indexA === -1 && indexB !== -1) {
              return 1;
            }

            // 如果都没找到索引，保持原有顺序
            return 0;
          });
        }
        // for (let i = 0; i < columnList.length; i++) {
        //   let key = columnList[i].name;
        //   this.columnObj[key] = true;
        // }
        currentSchoolStatistics(this.search, this.prefixDeptCode)
            .then((res) => {
              this.tableData.push(res);
            })
            .finally(() => {
              this.tableLoading = false;
            });
      }else {
        privateTongJi({schoolId: this.$store.getters.deptId,pageNumber:1, pageSize: 10}, this.prefixDeptCode)
            .then((res) => {
              this.tableData = res;
            })
            .finally(() => {
              this.tableLoading = false;
            });
      }

    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  font-size: 24px;
}
</style>