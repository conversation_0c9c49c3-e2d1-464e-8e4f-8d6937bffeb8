<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button type="primary" icon="el-icon-back" @click="back" size="small">返回</el-button>
        <el-button size="small" type="primary" @click="add" icon="el-icon-plus">添加</el-button>
      </div>
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input size="small" v-model.trim="search.keywords" placeholder="搜索小区/村庄名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" icon="el-icon-search" @click="searchSubmit"></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="flex-center school-name">
      {{ schoolName }}
    </div>
    <el-table :data="tableData.records" border stripe style="width: 100%" v-loading="tableLoading">
      <el-table-column align="center" label="序号" width="60" type="index" fixed="left"></el-table-column>
      <el-table-column align="center" label="街/镇" prop="level1Name"></el-table-column>
      <el-table-column align="center" label="社区/村" prop="level2Name"></el-table-column>
      <el-table-column align="center" label="小区名称" prop="level3Name"></el-table-column>
      <el-table-column align="center" label="操作" width="200px">
        <template slot-scope="{ row }">
          <el-link icon="el-icon-edit" type="warning" :underline="false" @click="edit(row)"
            style="margin-right: 10px">编辑</el-link>
          <el-link type="danger" icon="el-icon-delete" @click="del(row)" :underline="false">删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber" layout="total, prev, pager, next, sizes" :page-sizes="$pageSizes"
        :total="total">
      </el-pagination>
    </div>

    <!-- 新增，编辑 -->
    <el-dialog :title="dialogTitle" :visible.sync="modal.addOrEdit" center width="600px" :close-on-click-modal="false">
      <el-form :model="form" ref="form" :rules="rules" label-width="130px">
        <el-form-item label="所属学校">
          <span>{{ schoolName }}</span>
        </el-form-item>
        <el-form-item prop="level1Data" label="街/镇">
          <el-select v-model="form.level1Id" placeholder="请选择街/镇" class="sd-w-200" @change="getLevel2Data()">
            <el-option v-for="tpItem in level1Data" :key="tpItem.level1Id" :value="tpItem.level1Id"
              :label="tpItem.level1Name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="level2Data" label="社区/村">
          <el-select v-model="form.level2Id" placeholder="请选择社区/村" class="sd-w-200" @change="getLevel3Data()">
            <el-option v-for="tpItem in level2Data" :key="tpItem.level2Id" :value="tpItem.level2Id"
              :label="tpItem.level2Name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="level3Data" label="小区名称">
          <el-select v-model="form.level3Id" placeholder="请选择小区名称" class="sd-w-200">
            <el-option v-for="tpItem in level3Data" :key="tpItem.level2Id" :value="tpItem.level2Id"
              :label="tpItem.level2Name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)">取消</el-button>
        <el-button size="small" type="primary" @click="confirmUpdate">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getSchoolEnrollScope,
  getStreetList,
  addSchoolScope,
  updateSchoolScope,
  deleteSchoolScope,
  getSubList,
  updateSchoolScopeCheck,
} from "@/api/setting";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: "",
        schoolId: "",
        countyId: this.$store.getters.deptId,
        type: 2, // 1区县级别 2学校级别
      },
      countSearch: {
        parentId: this.$store.getters.deptId,
        type: 1, // 1区县级别 2学校级别
      },
      schoolenrollType: [],
      level1Data: [],
      level2Data: [],
      level3Data: [],
      addData: {
        enrollType: "", // 招生类别
        rangeName: "", // 招生范围
      },
      form: {
        id: "",
        enrollType: "",
        schoolId: "",
        rangeId: "",
        rangeName: "",
        level1Id: '',
        level1Name: '',
        level2Id: '',
        level2Name: '',
        level3Id: '',
        level3Name: '',
      },
      editForm: {
        level1Id: '',
        level1Name: '',
      },
      edit2Form: {
        level2Id: '',
        level2Name: '',
      },
      edit3Form: {
        level3Id: '',
        level3Name: '',
        schoolId: this.$route.query.schoolId,
      },
      rules: {
        rangeName: [
          { required: true, message: "请输入招生范围", trigger: "blur" },
        ],
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
      },
      schoolName: "",
    };
  },
  created() {
    this.search.schoolId = this.$route.query.schoolId;
    this.form.schoolId = this.$route.query.schoolId;
    this.schoolName = this.$route.query.schoolName;
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      this.search.schoolId = this.$route.query.schoolId;
      getSchoolEnrollScope(this.search)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    getLevel2Data() {
      this.form.level2Id = null
      this.form.level3Id = null
      getSubList(this.form).then((res) => {
        this.level2Data = res
      })
    },
    getLevel3Data() {
      this.form.level3Id = null
      getSubList(this.form).then((res) => {
        this.level3Data = res

      })
    },
    // 添加
    add() {
      this.level1Data = []
      this.level2Data = []
      this.level3Data = []
      this.form.level1Id = null
      this.form.level1Name = null
      this.form.level2Id = null
      this.form.level2Name = null
      this.form.level3Id = null
      this.form.level3Name = null
      this.dialogTitle = "招生范围";
      this.switchModal("addOrEdit", true);
      getStreetList(this.search).then((res) => {
        this.level1Data = res
      });
      console.log(this.leve1Data)

    },
    // 编辑
    edit(row) {
      console.log(row);
      this.dialogTitle = "编辑招生范围";
      this.switchModal("addOrEdit", true);

      // 重置表单
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });

      // 获取一级数据
      getStreetList(this.search).then((res) => {
        this.level1Data = res;
        this.form.level1Id = row.level1Id;
        this.form.level1Name = row.level1Name;

        // 获取二级数据
        return getSubList({ level1Id: row.level1Id });
      }).then((res) => {
        this.level2Data = res;
        this.form.level2Id = row.level2Id;
        this.form.level2Name = row.level2Name;

        // 获取三级数据
        return getSubList({ level2Id: row.level2Id });
      }).then((res) => {
        this.level3Data = res;
        this.form.level3Id = row.level3Id;
        this.form.level3Name = row.level3Name;
        this.form.id = row.id;
        // 填充其他表单字段
        this.form.rangeName = row.rangeName;
      });
    },
    // 删除
    del(row) {
      this.$confirm(`确定删除【招生类别：${row.level3Name}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        deleteSchoolScope({ id: row.id }).then(() => this.getTableData());
      });
    },
    // 提交
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            addSchoolScope(this.form).then((res) => {
              if (res.data) {
                this.$message.success("操作成功");
                this.switchModal("addOrEdit", false);
                this.getTableData();
              } else {
                this.$message.success("重复记录");
                this.switchModal("addOrEdit", false);
                this.getTableData();
              }
            });
          } else {
            // 假设存在编辑的 API 调用
            updateSchoolScopeCheck(this.form).then((res) => {
              console.log(res)
              if (res) {
                this.$confirm(`【招生类别：${this.form.level3Name}】已存在，是否添加`, "提示", {
                  type: "warning",
                }).then(() => {
                  updateSchoolScope(this.form).then((res) => {
                    this.$message.success("操作成功");
                    this.switchModal("addOrEdit", false);
                    this.getTableData();
                  });
                });
              } else {
                updateSchoolScope(this.form).then((res) => {
                  this.$message.success("操作成功");
                  this.switchModal("addOrEdit", false);
                  this.getTableData();
                });
              }
            });
          }
        }
      });
    },
    // 返回
    back() {
      this.$router.push("/setting/schoolenrollmentscopeconfiguration");
    },
  },
};
</script>

<style lang="scss" scoped>
.school-name {
  font-size: 22px;
  padding-bottom: 20px;
}
</style>