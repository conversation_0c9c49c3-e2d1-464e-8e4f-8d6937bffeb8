<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="add">添加</el-button>
        <el-button size="small" type="success" icon="el-icon-upload2" @click="importBatch">批量导入</el-button>
      </div>
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input size="small" v-model.trim="search.level1Name" placeholder="搜索街/镇名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-input size="small" v-model.trim="search.level2Name" placeholder="搜索社区/村庄名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-input size="small" v-model.trim="search.level3Name" placeholder="搜索小区名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" icon="el-icon-search" @click="searchSubmit"></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
<el-table :data="flatData" border stripe style="width: 100%" v-loading="tableLoading">
      <el-table-column align="center" label="序号" width="60" type="index" fixed="left"></el-table-column>
      <el-table-column align="center" label="街/镇" prop="level1Name"></el-table-column>
      <el-table-column align="center" label="社区/村" prop="level2Name"></el-table-column>
      <el-table-column align="center" label="小区名称" prop="level3Name"></el-table-column>
      <el-table-column align="center" width="200" label="操作">
        <template slot-scope="{ row }">
          <el-link icon="el-icon-edit" type="warning" :underline="false" @click="edit(row)">编辑</el-link>
          <el-link
            icon="el-icon-delete"
            type="danger"
            :underline="false"
            style="margin-right: 10px"
            @click="del(row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber" layout="total, prev, pager, next, sizes" :page-sizes="$pageSizes"
        :total="total">
      </el-pagination>
    </div>
    <!-- 新增，编辑 -->
    <el-dialog :title="dialogTitle" :visible.sync="modal.addOrEdit" center width="500px" :close-on-click-modal="false">
      <el-form :model="form" ref="form" :rules="rules" label-width="130px">
        <el-form-item prop="name" label="街/镇">
          <el-input size="small" v-model.trim="form.name" placeholder="请输入街/镇" style="width: 220px"></el-input>
        </el-form-item>
         <el-form-item prop="children[0].name" label="社区/村">
          <el-input size="small" v-model.trim="form.children[0].name" placeholder="请输入社区/村" style="width: 220px"></el-input>
        </el-form-item>
         <el-form-item prop="children[0].children[0].name" label="小区名称">
          <el-input size="small" v-model.trim="form.children[0].children[0].name" placeholder="请输入小区名称" style="width: 220px"></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)">取消</el-button>
        <el-button size="small" type="primary" @click="confirmUpdate">确定</el-button>
      </div>
    </el-dialog>

    <!-- 批量导入 -->
    <el-dialog :title="dialogTitle" :visible.sync="modal.import" center width="800px" :close-on-click-modal="false">
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button type="info" @click="downloadTemplateFile" icon="el-icon-download" size="small">下载模板</el-button>
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload ref="upload" accept=".xlsx,.xls" action="" :file-list="fileList" :auto-upload="false" :limit="1"
            :on-remove="onRemove" :on-exceed="onExceed" :on-change="onChange">
            <el-button size="small" type="primary" icon="el-icon-folder-opened">选择文件</el-button>
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)">取消</el-button>
        <el-button size="small" type="primary" @click="uploadSubmit" :disabled="fileList.length == 0">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getEnrollScopeList,
  creatEnrollScopeInfo,
  updateEnrollScopeInfo,
  deleteEnrollScopeInfo,
  importEnrollScopeInfo,
} from "@/api/setting";
import { getDepts } from "@/api/common";
import { schoolTypeOptions } from "@/utils/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      isFive: this.$store.getters.userInfo.isFive,
      search: {
        pageNumber: 1,
        pageSize: 10,
        level1Name: "",
        level2Name: "",
        level3Name: "",
        period: "",
        schoolId: "",
        parentId: this.$store.getters.deptId,
        type: 1, // 1区县级别 2学校级别
      },

      form: {
        id: "",
        name: "",
        level:1,
        children:[{
          id:"",
          name:"",
          level:2,
          children:[{
            id:"",
            name:"",
            level:3,
          }]
        }]
      },
   rules: { 
        name: [ 
          { required: true, message: "请输入街/镇", trigger: "blur" },
        ],
        'children[0].name': [ 
          { required: true, message: "请输入社区/村", trigger: "blur" },
        ],
        'children[0].children[0].name': [ 
          { required: true, message: "请输入小区名称", trigger: "blur" },
        ],
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
        import: false,
      },
      schoolList: [],
      schoolList1: [],
      periodOptions: schoolTypeOptions,
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
      flatData: [],
    };
  },
  created() {
    if (this.isFive) {
      this.form.period = '2'
      this.getDepts()
      this.getDepts1()
    }
  },
  methods: {
    
    // 列表
    getTableData() {
       this.flatData = []; 
      this.tableLoading = true;
      getEnrollScopeList(this.search)
        .then((res) => {
          this.tableData = res;
          this.flatData = this.tableData.records
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 根据学段获取学校
    periodChange() {
      this.getDepts();
      this.search.schoolId = "";
    },
    // 获取学校
    getDepts() {
      let params = {
        level: 3,
        period: this.search.period,
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 添加中根据学段获取学校
    periodChange1() {
      this.getDepts1();
      this.form.schoolId = "";
    },
    // 添加中获取学校
    getDepts1() {
      let params = {
        level: 3,
        period: this.form.period,
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList1 = res;
      });
    },
    // 添加
    add() {
      this.dialogTitle = "添加招生范围";
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
    },
    // 编辑
   edit(row) {
  this.dialogTitle = "修改招生范围";
  this.switchModal("addOrEdit", true);
  
  this.$nextTick(() => {
    // 构建表单数据结构
    this.form = {
      id: row.level1Id || "",
      name: row.level1Name || "",
      level: 1,
      children: [
        {
          id: row.level2Id || "",
          name: row.level2Name || "",
          level: 2,
          children: [
            {
              id: row.level3Id || "",
              name: row.level3Name || "",
              level: 3
            }
          ]
        }
      ]
    };
    
    console.log("编辑表单数据:", this.form);
  });
},
    // 确认更新
    confirmUpdate(row) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            this.form.parentId = this.$store.getters.deptId;
            creatEnrollScopeInfo(this.form).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getTableData();
            });
          }else{
            updateEnrollScopeInfo(this.form).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getTableData();
            });
          }
        }
      });
    },
    // 删除
    del(row) {
      this.form = {
      id: row.level1Id || "",
      name: row.level1Name || "",
      level: 1,
      children: [
        {
          id: row.level2Id || "",
          name: row.level2Name || "",
          level: 2,
          children: [
            {
              id: row.level3Id || "",
              name: row.level3Name || "",
              level: 3
            }
          ]
        }
      ]
    };
      console.log("删除区域数据:", this.form);
      this.$confirm(`确认删除区域：${row.level3Name}  ？`, "删除区域", {
        type: "error",
      }).then(() => {
        deleteEnrollScopeInfo(this.form).then((res) => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "批量导入";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.downloadLoading = true;
      this.$download(
        "/user-api/center/enrollmentScopeConfiguration/downTemplate",
        {},
        "xls",
        "学校招生范围导入模版.xls"
      ).then((res) => {
        this.$message
          .success("下载成功")
          .finally(() => (this.downloadLoading = false));
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importEnrollScopeInfo(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
