<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">

        <el-form :inline="true">
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.keywords"
                placeholder="姓名/身份证"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="importBatch"
          >批量导入</el-button
        >
      </div>
      <div class="sd-search">
      </div>
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      style="width: 100%"
      v-loading="tableLoading"
    >
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
      ></el-table-column>
      <el-table-column align="center" label="性别" prop="sex">
        <template slot-scope="{ row }">
          <span>{{ switchGender(row.studentGender) }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学籍号"
          prop="studentStatusNumber"
          show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名学校"
        prop="enrollSchoolName"
      ></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{ row }">
          <el-link type="warning" icon="el-icon-edit" @click="edit(row)">编辑</el-link
          >
          <el-link type="danger" icon="el-icon-delete" @click="del(row)">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened">选择文件</el-button>
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
        title="编辑"
        :visible.sync="editShow"
        width="800px"
        center
    >
      <el-form :model="form" label-width="300px">
        <el-form-item
            label="姓名"
        >
          <el-input
              clearable
              style="width: 220px"
              v-model="form.studentName"
              placeholder="请输入姓名"
          ></el-input>
        </el-form-item>
        <el-form-item
            label="性别"
        >
          <el-select
              clearable
              v-model="form.studentGender"
              placeholder="请选择性别"
          >
            <el-option
                label="男"
                :value="1"
            ></el-option>
            <el-option
                label="女"
                :value="2"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
            label="身份证号"
        >
          <el-input
              clearable
              style="width: 220px"
              v-model="form.studentIdCardNumber"
              placeholder="请输入身份证号"
          ></el-input>
        </el-form-item>
        <el-form-item
            label="学籍号"
        >
          <el-input  clearable style="width: 220px" v-model="form.studentStatusNumber" placeholder="请输入学籍号"></el-input>
        </el-form-item>
        <el-form-item label="报名学校">
          <el-input
              clearable
              style="width: 220px"
              v-model="form.enrollSchoolName"
              placeholder="请输入报名学校"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editShow = false">取 消</el-button>
        <el-button type="primary" @click="addOrEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {pref} from "@/utils/common";
import { getSupplementList,updateSupplementInfo,deleteSupplementInfo,importSupplementInfo} from "@/api/setting";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      editShow:false,
      prefixDeptCode: this.$store.getters.deptCode,
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: ""
      },
      form: {
        id: "",
        period: "",
        studentIdCardNumber: "",
        studentGender: "",
        studentStatusNumber: "",
        studentName: "",
        enrollSchoolName:"",
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
        import: false,
      },
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
    };
  },
  created() {
    console.log(this.$store.getters.userInfo.isFive,"this.$store")
  },
  computed: {
    switchGender() {
      return (val) => (val == "1" ? "男" : val == "2" ? "女" : "");
    },
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      getSupplementList(this.search,this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 编辑
    edit(row){
      this.editShow=true
      this.title='编辑学生学籍'
      this.form=row
    },
    del(row){
      this.$confirm('此操作将删除此补录学生, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSupplementInfo({key:row.id},this.prefixDeptCode).then(res=>{
          if(res) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    addOrEdit(){
      updateSupplementInfo(this.form,this.prefixDeptCode).then(res=>{
        if(res){
          this.editShow=false
          this.$message.success('操作成功')
          this.getTableData()
        }
      })
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "批量导入";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.downloadLoading = true;
      this.$download(
        `${pref + this.prefixDeptCode}/biz/supplementInfo/downTemplate`,
        {},
        "xls",
        "补录名单导入模版.xls"
      ).then((res) => {
        this.$message
          .success("下载成功")
          .finally(() => (this.downloadLoading = false));
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importSupplementInfo(formData,this.prefixDeptCode).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
