<template>
  <div>
    <el-tabs
      tab-position="left"
      :stretch="true"
      type="border-card"
      v-model="deptCode"
			@tab-click="tabClick"
    >
      <el-tab-pane
        v-for="item in deptOptions"
        :label="item.deptName"
        :name="item.deptCode"
        :key="item.id"
      >
        <div class="switch-title">{{ item.deptName }}中小学教育局功能开关设置</div>
        <div class="switch-container">
          <ul class="switch-box">
            <li class="switch-item" v-for="switchItem in switchList" :key="switchItem.id">
              <span class="switch-item-label">{{ switchItem.permissionName }}</span>
              <el-switch
                v-model="switchItem.statusTemp"
                active-color="#13ce66"
                inactive-color="#dcdfe6"
                @change="handleSwitch(switchItem)"
              >
              </el-switch>
            </li>
          </ul>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getDepts } from "@/api/common";
import { permissionList, permissionSave } from "@/api/setting"
export default {
  name: "signExportSet",
  data() {
    return {
      deptCode: "",
      deptOptions: [],
			switchList: []
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
		// 获取区县
    async getTableData() {
      this.deptOptions = await getDepts({ level: 2 })
      this.deptCode = this.deptOptions[0].deptCode
			this.tabClick({ name: this.deptCode })
    },
		// 所有开关
		getSwitchList(code) {
		  permissionList({}, code).then((res) => {
		    this.switchList = res;
		    this.switchList.forEach((item) => {
		      item.status == "1"
		        ? (item.statusTemp = false)
		        : (item.statusTemp = true);
		    });
		  });
		},
		// 开关操作
		handleSwitch(item) {
		  let params = {
		    id: item.id,
		    status: item.statusTemp ? "2" : "1",
		  };
		  permissionSave(params, this.deptCode).then((res) => {
		    this.$message.success("操作成功");
		    this.getSwitchList(this.deptCode);
		  });
		},
		// 区县点击
		tabClick(tab) {
			this.deptCode = tab.name
			this.getSwitchList(tab.name)
		}
  },
};
</script>

<style scoped lang="scss">
.el-tabs {
  height: calc(100vh - 160px);
  margin-top: 10px;
}
.switch-title {
  font-size: 20px;
  margin-bottom: 30px;
}
.switch-container {
  width: 800px;
  display: flex;
  justify-content: space-between;
  .switch-box {
    .el-switch {
      margin-left: 20px;
    }
    .switch-item {
      margin-bottom: 15px;
      .switch-item-label {
        display: inline-block;
        width: 260px;
      }
    }
  }
}
</style>
