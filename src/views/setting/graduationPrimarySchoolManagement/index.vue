<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button size="small" type="success" icon="el-icon-upload2" @click="importBatch">学校批量导入</el-button>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column align="center" label="序号" width="60" type="index" fixed="left"></el-table-column>
      <el-table-column align="center" label="学校名称" prop="primaryName"></el-table-column>
      <el-table-column align="center" label="直升中学" prop="juniorName"></el-table-column>

      <el-table-column align="center" label="操作" width="300px"  v-if="prefixDeptCode !== '130534' || prefixDeptCode !== '130526'">
        <template slot-scope="{ row }">
          <el-link icon="el-icon-edit" type="warning" :underline="false" style="margin-right: 10px"
            @click="edit(row)">编辑</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber" layout="total, prev, pager, next, sizes" :page-sizes="$pageSizes"
        :total="total">
      </el-pagination>
    </div>
    <!-- 新增，编辑 -->
    <el-dialog :title="dialogTitle" :visible.sync="modal.addOrEdit" center width="1000px" :close-on-click-modal="false">
      <!-- 假设我们选择一个空对象作为表单模型 -->
      <el-form :model="formModel" ref="editForm" label-width="180px">
        <el-table ref="editTable" :data="editForm" border stripe @selection-change="handleSelectionChange">
          <el-table-column align="center" type="selection" prop="juniorId"></el-table-column>
          <el-table-column align="center" label="序号" width="50" type="index" fixed="left"></el-table-column>
          <el-table-column align="center" label="学校名称" prop="juniorName"></el-table-column>
        </el-table>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)">取消</el-button>
        <el-button size="small" type="primary" @click="confirmUpdate">确定</el-button>
      </div>
    </el-dialog>

    <!-- 学校批量导入 -->
    <el-dialog :title="dialogTitle" :visible.sync="modal.import" center width="800px" :close-on-click-modal="false">
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button type="info" @click="downloadTemplateFile" icon="el-icon-download" size="small">下载模板</el-button>
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload ref="upload" accept=".xlsx,.xls" action="" :file-list="fileList" :auto-upload="false" :limit="1"
            :on-remove="onRemove" :on-exceed="onExceed" :on-change="onChange">
            <el-button size="small" type="primary" icon="el-icon-folder-opened">选择文件</el-button>
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)">取消</el-button>
        <el-button size="small" type="primary" @click="uploadSubmit" :disabled="fileList.length == 0">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import { getSchools, getJuniorList,updateSchool,importSchoolRang } from "@/api/schoolRelation";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      editForm: [],
      // 新增一个对象作为表单模型
      formModel: {}, 
      dialogTitle: "新增学校",
      currentPrimaryId: null,
      modal: {
        addOrEdit: false,
        import: false,
      },
      fileList: [],
      errorMessages: [],
      enrollTpList: [],
      // 新增变量用于存储选中的行数据
      multipleSelection: []
    };
  },
  methods: {
    // 列表
    getTableData() {
      this.search.type = 1;
      getSchools(this.search).then((data) => (this.tableData = data));
    },
    // 编辑
    async edit(row) {
      this.dialogTitle = "编辑学校";
      this.switchModal("addOrEdit", true);
      // 保存当前编辑的小学ID
      this.currentPrimaryId = row.primaryId;

      await getJuniorList({}).then((res) => {
        this.editForm = res;
        const secondArray = [];
    this.tableData.records.forEach(item => {
      if (item.primaryId === this.currentPrimaryId) {
        secondArray.push(...item.juniorName.split(','));
      }
    });
    console.log(secondArray)
       
        this.editForm.forEach(editRow => {
          if (secondArray.includes(editRow.juniorName)) {
            this.$nextTick(() => {
              this.$refs.editTable.toggleRowSelection(editRow, true);
            });
          }
        });
      });
    },
    // 处理表格选中状态变化的方法
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    // 确认更新
    confirmUpdate() {
      // 获取选中的中学ID数组
      const selectedJuniorIds = this.multipleSelection.map(item => item.juniorId);

      // 拼接成逗号分隔的字符串
      const juniorIdsStr = selectedJuniorIds.join(',');

      // 准备提交的数据
      const submitData = {
        primaryId: this.currentPrimaryId, // 当前编辑的小学ID
        juniorIds: juniorIdsStr    // 选中的中学ID字符串
      };
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          console.log(this.tableData.records)
          // 打印选中的表格信息
          console.log('选中的表格信息:', this.multipleSelection);
          if (this.multipleSelection) {
            updateSchool(submitData).then((res) => {
              this.switchModal("addOrEdit", false);
              this.$message.success("操作成功");
              this.getTableData();
            });
          }
        }
      });
    },
    // 学校批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "学校批量导入";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.$download(
        "/user-api/center/schoolRelation/downTemplate",
        {},
        "xls",
        "学校导入模版.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importSchoolRang(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hidden-column {
  display: none;
}
</style>