<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button
          size="small"
          icon="el-icon-plus"
          type="primary"
          @click="addSchool"
          >添加摇号学校</el-button
        >
        <el-button
          size="small"
          type="success"
          icon="el-icon-plus"
          @click="settingTime"
          >二次报名时间设置</el-button
        >
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="摇号学校"
        width="200"
        prop="lotterySchoolName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="志愿类别"
        width="80"
        prop="volunteerType"
      >
        <template slot-scope="scope">
          {{ ["", "第一志愿", "第二志愿"][scope.row.volunteerType] }}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="空余学位数"
          width="100"
          prop="settingNum"
      ></el-table-column>
      <el-table-column
        align="center"
        label="参与摇号学校"
        prop="participatingSchools"
      >
        <template slot-scope="{ row }">
          {{ row.participatingSchools.join('、') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300px">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            style="margin-right: 10px"
            @click="edit(row)"
            >编辑</el-link
          >
          <el-link
              icon="el-icon-s-tools"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              @click="lotteryset(row)"
          >各学校摇号指标数</el-link
          >
          <el-link
              icon="el-icon-delete"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              @click="del(row)"
          >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新增，编辑 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="1000px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm"
        ref="addForm"
        label-width="180px"
        :rules="rules"
      >
        <el-form-item prop="lotterySchoolId" label="摇号学校">
          <el-select v-model="addForm.lotterySchoolId"  clearable style="width: 400px" :disabled=" dialogTitle === '编辑摇号学校'">
            <el-option v-for="school in schoolList" :key="school.id" :value="school.id" :label="school.deptName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="volunteerType" label="志愿类别">
          <el-select
            size="small"
            v-model="addForm.volunteerType"
            style="width: 400px"
            :disabled=" dialogTitle === '编辑摇号学校'"
          >
            <el-option label="第一志愿" value="1"></el-option>
            <el-option label="第二志愿" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="settingNum" label="空余学位数">
          <el-input
            size="small"
            v-model.number.trim="addForm.settingNum"
            placeholder="请输入空余学位数"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="participatingSchools" label="参与摇号学校">
          <el-select v-model="addForm.participatingSchools" multiple clearable style="width: 400px">
            <el-option v-for="school in schoolList" :key="school.id" :value="school.id" :label="school.deptName"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate" v-if="dialogTitle === '添加摇号学校'"
          >确定</el-button
        >
        <el-button size="small" type="primary" @click="updateLotterySchool" v-else-if="dialogTitle === '编辑摇号学校'"
        >确定</el-button
        >
      </div>
    </el-dialog>


    <!-- 学校批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 账号批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.importUser"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFileUser"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileListUser"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemoveUser"
            :on-exceed="onExceedUser"
            :on-change="onChangeUser"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('importUser', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmitUser"
          :disabled="fileListUser.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 摇号指标配置弹窗 -->
    <el-dialog
        :title="dialogTitle"
        :visible.sync="modal.lotterySet"
        center
        width="800px"
        :close-on-click-modal="false"
    >
      <el-table :data="lotterySetData" border stripe>
        <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
        <el-table-column align="center" label="参与摇号学校" prop="schoolName"></el-table-column>
        <el-table-column align="center" label="分配摇号指标数" prop="quota">
          <template slot-scope="scope">
            <el-input
                v-model.number="scope.row.quota"
                placeholder="请输入分配摇号指标数"
                @input="validateQuota(scope.row)"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-center">
        <el-button size="small" @click="modal.lotterySet = false">取消</el-button>
        <el-button size="small" type="primary" @click="saveLotterySet">保存</el-button>
      </div>
    </el-dialog>

    <!--涉县设置每个类型报名时间-->
    <el-dialog
        :title="dialogTitle"
        :visible.sync="modal.settingTime"
        center
        width="800px"
        :close-on-click-modal="false"
    >
      <el-form :model="settingTimeForm" label-width="180px"  style="margin: auto;width: 500px">
        <el-form-item prop="beginTime" label="开始时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.beginTimeA"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmStart(settingTimeForm)"
              placeholder="请选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="结束时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.endTimeA"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmEnd(settingTimeForm)"
              placeholder="请选择结束时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('settingTime', false)"
        >取消</el-button
        >
        <el-button
            size="small"
            type="primary"
            @click="settingTimeClick"
        >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  schoolList,
  uptSchool,
  addSchool,
  schoolDetail,
  delSchool,
  enableSchool,
  disableSchool,
  importSchool,
  getTimeDetail,
  saveSchoolTime,
  enrollTpBySchool,
  enrollTpBySchoolAdd,
  getTownList,
  lotterySetSchool,
  lotterySchoolList,
  lotterySchoolDetail,
  lotterySetSchoolDetail,
  lotteryUpdateSchool,
  lotterySetSchoolDetailSave, updateTimePrivate, getTimePrivate, delLotterySetSchool
} from "@/api/setting";
import Editor from "wangeditor";
import { importUser } from "@/api/user";
import { pref } from "@/utils/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      schoolList: [] ,// 用于存储学校列表
      isFive: this.$store.getters.userInfo.isFive,
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      search: {
        keywords: "",
        town: "",
        nature: "",
        period: "3",
        status: "",
        type: '',
        level: 3,
        pageSize:100,
      },
      addForm: {
        id: "",
        volunteerType: "",
        settingNum: "",// 总指标数
        lotterySchoolId:"", // 用于存储学校列表
        participatingSchools: [],// 参与摇号学校ID数组
      },
      settingTimeForm:{
        beginTimeA:'',
        endTimeA:'',
      },
      rules: {
        participatingSchools:[
          {
            required: true,
            message: "请选择学校",
            trigger: "change",
          },
        ],
        volunteerType:[
          {
            required: true,
            message: "请选择志愿类别",
            trigger: "change",
          },
        ],
        lotterySchoolId:[
          {
            required: true,
            message: "请选择学校",
            trigger: "change",
          },
        ],
        settingNum: [
          {
            required: true,
            message: "请输入空余学位数",
            trigger: "blur",
          },
          {
            type: "number",
            message: "学位数必须为数字值",
          },
        ],
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
        import: false,
        importUser: false,
        settingTime:false,
        lotterySet: false // 新增摇号指标配置弹窗状态
      },
      lotterySetData: [], // 摇号指标配置数据
      fileList: [],
      errorMessages: [],
      editor: null,
      fileListUser: [],
      exportLoading: false,
      townList: [],
			enrollTpList: [],
    };
  },
  computed: {
    switchEnrollType() {
      return (val) => {
        // return (val == "1" ? "启用" : "禁用");
        let array = [];
        let str = '';
        if(val!=null){
          if(val.includes("A")) {
            array.push("房户一致")
          }
          if(val.includes("B")) {
            array.push("户口")
          }
          if(val.includes("C")) {
            array.push("房产")
          }
          if(val.includes("D")) {
            array.push("随迁子女")
          }
          if(val.includes("E")) {
            array.push("优抚对象")
          }
          if(val.includes("F")) {
            array.push("本县经商")
          }
          if(val.includes("G")) {
            array.push("本县务工")
          }
          if(val.includes("H")) {
            array.push("其他")
          }
          if(val.includes("I")) {
            array.push("毕业小学学籍")
          }
          str = array.join(',')
        }

        return str
      }
    }
  },
	watch: {
		// 性质改变后，触发查询招生类别
		'addForm.nature'(newV, oldV) {
			this.addForm.enrollTypeList = []
			this.enrollTpList = []
			// 改变
			if (newV && newV != oldV) {
				// 检查学段
				if (this.addForm.period && this.addForm.type) {
					this.getEnrollTp4AddSchool()
				}
			}
		},
		// 学段改变后，也触发查询招生类别
		'addForm.period'(newV, oldV) {
			this.addForm.enrollTypeList = []
			this.enrollTpList = []
			// 改变
			if (newV && newV != oldV) {
				// 检查学段
				if (this.addForm.nature && this.addForm.type) {
					this.getEnrollTp4AddSchool()
				}
			}
		},
    // 学校类型改变后，也触发查询招生类别
    'addForm.type'(newV, oldV) {
      this.addForm.enrollTypeList = []
      this.enrollTpList = []
      // 改变
      if (newV && newV != oldV) {
        // 检查学段
        if (this.addForm.nature && this.addForm.period) {
          this.getEnrollTp4AddSchool()
        }
      }
    }
	},
  created() {
    // console.log(this.$store.getters.userInfo.isFive, "this.$store");
     sessionStorage.removeItem("schoolDetail");
    if (this.prefixDeptCode==='130531') {
      this.queryTownList();
    }
    this.getSchoolList(); // 组件创建时获取学校列表
  },
  destroyed() {
    if (this.editor) {
      this.editor.destroy();
    }
  },
  methods: {
    getSchoolIdByName(schoolName) {
      const school = this.schoolList.find(school => school.deptName === schoolName);
      return school ? school.id : null;
    },
    validateQuota(row) {
      if (row.quota < 0) {
        this.$message.error('每个学校的分配摇号指标数必须为大于0的数字');
        row.quota = ''; // 清空无效输入
      }
    },
    async saveLotterySet() {
      try {
        // 计算各学校分配的摇号指标数总和
        const totalQuota = this.lotterySetData.reduce((sum, item) => sum + (item.quota || 0), 0);

        // 校验总和是否等于 settingNum
        if (totalQuota !== parseInt(this.currentSettingNum)) {
          //this.$message.error(`各学校分配的摇号指标数总和（${totalQuota}）必须等于总指标数（${this.currentSettingNum}）`);
          this.$message.error('分配摇号指标数与空余学位数不一致,请重新分配');
          return;
        }
        // 构建请求数据
        const requestData = {
          id:this.currentLotteryId,
          lotterySchoolId: this.currentLotterySchoolId, // 假设 row.id 是摇号学校的唯一标识
          participatingSchools: this.lotterySetData.map(item => ({
            lotteryPlanName: item.schoolName, // 需要根据 schoolName 获取对应的 schoolId
            planNum: item.quota
          }))
        };
        console.log("requestData:", requestData)
        // 调用 API 保存数据
        await lotterySetSchoolDetailSave(requestData, this.prefixDeptCode);
        this.$message.success('保存成功');
          this.modal.lotterySet = false;
          // 刷新列表数据
          this.getTableData();
      } catch (error) {
        console.error('保存摇号指标配置失败:', error);
        this.$message.error('保存失败，请检查网络或稍后重试');
      }
    },

    //
    async lotteryset(row) {
      this.dialogTitle = "摇号指标配置";
      this.modal.lotterySet = true;
      console.log("row:", row)
      // 存储摇号学校的唯一标识
      this.currentLotterySchoolId = row.lotterySchoolId;
      this.currentLotteryId = row.id;
      this.currentSettingNum = row.settingNum;
      // 获取摇号指标配置数据
      const res = await lotterySetSchoolDetail({key:row.id}, this.prefixDeptCode);
      // 初始化 lotterySetData
      this.lotterySetData = (res.schoolIndicators || []).map(item => ({
        schoolName: item.lotteryPlanName,
        quota: item.planNum
      }));
      console.log("摇号指标配置数据:", this.lotterySetData);
    },

    // 获取学校列表
    getSchoolList() {
      schoolList(this.search).then(res => {
        this.schoolList = res.records; // 假设API返回的结果中有records字段包含数据
      }).catch(error => {
        console.error('获取学校列表失败:', error);
      });
    },
    handleChange(){
      this.search.town = ""
    },
    queryTownList(){
      getTownList({
        key: this.prefixDeptCode
      }).then(res => {
        this.townList = res
      });
    },
    settingTimeClick(){
     //  saveSchoolTime(this.settingTimeForm,this.prefixDeptCode).then(res=>{
     //   if(res){
     //     this.switchModal("settingTime", false);
     //     this.$message.success('设置时间成功')
     //   }
     // })
      updateTimePrivate(
          {
            beginTime: this.settingTimeForm.beginTimeA,
            endTime: this.settingTimeForm.endTimeA,
            id: 22,
          },
          this.prefixDeptCode
      ).then((res) => {
        this.$message.success("操作成功");
      });
    },
    settingTime(row){
      this.dialogTitle = "二次报名时间设置";
      this.modal.settingTime=true
      getTimePrivate({key:22},this.prefixDeptCode).then(res=>{
            this.settingTimeForm.beginTimeA=res.beginTime,
            this.settingTimeForm.endTimeA=res.endTime
      })
    },
    confirmStart(item,index){
      let astartTime = new Date(item.beginTimeA).getTime()
      let endTimeA = new Date(item.endTimeA).getTime()
      if(item.beginTimeA&&item.endTimeA){

        if(astartTime>endTimeA){
          this.$message.error('开始时间不能大于结束时间')
            this.settingTimeForm.beginTimeA=''
        }
      }
    },
    confirmEnd(item,index){
      let astartTime = new Date(item.beginTimeA).getTime()
      let endTimeA = new Date(item.endTimeA).getTime()
      if(item.beginTimeA&&item.endTimeA){
        if(astartTime>endTimeA){
          this.$message.error('结束时间不能小于开始时间')
          this.settingTimeForm.endTimeA=''
        }
      }
    },
    // 列表
    getTableData() {
      lotterySchoolList(this.search,this.prefixDeptCode).then((data) => (this.tableData = data));
    },
		// 新增学校时，根据已选的性质和学段，查询招生类别
		getEnrollTp4AddSchool() {
			enrollTpBySchoolAdd({
				period: this.addForm.period - 0,
				nature: this.addForm.nature - 0,
        type: this.addForm.type
			}).then(res => {
				this.enrollTpList = res
			})
		},
    // 添加
    addSchool() {
      this.dialogTitle = "添加摇号学校";
      this.switchModal("addOrEdit", true);
      this.$nextTick(async () => {
        this.$refs.addForm.resetFields();
        this.addForm = {
          volunteerType:"",
          settingNum: "",
          lotterySchoolId: "", //
          participatingSchools: [] // 初始化为空数组
        };
        if (this.editor == null) {
          this.editorCreate();
        } else {
          this.editor.destroy();
          this.editorCreate();
        }
      });
    },
    // 编辑
    async edit(row) {
      console.log("row:",row)
      this.dialogTitle = "编辑摇号学校";
      this.switchModal("addOrEdit", true);
      const detail = await lotterySchoolDetail({key:row.id}, this.prefixDeptCode);
      console.log("detail:", detail);

      // 将 volunteerType 转换为对应的描述
      let volunteerTypeDesc = '';
      switch (detail.volunteerType) {
        case 1:
          volunteerTypeDesc = '第一志愿';
          break;
        case 2:
          volunteerTypeDesc = '第二志愿';
          break;
        default:
          volunteerTypeDesc = '';
      }

      // 初始化 addForm
      this.addForm = {
        id:detail.id,
        lotterySchoolId: detail.lotterySchoolId,
        volunteerType: detail.volunteerType.toString(), // 确保与表单的 value 类型一致（字符串）
        settingNum: detail.settingNum,
        participatingSchools: [] // 初始化为空数组
      };

      // 将参与摇号学校的名称数组转换为 ID 数组
      if (detail.participatingSchools && detail.participatingSchools.length > 0) {
        this.addForm.participatingSchools = this.schoolList
            .filter(school => detail.participatingSchools.includes(school.deptName))
            .map(school => school.id);
      }
      // 打印 addForm 确认数据正确性
      console.log("addForm after edit:", this.addForm);
    },
    // 账号管理
    acctManage(row) {
       sessionStorage.setItem("schoolDetail", JSON.stringify(row));
      this.$router.push({
        path: "/setting/schoolManageAcct",
        query: {
          id: row.id,
          period: row.period,
          deptName: row.deptName,
        },
      });
    },
    // 启用
    enable(row) {
      enableSchool({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getTableData();
      });
    },
    // 禁用
    disable(row) {
      disableSchool({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getTableData();
      });
    },
    // 删除
    del(row) {
      this.$confirm(`确认删除摇号学校${row.lotterySchoolName}？`, "删除学校", {
        type: "error",
      }).then(() => {
        delLotterySetSchool({key:row.id}, this.prefixDeptCode).then((res) => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 确认更新
    confirmUpdate() {
      console.log("11",this.addForm);
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          // 确保 participatingSchools 和 lotterySchoolId 已正确填充
          if (!this.addForm.lotterySchoolId.length || !this.addForm.participatingSchools.length) {
            this.$message.error("请选择摇号学校和参与摇号学校");
            return;
          }
          lotterySetSchool(this.addForm,this.prefixDeptCode).then((res) => {
              this.switchModal("addOrEdit", false);
              this.$message.success("操作成功");
              this.getTableData();
            });
          }

      });
    },
    async updateLotterySchool() {
      try {
        await lotteryUpdateSchool(this.addForm, this.prefixDeptCode);
          this.switchModal("addOrEdit", false);
          this.$message.success("更新成功");
          this.getTableData();
      } catch (error) {
        console.error('更新摇号学校失败:', error);
        this.$message.error('更新失败，请检查网络或稍后重试');
      }
    },
    // 学校批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "学校批量导入";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.$download(
        "/user-api/center/dept/downTemplate",
        {},
        "xls",
        "学校导入模版.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importSchool(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
    // 创建富文本
    editorCreate() {
      var _this = this;
      this.editor = new Editor(this.$refs.editor);
      this.editor.config.onchange = (html) => this.editorCatchData(html);
      this.editor.config.height = 400;
      this.editor.config.showLinkImg = false;
      this.editor.config.debug = true;
      this.editor.config.uploadImgServer = `${this.baseApi}/user-api/user/file/uploadImg`;
      this.editor.config.uploadFileName = "file";
      this.editor.config.uploadImgMaxSize = 5 * 1024 * 1024;
      this.editor.config.uploadImgHeaders = {
        Authorization: `${this.$store.getters.token}`,
        "client-id": "admin",
        state: this.$store.getters.securityKey,
      };
      this.editor.config.uploadImgHooks = {
        fail: function (xhr, editor, result) {},
        success: function (xhr, editor, result) {},
        customInsert: function (insertImgFn, result) {
          insertImgFn(_this.baseApi + "/user-api" + result.data);
        },
      };
      this.editor.config.menus = [
        // 菜单配置
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        "emoticon", // 表情
        "image", // 插入图片
        "table", // 表格
        "code", // 插入代码
        "undo", // 撤销
        "redo", // 重复
      ];
      // 创建富文本实例
      this.editor.create();
    },
    // 更新content
    editorCatchData(content) {
      this.addForm.content = content;
    },

    // 账号批量导入
    importBatchUser() {
      this.switchModal("importUser", true);
      this.dialogTitle = "账号批量导入";
      this.fileListUser = [];
      this.errorMessages = [];
    },
    // 账号批量导入下载模版
    downloadTemplateFileUser() {
      this.$download(
        "/user-api/center/user/downTemplate",
        {},
        "xls",
        "账号批量导入模版.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    onRemoveUser(file, fileList) {
      this.fileListUser = fileList;
      this.errorMessages = [];
    },
    onExceedUser(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChangeUser(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileListUser.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileListUser.push(file.raw);
      }
    },
    // 账号批量导入提交
    uploadSubmitUser() {
      let formData = new FormData();
      formData.append("file", this.fileListUser[0]);
      importUser(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("importUser", false);
        }
      });
    },
    // 账号导出
    exportListUser() {
      this.exportLoading = true;
      let params = this.search;
      this.$download(
        "/user-api/center/user/exportUserList",
        params,
        "xls",
        "导出账号列表.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
