<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button icon="el-icon-back" size="small" @click="back"
          >返回</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="addAcct"
          icon="el-icon-plus"
          v-if="((nature !== '1'&& type!=='2')&&prefixDeptCode === '130582')|| prefixDeptCode === '130532'|| prefixDeptCode === '130522'"
          >添加</el-button
        >
      </div>
      <div class="school-title">
        {{ schoolNm }}
      </div>
    </div>
    <div v-if="(nature === '1'||type==='2')&& prefixDeptCode === '130582'" style= "text-align: left;">
      <h4>暂无使用权限</h4>
    </div>
    <el-table :data="tableData" border stripe v-if="((nature !== '1'&& type!=='2')&&prefixDeptCode === '130582')|| prefixDeptCode === '130532'|| prefixDeptCode === '130522'">
      <el-table-column
        align="center"
        label="序号"
        width="60"
        fixed="left"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="账号"
        prop="username"
      ></el-table-column>
      <el-table-column
        prop="defaultPasswordView"
        label="默认密码"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{ row.defaultPasswordView }}</span>
          <el-link
            @click="changePasswordView(row)"
            :underline="false"
            :icon="row.showPasswordView ? 'el-icon-view' : ''"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="审核员姓名"
        prop="nickname"
      ></el-table-column>
      <el-table-column
        align="center"
        label="联系电话"
        width="150"
        prop="mobile"
      ></el-table-column>
      <el-table-column
        align="center"
        label="微信绑定昵称"
        width="150"
        prop="wxname"
      ></el-table-column>
      <el-table-column
        align="center"
        label="账号状态"
        width="100"
        prop="status"
      >
        <template slot-scope="{ row }">
          <el-link type="info" :underline="false" v-show="row.status == 0"
            >禁用</el-link
          >
          <el-link type="success" :underline="false" v-show="row.status == 1"
            >启用</el-link
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-refresh-left"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="resetPwd(row)"
            >重置密码</el-link
          >
          <el-link
            icon="el-icon-check"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            @click="enable(row)"
            v-if="row.status == 0"
            >启用</el-link
          >
          <el-link
            icon="el-icon-close"
            type="info"
            :underline="false"
            style="margin-right: 10px"
            @click="disable(row)"
            v-else-if="row.status == 1"
            >禁用</el-link
          >
          <el-link
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            style="margin-right: 10px"
            @click="edit(row)"
            >编辑</el-link
          >
          <el-link
            icon="el-icon-delete"
            type="danger"
            :underline="false"
            @click="del(row)"
            >删除</el-link
          >
          <el-link
              v-if="prefixDeptCode == '130532'"
              icon="el-icon-delete"
              type="danger"
              :underline="false"
              @click="delAuditor(row)"
          >删除审核任务</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新增，编辑 -->
    <el-dialog
      :title="addTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm"
        ref="addOrEditForm"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item prop="username" label="账号">
          <el-input
            size="small"
            v-model.trim="addForm.username"
            placeholder="请输入账号"
          ></el-input>
        </el-form-item>
        <el-form-item prop="nickname" label="审核员姓名">
          <el-input
            size="small"
            v-model.trim="addForm.nickname"
            placeholder="请输入审核员姓名"
          ></el-input>
        </el-form-item>
        <el-form-item prop="mobile" label="联系电话">
          <el-input
            size="small"
            v-model.trim="addForm.mobile"
            placeholder="请输入联系电话"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ModalMixin from "@/mixins/ModalMixin";
import {
  getSchoolAuditorList,
  create,
  update,
  enableUser,
  disableUser,
  resetPwd,
  delUser, delAuditor,
} from "@/api/user";
export default {
  mixins: [ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      nature:this.$store.getters.userInfo.deptInfo.nature,
      type:this.$store.getters.userInfo.deptInfo.type,
      schoolNm: "",
      search: {
        deptId: "",
        ancestors: "",
        roleCode: "",
        status: "",
        keywords: "",
        pageNumber: 1,
        pageSize: 10,
      },
      tableData: [],
      total:0,
      addForm: {
        id: "",
        username: "",
        nickname: "",
        mobile: "",
        defaultPassword: "",
        roleId: 17,
        deptId: "",
        schoolIds: [],
        schoolType: "",
      },
      addTp: "add",
      addTitle: "新增账号",
      modal: {
        addOrEdit: false,
      },
      rules: {
        username: [
          {
            required: true,
            trigger: "blur",
            message: "请输入账号",
          },
        ],
        nickname: [
          {
            required: true,
            trigger: "blur",
            message: "请输入姓名",
          },
        ],
        mobile: [
          {
            required: true,
            trigger: "blur",
            message: "请输入联系电话",
          },
        ],
      },
    };
  },
  computed: {
    // total() {
    //   return this.tableData && this.tableData.total
    //     ? Number(this.tableData.total)
    //     : 0;
    // },
  },
  created() {

    this.addForm.deptId = this.$store.getters.deptId;
    this.addForm.schoolType = this.$store.getters.period;
    this.schoolNm = this.$store.getters.userInfo.deptName;
    this.getList();
  },
  methods: {
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getList()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getList()
    },
    // 返
    back() {
      this.$router.go(-1);
    },
    // 添加
    addAcct() {
      this.addTp = "add";
      this.addTitle = "新增账号";
      this.addForm.id = "";
      this.addForm.username = "";
      this.addForm.nickname = "";
      this.addForm.mobile = "";
      this.switchModal("addOrEdit", true);
    },
    // 列表
    getList() {
      this.tableLoading = true;
      getSchoolAuditorList(this.search)
        .then((data) => {
          for (let i = 0; i < data.records.length; i++) {
            const item = data.records[i];
            item.defaultPasswordView = "********";
            item.showPasswordView = true;
          }
          this.tableData = data.records;
          this.total=Number(data.total)
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    changePasswordView(row) {
      if (row.showPasswordView) {
        row.defaultPasswordView = row.defaultPassword;
      } else {
        row.defaultPasswordView = "******";
      }
      row.showPasswordView = !row.showPasswordView;
    },
    // 通过
    enable(row) {
      enableUser({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
      });
    },
    // 不通过
    disable(row) {
      disableUser({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
      });
    },
    // 编辑
    edit(row) {
      this.addTp = "edit";
      this.addTitle = "编辑账号";
      this.addForm.id = row.id;
      this.addForm.username = row.username;
      this.addForm.nickname = row.nickname;
      this.addForm.mobile = row.mobile;
      this.addForm.defaultPassword = row.defaultPassword;
      this.switchModal("addOrEdit", true);
    },
    // 删除
    del(row) {
      delUser({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
      });
    },
    // 删除
    // 删除审核任务
    delAuditor(row) {
      this.$confirm('删除该审核员的任务分配', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delAuditor({
          key: row.id,
        }).then((res) => {
          this.$message.success("操作成功");
          this.getList();
        }).catch(() => {
          this.$message.info("已取消删除");
        });
      }).catch(() => {
        this.$message.info("已取消删除");
      });
    }
    ,
    // 重置密码
    resetPwd(row) {
      resetPwd({
        key: row.id,
      }).then((res) => {
        this.$message.success("密码已重置");
        this.getList();
      });
    },
    // 确认更新
    confirmUpdate() {
      this.$refs["addOrEditForm"].validate((valid) => {
        if (valid) {
          if (this.addForm.id) {
            update(this.addForm).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getList();
            });
          } else {
            console.log('添加参数',JSON.stringify(this.addForm,null,2))
            create(this.addForm).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getList();
            });
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .school-title {
    font-size: 18px;
    position: absolute;
    left: 50%;
  }
}
</style>
