<!-- 学籍管理 -->
<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="importBatch"
          >批量导入</el-button
        >
        <el-button
            v-if="prefixDeptCode!=='130522'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            @click="add"
        >添加</el-button
        >
        <el-button size="small" type="primary" icon="el-icon-download" @click="download" v-if="prefixDeptCode!=='130522'">导出</el-button>
      </div>
      <div class="sd-search">
        <el-input
          v-model.trim="search.keywords"
          placeholder="请输入姓名/身份证号/学籍号"
          size="small"
          clearable
          style="width: 240px"
        ></el-input>
        <el-select
          v-model="search.schoolId"
          placeholder="筛选学校-可模糊搜索"
          size="small"
          clearable
        >
          <el-option
            v-for="item in schoolList"
            :key="item.id"
            :label="item.deptName"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-button
          type="primary"
          class="sd-btn-search"
          icon="el-icon-search"
          @click="searchSubmit"
          size="small"
        />
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        fixed="left"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
      ></el-table-column>
      <el-table-column align="center" label="性别" prop="sex">
        <template slot-scope="{ row }">
          <span>{{ switchGender(row.gender) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="学生身份证号"
        prop="idCard"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学籍号"
        prop="studentCode"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        label="毕业学校"
        prop="schoolName"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
          v-if="prefixDeptCode==='130522'"
          align="center"
          label="毕业学校所属地区"
          prop="town"
          show-overflow-tooltip
      ></el-table-column>
      <el-table-column
          align="center"
          label="在校在籍"
          v-if="prefixDeptCode==='130526'"
      >
        <template slot-scope="{ row }">
          <span>{{ row.registeredAtSchool == '1' ? '是' : row.registeredAtSchool == '0' ? '否' : '' }}</span>
        </template>
<!--        回显0否1是-->
      </el-table-column>
      <el-table-column align="center" label="操作" >
        <template slot-scope="{row}">
          <el-link type="primary" icon="el-icon-edit" @click="edit(row)">编辑</el-link>
          <el-link type="danger" icon="el-icon-delete" @click="del(row)">删除</el-link>
        </template>
      </el-table-column>

    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
        :title="title"
        :visible.sync="addShow"
        width="800px"
        center
    >
      <el-form :model="form" label-width="300px">
        <el-form-item
            label="姓名"
        >
          <el-input
              clearable
              style="width: 220px"
              v-model="form.studentName"
             placeholder="请输入姓名"
          ></el-input>
        </el-form-item>
        <el-form-item
            label="性别"
        >
          <el-select
              clearable
              v-model="form.gender"
              placeholder="请选择性别"
          >
            <el-option
                label="男"
                value="1"
            ></el-option>
            <el-option
                label="女"
                value="2"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
            label="身份证号"
        >
          <el-input
              clearable
              style="width: 220px"
              v-model="form.idCard"
              placeholder="请输入身份证号"
          ></el-input>
        </el-form-item>
        <el-form-item
            label="学籍号"
        >
        <el-input  clearable style="width: 220px" v-model="form.studentCode" placeholder="请输入学籍号"></el-input>
        </el-form-item>
        <el-form-item label="毕业小学">
          <el-select
              clearable
              v-model="form.schoolName"
              placeholder="请选择毕业小学 "
              @change="change"
          >
            <el-option
                v-for="item in schoolList"
                :key="item.deptName"
                :label="item.deptName"
                :value="item.deptName"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="在校在籍" v-if="prefixDeptCode==='130526'">
          <el-select
              clearable
              v-model="form.registeredAtSchool"
              placeholder="请选择"
          >
            <el-option
                v-for="item in registeredAtSchoolList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="addShow = false">取 消</el-button>
        <el-button
            type="primary"
            @click="addOrEdit"
        >确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import { getList, importStudentCode,del,addOrEdit } from "@/api/setting";
import { getDepts } from "@/api/common";
export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      addShow:false,
      editShow:false,
      title:'添加学籍',
      form:{},
      optionsList:[],
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: null,
        schoolId: "",
      },
      schoolList: [],
      registeredAtSchoolList:[{value:0,label:'否'},{value:1,label:'是'}],
      dialogTitle: "批量导入",
      modal: {
        import: false,
      },
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
    };
  },
  created() {
    this.getDepts();
  },
  computed: {
    switchGender() {
      return (val) => (val == "1" ? "男" : val == "2" ? "女" : "");
    },
  },
  methods: {
    download(){
      let params = this.search;
      this.$download(
          "/user-api/center/studentCode/exportList",
          params,
          "xls",
          "导出学籍列表.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
    },
    change(e){
      this.schoolList.forEach(element=>{
        if(element.deptName==e){
          this.form.schoolId=element.id
        }
      })
    },
    add(){
      this.addShow=true
      this.title='添加学生学籍 '
      this.form={}
    },
    edit(row){
     this.addShow=true
      this.title='编辑学生学籍'
      this.form=row
    },
    addOrEdit(){
       addOrEdit(this.form).then(res=>{
         if(res){
           this.addShow=false
           this.$message.success('操作成功')
           this.getTableData()
         }
       })
    },
    del(row){
      this.$confirm('此操作将删除此学生学籍, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del({key:row.id}).then(res=>{
          if(res) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    // 列表
    getTableData() {
      this.tableLoading = true;
      getList(this.search).then((res) => {
        this.tableData = res;
        this.tableLoading = false;
      });
    },
    // 获取区县下的学校列表
    getDepts() {
      let params = {
        level: 3,
        period: "2",
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.downloadLoading = true;
      this.$download(
        "/user-api/center/studentCode/downTemplate",
        {},
        "xls",
        "毕业小学学籍.xls"
      ).then((res) => {
        this.$message
          .success("下载成功")
          .finally(() => (this.downloadLoading = false));
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importStudentCode(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
  },
};
</script>

<style scoped>
.warning-desc-text {
  color: #e6a23c;
}
.error-desc-text {
  color: #f56c6c;
}
</style>
