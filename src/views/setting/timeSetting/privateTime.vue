<template>
  <div>
    <ul class="setting-list">
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>民办小学报名时间</span>
          </div>
          <el-form :model="primary" label-width="100px">
            <el-form-item prop="beginTime" label="开始时间">
              <el-date-picker
                  size="small"
                  v-model="primary.beginTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择开始时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="confirmStart(primary,1)"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="endTime" label="结束时间">
              <el-date-picker
                  size="small"
                  v-model="primary.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="confirmEnd(primary,1)"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                :disabled="!primary.beginTime || !primary.endTime"
                @click="confirmSet(primary)"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>民办初中报名时间</span>
          </div>
          <el-form :model="junior" label-width="100px">
            <el-form-item prop="beginTime" label="开始时间">
              <el-date-picker
                  size="small"
                  v-model="junior.beginTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择开始时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="confirmStart(junior,2)"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="endTime" label="结束时间">
              <el-date-picker
                  size="small"
                  v-model="junior.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="confirmEnd(junior,2)"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                :disabled="!junior.beginTime || !junior.endTime"
                @click="confirmSet(junior)"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
    </ul>
  </div>
</template>

<script>
import { getTimePrivate, updateTimePrivate } from "@/api/setting";
export default {
  data() {
    return {
      primary:{},
      junior:{},
      prefixDeptCode: this.$store.getters.deptCode,
      dataList: [],
    };
  },
  created() {
    this.getSetting(1);
    this.getSetting(2);
  },
  methods: {
    // 获取
    getSetting(type) {
      if(type==1){
        getTimePrivate({ key: type }, this.prefixDeptCode).then((res) => {
          this.primary = res;
        });
      }else {
        getTimePrivate({ key: type }, this.prefixDeptCode).then((res) => {
          this.junior = res;
        });
      }

    },
    // 确认开始时间
    confirmStart(row,key) {
      if (row.beginTime && row.endTime) {
        let start = new Date(row.beginTime).getTime();
        let end = new Date(row.endTime).getTime();
        if (end <= start) {
          this.$message.warning("结束时间不能小于等于开始时间");
          if(key==1){
            this.primary.endTime=''
          }else {
            this.junior.endTime=''
          }
        }
      }
    },
    // 确认结束时间
    confirmEnd(row,key) {
      if (row.beginTime && row.endTime) {
        let start = new Date(row.beginTime).getTime();
        let end = new Date(row.endTime).getTime();
        if (end <= start) {
          this.$message.warning("结束时间不能小于等于开始时间");
          if(key==1){
            this.primary.endTime=''
          }else {
            this.junior.endTime=''
          }
        }
      }
    },
    // 确认设置
    confirmSet(row) {
      updateTimePrivate(
          {
            beginTime: row.beginTime,
            endTime: row.endTime,
            id: row.id,
          },
          this.prefixDeptCode
      ).then((res) => {
        this.$message.success("操作成功");
      });
    },
  },
};
</script>

<style scoped lang="scss">
.setting-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;

  & > li {
    flex: 0 0 33.3333%;
    padding: 10px 10px;
    box-sizing: border-box;
  }
}
</style>
