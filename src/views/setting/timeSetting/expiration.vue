<template>
  <div>
    <ul class="setting-list">
      <li  v-if="prefixDeptCode === '130503' || prefixDeptCode === '130502'">
        <el-card class="box-card">
          <div slot="header">
            <span>幼儿园截止修改报名时间设置</span>
          </div>
          <el-form :model="kindergartenEditForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                  size="small"
                  v-model="kindergartenEditForm.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择截止时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                @click="confirmKindergartenEditSet()"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>小学截止修改报名时间设置</span>
          </div>
          <el-form :model="primaryForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                size="small"
                v-model="primaryForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                placeholder="请选择截止时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
              type="success"
              size="small"
              @click="confirmPrimarySet()"
              >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>初中截止修改报名时间设置</span>
          </div>
          <el-form :model="juniorForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                size="small"
                v-model="juniorForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                placeholder="请选择截止时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
              type="success"
              size="small"
              @click="confirmJuniorSet()"
              >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li v-if="prefixDeptCode === '130503' || prefixDeptCode === '130502'">
        <el-card class="box-card">
          <div slot="header">
            <span>幼儿园截止添加报名时间</span>
          </div>
          <el-form :model="kindergartenAddForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                  size="small"
                  v-model="kindergartenAddForm.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择截止时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                @click="confirmKindergartenAddSet()"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>小学截止添加报名时间</span>
          </div>
          <el-form :model="primarySchoolForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                size="small"
                v-model="primarySchoolForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                placeholder="请选择截止时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
              type="success"
              size="small"
              @click="confirmPrimarySchoolSet()"
              >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>初中截止添加报名时间</span>
          </div>
          <el-form :model="juniorSchoolForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                size="small"
                v-model="juniorSchoolForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                placeholder="请选择截止时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
              type="success"
              size="small"
              @click="confirmJuniorSchoolSet()"
              >确定</el-button
            >
          </div>
        </el-card>
      </li>
    </ul>
		<ul class="setting-list" v-if="prefixDeptCode == '130435'">
			<li>
				<el-card class="box-card">
				  <div slot="header">
				    <span>二次报名时间设置</span>
				  </div>
				  <el-form :model="joinSecondQZ" label-width="100px">
				    <el-form-item prop="beginTime" label="开始时间">
				      <el-date-picker
				        size="small"
				        v-model="joinSecondQZ.beginTime"
				        type="datetime"
				        :editable="false"
				        :clearable="true"
				        placeholder="请选择开始时间"
				        value-format="yyyy-MM-dd HH:mm:ss"
				        @change="confirmSecondQZStart"
				      >
				      </el-date-picker>
				    </el-form-item>
				    <el-form-item prop="endTime" label="结束时间">
				      <el-date-picker
				        size="small"
				        v-model="joinSecondQZ.endTime"
				        type="datetime"
				        :editable="false"
				        :clearable="true"
				        placeholder="请选择结束时间"
				        value-format="yyyy-MM-dd HH:mm:ss"
				        @change="confirmSecondQZEnd"
				      >
				      </el-date-picker>
				    </el-form-item>
				  </el-form>
				  <div class="flex-end">
				    <el-button
				      type="success"
				      size="small"
							:disabled="!joinSecondQZ.beginTime || !joinSecondQZ.endTime"
				      @click="confirmSecondQZ"
				      >确定</el-button
				    >
				  </div>
				</el-card>
			</li>
		</ul>
  </div>
</template>

<script>
import { getDeadline, deadlineCreate, saveTimeSetting } from "@/api/setting";
export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      primaryForm: {
        type: '501',
        endTime: ''
      },
      juniorForm: {
        type: '601',
        endTime: ''
      },
      primarySchoolForm: {
        type: '502',
        endTime: ''
      },
      juniorSchoolForm: {
        type: '602',
        endTime: ''
      },
      kindergartenAddForm: {
        type: '702',
        endTime: ''
      },
      kindergartenEditForm: {
        type: '802',
        endTime: ''
      },
			// 曲周专用 - 二次报名时间
			joinSecondQZ: {
				type: '701',
				beginTime: '',
				endTime: ''
			}
    }
  },
  created() {
    this.getDeadline();
  },
  methods: {
    // 获取
    getDeadline() {
      getDeadline({}, this.prefixDeptCode).then((res) => {
        this.primaryForm.endTime = res.find(item => item.type == '501').endTime;
        this.juniorForm.endTime = res.find(item => item.type == '601').endTime;
        this.primarySchoolForm.endTime = res.find(item => item.type == '502').endTime;
        this.juniorSchoolForm.endTime=res.find(item => item.type == '602').endTime;
        this.kindergartenAddForm.endTime = res.find(item => item.type == '702').endTime;
        this.kindergartenEditForm.endTime=res.find(item => item.type == '802').endTime;
				if (this.prefixDeptCode == '130435') {
					this.joinSecondQZ.beginTime = res.find(item => item.type == '701').beginTime
					this.joinSecondQZ.endTime = res.find(item => item.type == '701').endTime
				}
      });
    },
    // 幼儿园截止时间修改 - 确认
    confirmKindergartenEditSet() {
      deadlineCreate(this.kindergartenEditForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline()
      });
    },
    // 幼儿园截止时间增加 - 确认
    confirmKindergartenAddSet() {
      deadlineCreate(this.kindergartenAddForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline()
      });
    },
    // 小学 - 确认
    confirmPrimarySet() {
      deadlineCreate(this.primaryForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline()
      });
    },
    // 初中 - 确认
    confirmJuniorSet() {
      deadlineCreate(this.juniorForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline()
      });
    },
    // 小学学校截止添加报名时间 - 确认
    confirmPrimarySchoolSet() {
      deadlineCreate(this.primarySchoolForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline()
      });
    },
    // 初中学校截止添加报名时间 - 确认
    confirmJuniorSchoolSet() {
      deadlineCreate(this.juniorSchoolForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline()
      });
    },
		// 曲周专用 - 二次报名开始时间
		confirmSecondQZStart() {
			if (this.joinSecondQZ.beginTime && this.joinSecondQZ.endTime) {
			  let start = new Date(this.joinSecondQZ.beginTime).getTime()
			  let end = new Date(this.joinSecondQZ.endTime).getTime()
			  if (end <= start) {
			    this.$message.warning("结束时间不能小于等于开始时间")
			    this.joinSecondQZ.beginTime = ""
			  }
			}
		},
		// 曲周专用 - 二次报名结束时间
		confirmSecondQZEnd() {
			if (this.joinSecondQZ.beginTime && this.joinSecondQZ.endTime) {
			  let start = new Date(this.joinSecondQZ.beginTime).getTime()
			  let end = new Date(this.joinSecondQZ.endTime).getTime()
			  if (end <= start) {
			    this.$message.warning("结束时间不能小于等于开始时间")
			    this.joinSecondQZ.endTime = ""
			  }
			}
		},
		// 曲周专用 - 二次报名时间确认
		confirmSecondQZ() {
			saveTimeSetting({
				beginTime: this.joinSecondQZ.beginTime,
				endTime: this.joinSecondQZ.endTime,
				type: '701',
				status: 0,
				setupId: '',
			},
				this.prefixDeptCode
			).then((res) => {
			  this.$message.success("操作成功")
			})
		}
  },
};
</script>

<style scoped lang="scss">
.setting-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;

  & > li {
    flex: 0 0 33.3333%;
    padding: 10px 10px;
    box-sizing: border-box;
  }
}
</style>
