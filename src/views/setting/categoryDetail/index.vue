<template>
  <div>
    <el-tabs v-model="curTab" type="border-card">
      <el-tab-pane v-if=" this.prefixDeptCode !== '130503' || this.prefixDeptCode !== '130502'" label="乡镇" name="county">
        <el-table
          :data="tableData.county"
          border
          stripe
          v-loading="tableLoading"
        >
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="60"
            fixed="left"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="field"
            label="学段"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="signUpSort"
            label="报名类别"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="detail"
            label="报名类别详情"
          ></el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="{ row }">
              <el-link
                @click="edit(row)"
                icon="el-icon-edit"
                type="warning"
                :underline="false"
                >编辑
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="城区" name="city">
        <el-table :data="tableData.city" border stripe v-loading="tableLoading">
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="60"
            fixed="left"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="field"
            label="学段"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="signUpSort"
            label="报名类别"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="detail"
            label="报名类别详情"
          ></el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="{ row }">
              <el-link
                @click="edit(row)"
                icon="el-icon-edit"
                type="warning"
                :underline="false"
                >编辑
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 编辑 -->
    <el-dialog
      title="编辑"
      :visible.sync="modal.addOrEdit"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        label-position="right"
        label-width="140px"
      >
        <el-form-item prop="title" label="报名类别：">
          <span>{{ categoryTitle }}</span>
        </el-form-item>
        <el-form-item prop="detail" label="报名类别详情：">
          <el-input
            type="textarea"
            :rows="8"
            v-model.trim="form.detail"
            placeholder="请输入报名类别详情,至少5个字符"
            style="width: 400px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ModalMixin from "@/mixins/ModalMixin";
import {
  signUpSortDetailSetting,
  signUpSortDetailSettingUpdate,
} from "@/api/setting";

export default {
  name: "categoryDetail",
  mixins: [ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      curTab: "county",
      tableData: {
        city: [],
        county: [],
      },
      modal: {
        addOrEdit: false,
      },
      tableLoading: false,
      form: {
        id: "",
        detail: "",
      },
      rules: {
        detail: [
          {
            required: true,
            message: "请输入类别详情",
            trigger: "blur",
          },
          {
            min: 5,
            message: "请至少输入5个以上字符",
            trigger: "blur",
          },
        ],
      },
      categoryTitle: "",
    };
  },
  created() {
    if(this.$store.getters.deptCode === "130503" || this.$store.getters.deptCode === "130502") {
      this.curTab = "city";
    }
    this.getTableDataCounty();
    this.getTableDataCity();
  },
  methods: {
    // 乡镇列表
    getTableDataCounty() {
      this.tableLoading = true;
      signUpSortDetailSetting({ key: 2 }, this.prefixDeptCode)
        .then((res) => {
          this.tableData.county = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    // 城区列表
    getTableDataCity() {
      this.tableLoading = true;
      signUpSortDetailSetting({ key: 3 }, this.prefixDeptCode)
        .then((res) => {
          this.tableData.city = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 编辑
    edit(row) {
      console.log(row);
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.id = row.setupId;
        this.form.detail = row.detail;
        this.categoryTitle =
          (this.curTab == "county" ? "乡镇" : "城区") +
          " - " +
          row.field +
          " - " +
          row.signUpSort;
      });
    },
    // 更新
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          signUpSortDetailSettingUpdate(this.form, this.prefixDeptCode).then(
            () => {
              this.switchModal("addOrEdit", false);
              this.$message.success("操作成功");
              if (this.curTab == "county") {
                this.getTableDataCounty();
              } else {
                this.getTableDataCity();
              }
            }
          );
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
