<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button type="primary" icon="el-icon-back" @click="back" size="small"
          >返回</el-button
        >
        <el-button size="small" type="primary" @click="add" icon="el-icon-plus">添加</el-button>
      </div>
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="搜索小区/村庄名称"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="flex-center school-name">
      {{ schoolName }}
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      style="width: 100%"
      v-loading="tableLoading"
    >
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="招生类别"
        prop="enrollTypeName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="区域"
        prop="rangeName"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="200px">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            @click="edit(row)"
            style="margin-right: 10px"
            >编辑</el-link
          >
          <el-link
            type="danger"
            icon="el-icon-delete"
            @click="del(row)"
            :underline="false"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 新增，编辑 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="130px">
        <el-form-item label="所属学校">
          <span>{{ schoolName }}</span>
        </el-form-item>
        <el-form-item prop="enrollType" label="招生类别">
          <el-select v-model="form.enrollType" size="small" style="width: 220px">
            <el-option
              v-for="item in schoolenrollType"
              :key="item.id"
              :label="item.enrollName"
              :value="item.enrollType"
            ></el-option>    
          </el-select>
        </el-form-item>
        <el-form-item prop="enrollTypeList" label="招生范围">
					<el-select v-model="form.enrollTypeList" multiple clearable style="width: 420px">
						<el-option v-for="tpItem in enrollTpList" :key="tpItem.id" :value="tpItem.id" :label="tpItem.rangeName"></el-option>
					</el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getSchoolEnrollRang,
  addSchoolEnrollRang,
  updateSchoolEnrollRang,
  deleteSchoolEnrollRang,
  getEnrollArea,
  getSchoolEnrollType,
} from "@/api/setting";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      enrollTpList: [], // 添加这个字段
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: "",
        // schoolType: "",
        schoolId: "",
        countyId: this.$store.getters.deptId,
        type: 2, // 1区县级别 2学校级别
      },
      countSearch: {
        parentId: this.$store.getters.deptId,
        type: 1, // 1区县级别 2学校级别
      },
      schoolenrollType: [],
      addData: {
        enrollType: "", // 招生类别
        rangeName: "", // 招生范围
      },
      form: {
        id: "",
        enrollType: "",
        schoolId: "",
        rangeId: "",
        rangeName:"",
        enrollTypeList: [],
      },
      rules: {
        rangeName: [
          { required: true, message: "请输入招生范围", trigger: "blur" },
        ],
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
      },
      schoolName: "",
    };
  },
  created() {
    this.search.schoolId = this.$route.query.schoolId;
    this.form.schoolId = this.$route.query.schoolId;
    this.schoolName = this.$route.query.schoolName;

    // 获取区域列表
    getEnrollArea(this.countSearch).then((res) => {
      this.enrollTpList = res;
    });
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      this.search.schoolId = this.$route.query.schoolId;
      getSchoolEnrollRang(this.search)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
       
    },
    // 添加
    add() {
      this.dialogTitle = "招生范围";
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.id = '';
      });
      getSchoolEnrollType(this.search).then((res) => {
          this.schoolenrollType = res;
        });
      getEnrollArea(this.countSearch).then((res) => {
        this.enrollTpList = res;
      })
    },

    // 编辑
    edit(row) {
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.id = row.id;
        this.form.enrollType = row.enrollType;

        // 根据 rangeName 查找对应的 id
        if (row.rangeName) {
          const rangeNames = row.rangeName.split(',');
          const selectedIds = this.enrollTpList
              .filter(item => rangeNames.includes(item.rangeName))
              .map(item => item.id);

          this.form.enrollTypeList = selectedIds;
        } else {
          this.form.enrollTypeList = [];
        }

        this.form.rangeName = row.rangeName;
        this.form.rangeId = row.rangeId;
      });

      getSchoolEnrollType(this.search).then((res) => {
        this.schoolenrollType = res;
      });

      // 如果 enrollTpList 已经在 created 中获取，这里可以省略
      getEnrollArea(this.countSearch).then((res) => {
        this.enrollTpList = res;
      });
    },

    // 删除
    del(row) {
      this.$confirm(`确定删除【招生类别：${row.enrollTypeName}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        deleteSchoolEnrollRang({ key: row.id }).then(() => this.getTableData());
      });
    },
    // 提交
    confirmUpdate() {
    this.$refs.form.validate((valid) => {
        if (valid) {
            this.form.rangeName = this.form.enrollTypeList.join(',');
            this.form.rangeId = this.form.enrollTypeList.join(',');
            if (!this.form.id) {
                addSchoolEnrollRang(this.form).then((res) => {
                    this.$message.success("操作成功");
                    this.switchModal("addOrEdit", false);
                    this.getTableData();
                });
            } else {
                // 假设存在编辑的 API 调用
                updateSchoolEnrollRang(this.form).then((res) => {
                    this.$message.success("操作成功");
                    this.switchModal("addOrEdit", false);
                    this.getTableData();
                });
            }
        }
    });
    },
    // 返回
    back() {
      this.$router.push("/setting/enrollRangeSchool");
    },
  },
};
</script>

<style lang="scss" scoped>
.school-name {
  font-size: 22px;
  padding-bottom: 20px;
}
</style>