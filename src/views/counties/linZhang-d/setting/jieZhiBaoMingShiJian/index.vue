<template>
  <div>
    <div style="display: flex;justify-content: space-around">

      <shi-jian-zu-jian
          type="501"
          :beginTime="xiaoXueTime.beginTime"
          :endTime="xiaoXueTime.endTime"
          title="小学报名截止时间设置"
          class="zujian"
      ></shi-jian-zu-jian>

      <shi-jian-zu-jian
          type="601"
          :beginTime="chuZhongTime.beginTime"
          :endTime="chuZhongTime.endTime"
          title="初中报名截止时间设置"
          class="zujian"
      ></shi-jian-zu-jian>
    </div>
  </div>
</template>

<script>
import {getTimeList} from "@/api/linZhangShiJianSheZhi";
import shiJianZuJian from "@/views/countySpecial/linZhang/setting/shiJianZuJian";
export default {
  name: "index",
  components:{
    shiJianZuJian
  },
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      xiaoXueTime:{
        beginTime:'',
        endTime:''
      },
      chuZhongTime:{
        beginTime:'',
        endTime:''
      },
    }
  },
  created() {
    this.getTime()
  },
  methods:{
    getTime(){
      getTimeList({key:3},this.prefixDeptCode).then(res=>{
        this.xiaoXueTime=res[0]||''
        this.chuZhongTime=res[1]||''
      })
    }
  }
}
</script>

<style scoped>
.zujian{
  width:350px;
  height: 200px;
}
</style>
