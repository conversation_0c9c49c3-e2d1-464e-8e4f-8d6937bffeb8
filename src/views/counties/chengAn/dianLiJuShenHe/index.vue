<template>
  <div>
<!--  <div class="search-form_left">-->
<!--      <el-button-->
<!--          v-if="prefixDeptCode=='130424'"-->
<!--          size="small"-->
<!--          type="warning"-->
<!--          icon="el-icon-download"-->
<!--          @click="exportList"-->
<!--      >导出电力局信息</el-button-->
<!--      >-->
<!--      <el-button-->
<!--          size="small"-->
<!--          type="success"-->
<!--          icon="el-icon-upload2"-->
<!--          @click="openImport"-->
<!--      >导入电力局信息</el-button-->
<!--      >-->
<!--    </div>-->
    <div class="sd-option-container">
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.enrollId"
                placeholder="报名ID"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.studentName"
                placeholder="姓名或身份证"
                clearable
            ></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-date-picker
              size="small"
              v-model="search.houseDate"
              type="date"
              placeholder="购房合同时间"
              clearable
            ></el-date-picker>
          </el-form-item> -->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.type"-->
<!--                placeholder="类别"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option-->
<!--                  v-for="(item, index) in typeList"-->
<!--                  :label="item"-->
<!--                  :value="item"-->
<!--                  :key="index"-->
<!--              ></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-select
                size="small"
                v-model="search.statusDl"
                placeholder="审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData1"
            ></el-button>
						<el-button
							size="small"
							type="warning"
							icon="el-icon-download"
							@click="exportList"
              :loading="exportLoading"
						>导出电力局信息</el-button>
          </el-form-item>
<!--          <el-row>-->
<!--            <el-form-item>-->
<!--              <el-button-->
<!--                  size="small"-->
<!--                  type="warning"-->
<!--                  icon="el-icon-download"-->
<!--                  @click="exportEnrollInfo"-->
<!--                  v-if="-->
<!--                  role == 'COUNTY_ADMIN' ||-->
<!--                  role == 'AUDITOR' ||-->
<!--                  role == 'SCHOOL'-->
<!--                "-->
<!--              >导出报名信息</el-button-->
<!--              >-->
<!--            </el-form-item>-->
<!--          </el-row>-->
        </el-form>
      </div>
    </div>
    <el-table
        :data="tableData.records"
        border
        stripe
        v-loading="tableLoading"
    >
      <el-table-column align="center" type="selection" width="50" :fixed="true">
      </el-table-column>
      <el-table-column
          align="center"
          label="报名ID"
          prop="enrollId"
          width="140"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
          width="120"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
          width="170"
      ></el-table-column>
      <!-- <el-table-column
        align="center"
        label="购房时间"
        prop="purchaseTime"
        width="100"
      ></el-table-column> -->
      <el-table-column
          align="center"
          label="电力局审核状态"
          prop="statusDl"
      >
        <template slot-scope="scope">
          <span>{{scope.row.statusDl==1?'待审核':scope.row.statusDl==2?'通过':scope.row.statusDl==3?'不通过':''}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="360" fixed="right">
        <template slot-scope="{ row, $index }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail(row, $index)"
          >详情</el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              @click="pass(row)"
              v-if="role == 'DIAN'"
          >通过</el-link
          >
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              @click="reject(row)"
              v-if="role == 'DIAN'"
          >不通过</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="total"
      >
      </el-pagination>
    </div>

    <!-- 不通过 -->
    <el-dialog
        title="不通过"
         :visible.sync="modal.reject"
        append-to-body
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="rejectForm"
          ref="rejectForm"
          :rules="rejectRules"
          label-position="right"
          label-width="100px"
      >
        <el-form-item prop="statusDlDes" label="不通过原因：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="rejectForm.statusDlDes"
              placeholder="请输入驳回原因，最多200个字符"
              style="width: 400px"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('reject', false)"
        >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmReject"
        >确定</el-button
        >
      </div>
    </el-dialog>


    <!-- 详情 -->
    <el-dialog
        :visible.sync="modal.stuDetail"
        :close-on-click-modal="false"
        title="学生报名详情"
        center
        width="1240px"
        @close="stuDetailClose"
    >
      <enroll-detail
          :stu-detail="curStuDetail"
          :key="curStuDetail.studentBaseId"
      ></enroll-detail>
      <div>
        <el-skeleton :loading="loadingAuditStatus" animated>
          <p>审核情况</p>
          <el-table :data="auditStatusList" border stripe>
            <el-table-column
                align="center"
                label="角色"
                prop="roleType"
            ></el-table-column>
            <el-table-column
                align="center"
                label="审核状态"
                prop="status"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作账号"
                prop="createBy"
            ></el-table-column>
            <el-table-column
                align="center"
                label="驳回原因"
                prop="des"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作时间"
                prop="createTime"
            ></el-table-column>
          </el-table>
        </el-skeleton>
      </div>
      <div class="flex-center" style="margin-top: 20px">
        <el-button
            icon="el-icon-check"
            type="success"
            size="small"
            @click="pass(curStuDetail)"
            v-if="role == 'DIAN'"
        >通过</el-button
        >
        <el-button
            icon="el-icon-close"
            type="danger"
            size="small"
            @click="reject(curStuDetail)"
            v-if="role == 'DIAN'"
        >不通过</el-button
        >
        <el-button type="primary" @click="nextEnrollDetail" size="small"
        >下一条</el-button
        >
        <el-button size="small" type="info" @click="stuDetailClose"
        >关闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {getAuditStatusJueSe, getListShenHe, shenHe} from "@/api/chengAnShenHeYuan"
import { pref, priorityTypeList } from "@/utils/common";
import EnrollDetail from "@/components/JueSeShenHeDetail";
import {policeIm, policeList} from "@/api/adInfo";
import {getEnrollList} from "@/api/enrollment";
export default {
  mixins: [TableMixin, ModalMixin],
  components: { EnrollDetail },
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      role: this.$store.getters.role,
      search: {

      },
      typeList: [],
      modal: {
        reject: false,
        adjust: false,
        report: false,
        notice: false,
        priority: false,
        stuDetail: false,
      },
      schoolList: [],
      multipleSelection: [],
      // 驳回
      rejectForm: {
        id: 0,
        reviewReason: "",
        reviewStatus: "",
        statusDlDes:''
      },
      rejectRules: {
        statusDlDes: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 调剂
      adjustForm: {
        id: "",
        adjustSchoolId: "",
        adjustSchoolName: "",
      },
      adjustRules: {
        adjustSchoolId: [
          {
            required: true,
            message: "请选择调剂学校",
            trigger: "change",
          },
        ],
      },
      adjustInfo: {
        enRollId: "",
        studentName: "",
        enrollSchoolName: "",
        adjustSchoolName: "",
      },
      reportRules: {
        reportStatus: [
          {
            required: true,
            message: "请选择报到类型",
            trigger: "change",
          },
        ],
        noShowCause: [
          {
            required: true,
            message: "请输入未报到原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      exportLoading:false,
      priorityInfo: {
        studentName: "",
        studentIdCardNumber: "",
      },
      priorityTypeList: priorityTypeList,
      auditStatusList: [],
      loadingAuditStatus: true,
      // 当前学生报名详情
      curStuDetail: {},
      // 下一条报名详情index
      index: 0,
    };
  },
  computed: {
    schoolListAvailable() {
      return this.schoolList.filter(
          (item) =>
              item.deptName !=
              (this.adjustInfo.adjustSchoolName
                  ? this.adjustInfo.adjustSchoolName
                  : this.adjustInfo.enrollSchoolName)
      );
    },
  },
  async created() {

  },
  methods: {
    // 导出
    exportList() {
      this.$message.info('正在导出，数据量较大，请耐心等待')
      this.exportLoading = true;
      let params = { ...this.search };
      this.$download(
          `${pref}${this.prefixDeptCode}/biz/recruitStudent/exportData`,
          params,
          "xls",
          "电力局信息列表.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("下载成功");
      });
    },
    // 打开导入
    openImport() {
      this.switchModal("import", true);
      this.removeFile();
    },
    // 上传
    changeFile(file, fileList) {
      this.upload.list = [file.raw];
    },
    // 删除上传
    removeFile() {
      this.upload.list = [];
    },
    // 上传
    confirmUpload() {
      let fd = new FormData();
      fd.append("file", this.upload.list[0]);
      policeIm(fd, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.switchModal("import", false);
        this.getTableData();
      });
    },
    getTableData1(){
      this.search.pageNumber=1
      this.getTableData()
    },
    // 列表
    getTableData() {
      this.tableLoading = true;
      this.search.typeSelect=4
      getListShenHe(this.search, this.prefixDeptCode)
          .then((res) => {
            this.tableData = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    // 通过
    pass(row) {
      this.$confirm("确认该学生信息无误，审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        shenHe(
            { id: row.studentBaseId, statusDl: "2" },
            this.prefixDeptCode
        ).then(() => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 驳回
    reject(row) {
      this.switchModal("reject", true);
      this.$nextTick(() => {
        this.$refs.rejectForm.resetFields();
        this.rejectForm.id = row.studentBaseId;
      });
    },
    // 驳回确定
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          let params = {};
            params.id = this.rejectForm.id;
            params.statusDl = 3;
            params.statusDlDes = this.rejectForm.statusDlDes;
          shenHe(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("reject", false);
            this.getTableData();
          });
        }
      });
    },
    // 详情
    detail(row, index) {
      this.curStuDetail = row;
      this.index = index;
      this.modal.stuDetail = true;
      this.getAuditStatus();
    },
    // 查询审核情况
    getAuditStatus() {
      this.auditStatusList = [];
      this.loadingAuditStatus = true;
      getAuditStatusJueSe(
          { id: this.curStuDetail.studentBaseId,roleType:5 },
          this.prefixDeptCode
      )
          .then((res) => {
            this.auditStatusList = res;
          })
          .finally(() => {
            this.loadingAuditStatus = false;
          });
    },
    // 详情 - 下一条
    nextEnrollDetail() {
      // 当前是不可翻页的页码（最后一页）
      if (
          this.search.pageNumber * this.search.pageSize > this.total ||
          this.search.pageNumber * this.search.pageSize == this.total
      ) {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditStatus();
        } else {
          this.$message.error("已是最后一条数据");
        }
      }
      // 当前是可翻页的页码
      else {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditStatus();
        } else {
          this.search.pageNumber += 1;
          getListShenHe(this.search, this.prefixDeptCode).then((res) => {
            this.tableData = res;
            this.curStuDetail = this.tableData.records[0];
            this.index = 0;
            this.getAuditStatus();
          });
        }
      }
    },
    // 详情 - 关闭
    stuDetailClose() {
      this.switchModal("stuDetail", false);
      this.index = 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.tips {
  padding-left: 30px;
  padding-bottom: 30px;
  font-size: 12px;
}
.notice-num {
  font-size: 16px;
}
</style>
