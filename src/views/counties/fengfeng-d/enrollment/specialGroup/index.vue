<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.enrollId"
              placeholder="报名ID"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.studentName"
              placeholder="姓名或身份证"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="search.estateReviewStatus"
              placeholder="房管局审核状态"
              clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="search.publicSecurityReviewStatus"
              placeholder="公安审核状态"
              clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="getTableData"
            ></el-button>
          </el-form-item>
          <el-row>
            <el-form-item>
              <el-button
                size="small"
                type="primary"
                icon="el-icon-position"
                @click="assignBatch"
                >批量分配学校</el-button
              >
              <el-button
                size="small"
                type="primary"
                icon="el-icon-s-promotion"
                @click="verify"
                >发送现场验证通知书</el-button
              >
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      v-loading="tableLoading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="50" :fixed="true">
      </el-table-column>
      <el-table-column
        align="center"
        label="是否发送现场验证通知书"
        prop="fieldValidation"
        width="105"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="报名ID"
        prop="enrollId"
        width="140"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="身份证号"
        prop="studentIdCardNumber"
        width="170"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学段"
        prop="enrollStageName"
        width="85"
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名时间"
        prop="enrollTime"
        width="160"
      ></el-table-column>
      <el-table-column
        align="center"
        label="房产审核"
        prop="estateReviewStatus"
      ></el-table-column>
      <el-table-column
        align="center"
        label="公安审核"
        prop="publicSecurityReviewStatus"
      ></el-table-column>
      <el-table-column
        align="center"
        label="分配学校"
        prop="adjustSchoolName"
        width="85"
      ></el-table-column>
      <el-table-column
        align="center"
        label="教育局审核状态"
        prop="educationReviewStatus"
        width="85"
      ></el-table-column>
      <el-table-column
        align="center"
        label="是否报到"
        prop="checkInStatus"
        width="85"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="300" fixed="right">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-view"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="detail(row, $index)"
            >详情</el-link
          >
          <el-link
            icon="el-icon-check"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.educationReviewStatus != '待审核'"
            @click="pass(row)"
            >通过</el-link
          >
          <el-link
            icon="el-icon-close"
            type="danger"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.educationReviewStatus != '待审核'"
            @click="reject(row)"
            >不通过</el-link
          >
          <el-link
            icon="el-icon-position"
            type="primary"
            :underline="false"
            @click="assign(row)"
            >分配学校</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 不通过 -->
    <el-dialog
      title="不通过"
       :visible.sync="modal.reject"
        append-to-body
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="rejectForm"
        ref="rejectForm"
        :rules="rejectRules"
        label-position="right"
        label-width="100px"
      >
        <el-form-item prop="reviewStatus" label="驳回类型：">
          <el-select v-model="rejectForm.reviewStatus">
            <el-option label="驳回-修改信息" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="reviewReason" label="驳回原因：">
          <el-input
            type="textarea"
            :rows="5"
            v-model.trim="rejectForm.reviewReason"
            placeholder="请输入驳回原因，最多200个字符"
            style="width: 400px"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="tips">
        <div>注释：</div>
        <div>
          驳回-修改信息：选择修改信息，可以修改报名类别及基础信息进行修改
        </div>
      </div>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('reject', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmReject"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 分配学校 -->
    <el-dialog
      title="分配学校"
      :visible.sync="modal.assign"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <p class="assign-num">当前已选择人数：{{ assignForm.num }}</p>
      <el-form
        :model="assignForm"
        ref="assignForm"
        :rules="assignRules"
        label-position="right"
      >
        <el-form-item prop="schoolId" label="分配学校选择">
          <el-select
            size="small"
            v-model="assignForm.schoolId"
            placeholder="选择学校"
            clearable
            @change="schoolChange"
          >
            <el-option
              v-for="item in schoolList"
              :label="item.deptName"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-15">
        <el-button size="small" @click="switchModal('assign', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmAssign"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 发送现场验证通知书 -->
    <el-dialog
      title="发送现场验证通知书"
      :visible.sync="modal.verify"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <p class="notice-num">发送人数：{{ verifyForm.num }}</p>
      <el-form
        :model="verifyForm"
        ref="verifyForm"
        :rules="verifyRules"
        label-position="right"
      >
        <el-form-item prop="content">
          <el-input
            type="textarea"
            :rows="5"
            v-model.trim="verifyForm.content"
            placeholder="请输入现场验证通知书内容，最多200个字符"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('verify', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="confirmVerify"
          :disabled="verifyForm.num == '' || verifyForm.num == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 详情 -->
    <el-dialog
      :visible.sync="modal.stuDetail"
      :close-on-click-modal="false"
      title="学生报名详情"
      center
      width="1240px"
      @close="stuDetailClose"
    >
      <enroll-detail
        :stu-detail="curStuDetail"
        :key="curStuDetail.studentBaseId"
      ></enroll-detail>
      <div>
        <el-skeleton :loading="loadingAuditStatus" animated>
          <p>审核情况</p>
          <el-table :data="auditStatusList" border stripe>
            <el-table-column
              align="center"
              label="角色"
              prop="role"
            ></el-table-column>
            <el-table-column
              align="center"
              label="审核状态"
              prop="status"
            ></el-table-column>
            <el-table-column
              align="center"
              label="操作账号"
              prop="operationName"
            ></el-table-column>
            <el-table-column
              align="center"
              label="驳回原因"
              prop="causeOfRejection"
            ></el-table-column>
            <el-table-column
              align="center"
              label="调剂学校"
              prop="adjustSchoolName"
            ></el-table-column>
            <el-table-column
              align="center"
              label="操作时间"
              prop="operationTime"
            ></el-table-column>
          </el-table>
        </el-skeleton>
      </div>
      <div class="flex-center" style="margin-top: 20px">
        <el-button
          icon="el-icon-check"
          type="success"
          size="small"
          :disabled="curStuDetail.educationReviewStatus == '通过'"
          @click="pass(curStuDetail)"
          v-if="role == 'COUNTY_ADMIN' || role == 'AUDITOR'"
          >通过</el-button
        >
        <el-button
          icon="el-icon-close"
          type="danger"
          size="small"
          :disabled="curStuDetail.educationReviewStatus == '不通过'"
          @click="reject(curStuDetail)"
          v-if="role == 'COUNTY_ADMIN' || role == 'AUDITOR'"
          >不通过</el-button
        >
        <el-button type="primary" @click="nextEnrollDetail" size="small"
          >下一条</el-button
        >
        <el-button size="small" type="info" @click="stuDetailClose"
          >关闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  passAudit,
  schoolBackDistribution,
  getAuditStatus,
  fieldValidation,
  specialPage,
  migrantChildrenAssignedSchool,
} from "@/api/enrollment";
import { getDepts } from "@/api/common";
import EnrollDetail from "@/components/EnrollDetail";
export default {
  mixins: [TableMixin, ModalMixin],
  components: { EnrollDetail },
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      role: this.$store.getters.role,
      search: {
        enrollStage: "3",
        enrollId: "",
        studentName: "",
        estateReviewStatus: "",
        publicSecurityReviewStatus: "",
      },
      modal: {
        reject: false,
        stuDetail: false,
        assign: false,
        verify: false,
      },
      multipleSelection: [],
      schoolList: [],
      // 驳回
      rejectForm: {
        id: "",
        reviewReason: "",
        reviewStatus: 1,
      },
      rejectRules: {
        reviewStatus: [
          {
            required: true,
            message: "请选择驳回类型",
            trigger: "change",
          },
        ],
        reviewReason: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 分配学校
      assignForm: {
        num: "",
        type: 2, //1随迁子女 2特殊群体
        schoolId: "",
        schoolName: "",
        studentId: [],
      },
      assignRules: {
        content: [
          {
            required: true,
            message: "请输入未报到原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 发送现场验证通知书
      verifyForm: {
        content: "",
        num: "",
      },
      verifyRules: {
        content: [
          {
            required: true,
            message: "请输入现场验证通知书内容",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      auditStatusList: [],
      loadingAuditStatus: true,
      // 当前学生报名详情
      curStuDetail: {},
      // 下一条报名详情index
      index: 0,
    };
  },
  async created() {},
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      specialPage(this.search, this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 查询学校
    getDepts() {
      let params = {
        level: 3,
        period: '3',
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 获取学校名称
    schoolChange() {
      let curSchool = this.schoolList.find(
        (item) => item.id == this.assignForm.schoolId
      );
      this.assignForm.schoolName = curSchool.deptName;
    },
    // 单条分配学校
    assign(row) {
      this.getDepts();
      this.switchModal("assign", true);
      this.$nextTick(() => {
        this.$refs.assignForm.resetFields();
        this.assignForm.schoolId = "";
        this.assignForm.schoolName = "";
        this.assignForm.num = 1;
        this.assignForm.studentId = [row.studentBaseId];
      });
    },
    // 批量分配学校
    assignBatch() {
      this.getDepts();
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      if (checkList.length > 0) {
        this.switchModal("assign", true);
        this.$nextTick(() => {
          this.$refs.assignForm.resetFields();
          this.assignForm.schoolId = "";
          this.assignForm.schoolName = "";
          this.assignForm.num = checkList.length;
          this.assignForm.studentId = checkList;
        });
      } else {
        this.$message.warning("请选择报名学生");
      }
    },
    // 分配学校 - 确定
    confirmAssign() {
      this.$refs.assignForm.validate((valid) => {
        if (valid) {
          migrantChildrenAssignedSchool(
            this.assignForm,
            this.prefixDeptCode
          ).then((res) => {
            this.switchModal("assign", false);
            this.getTableData();
          });
        }
      });
    },
    // 通过
    pass(row) {
      this.$confirm("确认该学生信息无误，审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        passAudit(
          { id: row.studentBaseId, enrollStage: '3' },
          this.prefixDeptCode
        ).then(() => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 驳回
    reject(row) {
      this.switchModal("reject", true);
      this.$nextTick(() => {
        this.$refs.rejectForm.resetFields();
        this.rejectForm.id = row.studentBaseId;
      });
    },
    // 驳回确定
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          let params = {};
          params.studentId = this.rejectForm.id;
          params.state = this.rejectForm.reviewStatus;
          params.reasonForReturn = this.rejectForm.reviewReason;
          schoolBackDistribution(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("reject", false);
            this.getTableData();
          });
        }
      });
    },
    // 发送现场验证通知书
    verify() {
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      this.switchModal("verify", true);
      this.$nextTick(() => {
        this.$refs.verifyForm.resetFields();
        this.verifyForm.content = "";
        this.verifyForm.num = "";
        if (checkList.length > 0) {
          this.verifyForm.num = checkList.length;
        } else {
          let params = {
            search: Object.assign({}, this.search, {
              pageSize: undefined,
              pageNumber: undefined,
            }),
            type: 1,
            content: this.verifyForm.content,
          };
          fieldValidation(params, this.prefixDeptCode).then((res) => {
            this.verifyForm.num = res;
          });
        }
      });
    },
    // 发送现场验证通知书确定
    confirmVerify() {
      this.$refs.verifyForm.validate((valid) => {
        if (valid) {
          let checkList = this.multipleSelection.map(
            (item) => item.studentBaseId
          );
          let params = {
            search: Object.assign({}, this.search, {
              pageSize: undefined,
              pageNumber: undefined,
            }),
            type: 2,
            content: this.verifyForm.content,
            list: checkList,
          };
          fieldValidation(params, this.prefixDeptCode).then((res) => {
            this.$confirm("现场验证通知书成功发送 " + res + " 人", "提示", {
              confirmButtonText: "关闭",
              showCancelButton: false,
              type: "success",
              closeOnClickModal: false,
              showClose: false,
              closeOnPressEscape: false,
            }).then(() => {
              this.switchModal("verify", false);
              this.getTableData();
            });
          });
        }
      });
    },
    // 详情
    detail(row, index) {
      this.curStuDetail = row;
      this.index = index;
      this.modal.stuDetail = true;
      this.getAuditStatus();
    },
    // 查询审核情况
    getAuditStatus() {
      this.auditStatusList = [];
      this.loadingAuditStatus = true;
      getAuditStatus(
        { key: this.curStuDetail.studentBaseId },
        this.prefixDeptCode
      )
        .then((res) => {
          this.auditStatusList = res;
        })
        .finally(() => {
          this.loadingAuditStatus = false;
        });
    },
    // 详情 - 下一条
    nextEnrollDetail() {
      // 当前是不可翻页的页码（最后一页）
      if (
        this.search.pageNumber * this.search.pageSize > this.total ||
        this.search.pageNumber * this.search.pageSize == this.total
      ) {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditStatus();
        } else {
          this.$message.error("已是最后一条数据");
        }
      }
      // 当前是可翻页的页码
      else {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditStatus();
        } else {
          this.search.pageNumber += 1;
          specialPage(this.search, this.prefixDeptCode).then((res) => {
            this.tableData = res;
            this.curStuDetail = this.tableData.records[0];
            this.index = 0;
            this.getAuditStatus();
          });
        }
      }
    },
    // 详情 - 关闭
    stuDetailClose() {
      this.switchModal("stuDetail", false);
      this.index = 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.tips {
  padding-left: 30px;
  padding-bottom: 30px;
  font-size: 12px;
}
.assign-num {
  font-size: 16px;
}
</style>