<template>
  <div>
    <el-button type="primary" @click="add">添加</el-button>
    <el-table
        :data="tableData"
        style="width: 100%"
    >
      <el-table-column
      prop="index"
      label="序号"
      >

      </el-table-column>
      <el-table-column
          prop="juniorName"
          label="学校"
          width="180"
      >
      </el-table-column>
      <el-table-column
          prop="name"
          label="关联学校"
          width="180"
      >
      </el-table-column>
      <el-table-column
          prop="address"
          label="添加时间"
      >
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="{row}">
          <el-link
              icon="el-icon-delete"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              @click="del(row)"
          >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        :title="dialogTitle"
        :visible.sync="modal.add"
        center
        width="1000px"
        :close-on-click-modal="false"
    >
      <el-form
          :model="addForm"
          ref="addForm"
          label-width="180px"
          :rules="rules"
      >
        <el-form-item prop="juniorId" label="选择初中学校">
          <el-select
              v-model="addForm.juniorId"
              placeholder="请选择初中学校"
              clearable
              filterable
          >
            <el-option
                v-for="item in juniorList"
                :key="item.id"
                :label="item.deptName"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="deptName" label="选择关联小学">
          <el-select
              v-model="addForm.primaryIds"
              placeholder="请选择关联小学"
              filterable
              multiple
          >
            <el-option
                v-for="item in primaryList"
                :key="item.id"
                :label="item.deptName"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('add', false)"
        >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
        >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ModalMixin from "@/mixins/ModalMixin";
import { getDepts } from "@/api/common";
import {addGuanLian,deletePrimaryGuanLian,deleteJuniorGuanLian,getGuanLianList} from "@/api/setting";

export default {
  name: "xueXiaoGuanLian",
  mixins:[ModalMixin],
  data(){
    return{
      juniorList:[],
      primaryList:[],
      tableData:[],
      dialogTitle:'新增关联中学',
      modal:{
        add:false
      },
      addForm:{},
      rules:
          {
            juniorId:[
              {
                required: true,
                message: "请选择初中学校",
                trigger: "change",
              },
            ],
            primaryIds:[ {
              required: true,
              message: "请选择关联小学",
              trigger: "change",
            },]
          }
    ,
      prefixDeptCode: this.$store.getters.deptCode,
    }
  },
  created() {
    this.getJuniorSchool()
    this.getPrimarySchool()
    this.getTable()
  },
  methods:{
    add(){
      this.switchModal("add", true);
    },
    del(row){
      this.$confirm(`确认删除${row.juniorName
      }？`, "删除学校", {
        type: "error",
      }).then(() => {
        deleteJuniorGuanLian({
          key:row.juniorId
        }).then(res=>{
            if(res){
              this.$message.success('删除成功')
              this.getTable()
            }
        })
      })
    },
    getTable(){
      getGuanLianList({pageNumber:1,pageSize:10,type:1}).then(res=>{
        this.tableData=res.records
      })
    },
    confirmUpdate(){
        addGuanLian(this.addForm).then(res=>{
          if(res){
            this.switchModal("add", false);
            this.$message.success("添加成功")
            this.getTable()
          }
        })
    },
      getJuniorSchool(){
        let params = {
          level: 3,
          period:3,
          parentId: this.$store.getters.deptId,
        };
        getDepts(params).then((res) => {
          this.juniorList = res;
        });
      },
      getPrimarySchool(){
      let params = {
        level: 3,
        period:2,
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.primaryList = res;
      });
    }
  }
}
</script>

<style scoped>

</style>
