<template>
  <el-form
    :model="form"
    :rules="rules"
    ref="form"
    label-width="auto"
    @keydown.enter="save"
  >
    <el-row>
      <el-col :span="16" :offset="4">
        <el-form-item label="选择区县" prop="deptId">
          <el-select v-model="form.deptId" size="small">
            <el-option
              v-for="item in deptOptions"
              :key="item.id"
              :value="item.id"
              :label="item.deptName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号类别" prop="roleId">
          <el-select v-model="form.roleId" size="small">
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号" prop="username">
          <el-input
            v-model.trim="form.username"
            placeholder="请输入用户名"
            clearable
            maxlength="32"
            show-word-limit
            size="small"
            style="width: 215px"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="nickname">
          <el-input
            v-model.trim="form.nickname"
            placeholder="请输入真实姓名"
            clearable
            maxlength="32"
            show-word-limit
            size="small"
            style="width: 215px"
          />
        </el-form-item>
        <el-form-item label="电话" prop="mobile">
          <el-input
            v-model.trim="form.mobile"
            placeholder="请输入手机号码"
            clearable
            maxlength="11"
            show-word-limit
            size="small"
            style="width: 215px"
          />
        </el-form-item>
        <el-form-item label="默认密码">
          <div style="color: red; line-height: 20px; padding-top: 9px">
            教育局总账号密码生成规则：随机生成数字+字母+特殊符号8—12位组合密码
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import FormMixin from "@/mixins/FormMixin";
import { create, detail, update } from "@/api/user";
import { getDepts } from "@/api/common";
import { roleOptions } from "@/utils/common";
import { mobileValidator } from "@/utils/validator";

export default {
  name: "EditForm",
  mixins: [FormMixin],
  data() {
    return {
      form: {
        id: null,
        deptId: null,
        roleId: null,
        username: null,
        nickname: null,
        mobile: null,
      },
      rules: {
        deptId: [{ required: true, message: "请选择区县", trigger: "change" }],
        roleId: [
          { required: true, message: "请选择账号类别", trigger: "change" },
        ],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        nickname: [
          { required: true, message: "请输入姓名", trigger: "blur" },
        ],
        mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { validator: mobileValidator, trigger: "blur" },
        ]
      },
      roleOptions: roleOptions,
      deptOptions: [],
    };
  },
  async created() {
    this.deptOptions = await getDepts({ level: 2 });
    if(this.mode == "ADD") {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    }
  },
  computed: {},
  methods: {
    async getDetail() {
      this.detailLoading = true;
      const { id, deptId, roleId, username, nickname, mobile } = await detail({key: this.data.id});
      this.form = {
        id,
        deptId,
        roleId,
        username,
        nickname,
        mobile,
      };
      this.detailLoading = false;
    },
    // 提交
    saveSubmit() {
      if(this.mode == "ADD") {
        create(this.form).then(() => {
          console.log("saveSubmit", "save-complete");
          this.$emit("save-complete", 1);
        })
        .catch(() => this.$emit("save-complete", 0));
      }
      else {
       update(this.form).then(() => {
          console.log("saveSubmit", "save-complete");
          this.$emit("save-complete", 1);
        })
        .catch(() => this.$emit("save-complete", 0));
      }
    },
  }
};
</script>

<style scoped lang="scss"></style>