<!-- 清除学生报名数据 -->
<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="clearBatch"
          size="small"
          >批量清除</el-button
        >
      </div>
      <form @submit="searchSubmit" class="sd-search">
        <el-input
          v-model.trim="search.keywords"
          placeholder="搜索学生身份证号"
          clearable
          size="small"
        ></el-input>
        <el-button
          type="primary"
          class="sd-btn-search"
          icon="el-icon-search"
          @click="searchSubmit"
          size="small"
        />
      </form>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
        type="index"
        :index="getTableIndex"
        label="序号"
        width="60"
        align="center"
      />
      <el-table-column
        prop="username"
        label="学生姓名"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="idNumber"
        label="身份证号"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="clearTime"
        label="清除时间"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="id" label="操作" width="260" fixed="right">
        <template slot-scope="{ row }">
          <el-link
            type="success" 
            icon="el-icon-edit" 
            @click="showEditForm(row)" 
            :underline="false"
            >恢复
          </el-link>
          <el-link
            type="danger"
            icon="el-icon-delete"
            @click="showDeleteForm(row)"
            :underline="false"
            >清除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="sd-pagination-container">
      <el-pagination
        background
        layout="total, prev, pager, next,sizes"
        :total="Number(tableData.total || 0)"
        :current-page.sync="search.pageNumber"
        :page-size.sync="search.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-drawer
      class="sd-drawer"
      :title="dialogFormComponent.title()"
      size="800px"
      append-to-body
      destroy-on-close
      :wrapperClosable="false"
      :visible.sync="dialogFormComponent.dialogFormVisible"
    >
      <div class="sd-drawer-content">
        <component
          :ref="dialogFormComponent.name"
          :is="dialogFormComponent.name"
          :data="dialogFormComponent.data"
          :mode="dialogFormComponent.mode"
          @save-complete="dialogFormComponent.saveComplete"
        />
      </div>
      <div class="sd-drawer-footer">
        <el-button
          type="default"
          @click="dialogFormComponent.close()"
          size="small"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="dialogFormComponent.save()"
          :loading="dialogFormComponent.saveLoading"
          size="small"
          >保 存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";

export default {
  name: "UserList",
  mixins: [TableMixin],
  components: {},
  data() {
    return {
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: null,
      },
      roles: [],
    };
  },
  created() {},
  methods: {
    async getTableData() {
      this.tableLoading = true;
      const data = await this.$apis.user.list(this.search);
      this.tableData = data;
      this.tableLoading = false;
    },
    showDeleteForm(row) {
      this.$confirm(`确定删除【${row.username}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        this.$apis.user.delete(row.id).then(() => this.getTableData());
      });
    },
    // 批量清除
    showDeleteFormBatch(row) {
      this.$confirm(`确定删除【${row.username}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        this.$apis.user.delete(row.id).then(() => this.getTableData());
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
