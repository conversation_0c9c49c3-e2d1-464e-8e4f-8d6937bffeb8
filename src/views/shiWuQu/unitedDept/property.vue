<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="openImport"
          >导入房管信息</el-button
        >
      </div>
      <div class="search-form_right">
        <el-form :model="search" :inline="true">
          <el-form-item prop="enrollId">
            <el-input
                size="small"
                v-model.trim="search.enrollId"
                placeholder="报名ID"
                style="width: 185px"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="keywords">
            <el-input
                size="small"
                v-model.trim="search.keywords"
                placeholder="姓名或身份证"
                style="width: 185px"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.houseStatus"
                placeholder="房管联审状态"
                style="width: 185px"
                clearable
            >
              <el-option label="待审核" :value="0"></el-option>
              <el-option label="通过" :value="1"></el-option>
              <el-option label="不通过" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
          align="center"
          label="序号"
          width="60"
          type="index"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报名ID"
          width="150"
          prop="signId"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学段"
          prop="enrollStageText"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="idCard"
          width="180"
      ></el-table-column>
      <el-table-column
          align="center"
          label="类别"
          prop="houseType"
      >
        <template slot-scope="scope">
          {{scope.row.houseType==1?'使用《房屋所有权证》信息报名':
            scope.row.houseType==2?'使用《不动产权证书》信息报名':
                scope.row.houseType==3?'使用购房合同信息报名':
                    scope.row.houseType==4?'小产权房信息报名':
                        scope.row.houseType==5?'使用廉租房/公租房信息报名':
                            scope.row.houseType==6?'自然村':''}}
        </template>

      </el-table-column>
      <el-table-column
          align="center"
          label="证件编号"
          prop="documentNum"
          width="240"
      ></el-table-column>
      <el-table-column
          align="center"
          label="户主"
          prop="masterName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="房主姓名"
          prop="ownerName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="与该生关系"
          prop="hukouRelation"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="房管部门审核"
          prop="houseStatus"
      >
        <template slot-scope="scope">
          {{scope.row.houseStatus==1?'通过':scope.row.houseStatus==2?'不通过':scope.row.houseStatus==0?'待审核':''}}
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        align="center"-->
      <!--        label="房管部门审核"-->
      <!--        prop="publicSecurityReviewStatus"-->
      <!--      ></el-table-column>-->
      <!-- <el-table-column
        align="center"
        label="操作"
        width="300px"
        v-if="role == 'HOUSE'"
      >
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-view"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="detail(row)"
            >详情</el-link
          >
          <el-link
            icon="el-icon-check"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.publicSecurityReviewStatus == '通过'"
            @click="pass(row)"
            >通过</el-link
          >
          <el-link
            icon="el-icon-close"
            type="danger"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.publicSecurityReviewStatus == '不通过'"
            @click="fail(row)"
            >不通过</el-link
          >
        </template>
      </el-table-column> -->
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 导入 -->
    <el-dialog
      :visible.sync="modal.import"
      center
      title="导入信息"
      :close-on-click-modal="false"
      width="500px"
    >
      <el-upload
        action="#"
        :limit="upload.limit"
        :auto-upload="upload.auto"
        :file-list="upload.list"
        :on-change="changeFile"
        :on-remove="removeFile"
      >
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>
      <el-form
          ref="form"
          label-width="80px"
      >
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          :disabled="upload.list.length == 0"
          @click="confirmUpload"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 不通过 -->
    <el-dialog
      title="不通过"
       :visible.sync="modal.reject"
        append-to-body
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="rejectForm"
        ref="rejectForm"
        :rules="rejectRules"
        label-position="right"
        label-width="120px"
      >
        <el-form-item prop="rejectStatus" label="不通过原因：">
          <el-radio-group v-model="rejectForm.rejectStatus">
            <el-radio :label="1">信息不符</el-radio>
            <el-radio :label="2">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :prop="rejectForm.rejectStatus == 2 ? 'cause' : 'empty'"
          label="原因："
        >
          <el-input
            type="textarea"
            :rows="5"
            v-model.trim="rejectForm.cause"
            placeholder="请输入不通过原因，最多200个字符"
            style="width: 400px"
            maxlength="200"
            show-word-limit
            :disabled="rejectForm.rejectStatus != 2"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-15">
        <el-button size="small" @click="switchModal('reject', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmReject"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import { fangChanStudentList, propDeptIm } from "@/api/shiWuQuLianHeBuMenShenHe";
import { relationList } from "@/utils/dictionary";
import { pref } from "@/utils/common";
export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      errorMessages: [],
      prefixDeptCode: this.$store.getters.deptCode,
      role: this.$store.getters.role,
      search: {
        enrollId: "",
        keywords: "",
        createdTime: "",
        endTime: "",
        publicSecurityAndHouseReviewStatus: "",
        type: 1,
      },
      modal: {
        import: false,
        reject: false,
      },
      upload: {
        limit: 1,
        auto: false,
        list: [],
      },
      rejectForm: {
        type: 1,
        studentId: "",
        rejectStatus: "",
        cause: "",
      },
      rejectRules: {
        rejectStatus: [
          {
            required: true,
            message: "请选择驳回原因",
            trigger: "change",
          },
        ],
        cause: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    // 列表
    getTableData() {
      fangChanStudentList(this.search, this.prefixDeptCode).then(
        (data) => (this.tableData = data)
      );
    },
    // 关系
    relationCn(val) {
      return relationList.find((v) => v.id == val)?.val || "";
    },

    // 导入
    openImport() {
      this.switchModal("import", true);
      this.removeFile();
      this.errorMessages = [];
    },
    // 上传
    changeFile(file, fileList) {
      this.upload.list = [file.raw];
    },
    // 删除上传
    removeFile() {
      this.upload.list = [];
    },
    // 上传
    confirmUpload() {
      let fd = new FormData();
      fd.append("file", this.upload.list[0]);
      propDeptIm(fd, this.prefixDeptCode).then((res) => {
        if(res.length>0){
          this.errorMessages =res;
        }else {
          this.$message.success("操作成功");
          this.getTableData();
          this.switchModal("import", false);
        }
      });
    },
    // 通过
    pass(row) {
      this.$confirm("确认该学生信息无误，审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        passSecurityAndHouseAudit(
          { studentId: row.studentBaseId, type: 1 },
          this.prefixDeptCode
        ).then(() => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 驳回
    reject(row) {
      this.switchModal("reject", true);
      this.$nextTick(() => {
        this.$refs.rejectForm.resetFields();
        this.rejectForm.studentId = row.studentBaseId;
      });
    },
    // 驳回确定
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          let params = {
            studentId: this.rejectForm.studentId,
            type: this.rejectForm.type,
            cause:
              this.rejectForm.rejectStatus == 1
                ? "信息不符"
                : this.rejectForm.cause,
          };
          notPassSecurityAndHouseAudit(params, this.prefixDeptCode).then(
            (res) => {
              this.$message.success("操作成功");
              this.switchModal("reject", false);
              this.getTableData();
            }
          );
        }
      });
    },
    // 详情
    detail() {
      this.switchModal("detail", true);
    },
  },
};
</script>

<style lang="scss">
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-form--inline .el-form-item {
    margin-bottom: 0 !important;
  }
}
</style>
