<template>
  <div>
    <div class="sd-option-container">
<!--class="sd-search"-->
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.signId"
                placeholder="报名ID"
                class="sd-w-200"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.keywords"
                placeholder="姓名或身份证号"
                class="sd-w-200"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.type"
                placeholder="报名类型"
                class="sd-w-150"
                clearable
            >
              <el-option label="选择拟报名学校" :value="1"></el-option>
              <el-option label="不确定拟报名学校" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item  v-if="
                role == 'COUNTY_ADMIN' || role == 'AUDITOR'
              ">
            <el-select clearable size="small" v-model="search.enterSchoolId" placeholder="录取学校" >
              <el-option
                  v-for="item in schoolList"
                  :key="item.id"
                  :label="item.deptName
"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.twinsStatus"
                placeholder="是否多胞胎"
                class="sd-w-150"
                clearable
            >
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.publicStatus"
                placeholder="是否公示"
                class="sd-w-130"
                clearable
            >
              <el-option label="未公示" :value="0"></el-option>
              <el-option label="公示" :value="1"></el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.schoolStatus"-->
<!--                placeholder="学校审核状态"-->
<!--                class="sd-w-150"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="审核中" :value="0"></el-option>-->
<!--              <el-option label="已通过" :value="1"></el-option>-->
<!--              <el-option label="已驳回-修改信息" :value="2"></el-option>-->
<!--              <el-option label="已驳回-重新报名" :value="3"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.eduStatus"-->
<!--                placeholder="教育局审核状态"-->
<!--                class="sd-w-150"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="审核中" :value="0"></el-option>-->
<!--              <el-option label="已通过" :value="1"></el-option>-->
<!--              <el-option label="已驳回-修改信息" :value="2"></el-option>-->
<!--              <el-option label="已驳回-重新报名" :value="3"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.houseStatus"-->
<!--                placeholder="房管局审核状态"-->
<!--                class="sd-w-130"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="0"></el-option>-->
<!--              <el-option label="通过" :value="1"></el-option>-->
<!--              <el-option label="不通过" :value="2"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.policeStatus"-->
<!--                placeholder="公安审核状态"-->
<!--                class="sd-w-130"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="0"></el-option>-->
<!--              <el-option label="通过" :value="1"></el-option>-->
<!--              <el-option label="不通过" :value="2"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.signStatus"-->
<!--                placeholder="是否报到"-->
<!--                class="sd-w-130"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="未报到" :value="2"></el-option>-->
<!--              <el-option label="已报到" :value="1"></el-option>-->
<!--              <el-option label="待报到" :value="0"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData1"
            ></el-button>
          </el-form-item>
         </el-form>
        </div>
    </div>
    <el-table :data="tableData.records" border stripe @selection-change="handleSelectionChange">
      <el-table-column
          type="selection"
          width="55">
      </el-table-column>
      <el-table-column
          align="center"
          label="报名ID"
          prop="signId"
          width="90"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="idCard"
          width="150"
      ></el-table-column>

      <el-table-column
          align="center"
          label="录取学校"
          prop="enterSchoolName"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="多胞胎个数"
          prop="twinsNum"
      >
      </el-table-column>
<!--      <el-table-column align="center" label="报名类别" prop="signType">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.signType == 1 ? "房户一致" :scope.row.signType == 2?"户口":scope.row.signType == 3?'房产':scope.row.signType == 4?'':'外来务工经商' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="购房时间"-->
<!--          prop="aId"-->
<!--          width="150"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="报名学校"-->
<!--          prop="schoolName"-->
<!--      ></el-table-column>-->
<!--      <el-table-column align="center" label="是否优抚对象" prop="careStatus">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.careStatus == 1 ? "是" :scope.row.careStatus == 0?"否":'' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="调剂学校"-->
<!--          prop="adjustSchoolName"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="报名时间"-->
<!--          prop="createTime"-->
<!--          width="150"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="房产审核"-->
<!--          prop="houseStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.houseStatus == 1 ? "通过" :scope.row.houseStatus == 0?"待审核":scope.row.houseStatus == 2?"未通过":'' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="公安审核"-->
<!--          prop="policeStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.policeStatus == 1 ? "通过" :scope.row.policeStatus == 0?"待审核":scope.row.policeStatus == 2?"未通过":'' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="学校审核状态"-->
<!--          prop="schoolStatus"-->
<!--          width="130"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{scope.row.schoolStatus == 1 ? "通过" :scope.row.schoolStatus == 0?"审核中":scope.row.schoolStatus == 2?"驳回修改信息":scope.row.schoolStatus == 3?'驳回重新报名':''}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="教育局审核状态"-->
<!--          prop="eduStatus"-->
<!--          width="130"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{scope.row.eduStatus == 1 ? "通过" :scope.row.eduStatus == 0?"审核中":scope.row.eduStatus == 2?"驳回修改信息":scope.row.eduStatus == 3?'驳回重新报名':''}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--      align="center"-->
<!--      label="对口类型"-->
<!--      prop="signTypeStr"-->
<!--      >-->
<!--      </el-table-column>-->
      <el-table-column
          align="center"
          label="是否公示"
          prop="publicStatus"
      >
        <template slot-scope="scope">
          <template>{{scope.row.publicStatus==0?'未公示':scope.row.publicStatus==1?'公示':''}}</template>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="报名类型"
          prop="type"
      >
        <template slot-scope="scope">
          <template>{{scope.row.type==1?'选择拟报名学校':scope.row.type==2?'不确定拟报名学校':''}}</template>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="是否报到"
          prop="signStatus"
      >
        <template slot-scope="scope">
          <template>{{scope.row.signStatus==0?'待报到':scope.row.signStatus==1?'报到':scope.row.signStatus==2?'未报到':''}}</template>
        </template>
      </el-table-column>
<!--      <el-table-column align="center" label="备注" prop="mark"></el-table-column>-->
<!--      <el-table-column align="center" label="优抚类别" prop="careType"></el-table-column>-->
      <el-table-column align="center" label="操作" width="300" fixed="right">
        <template slot-scope="{ row }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail(row)"
          >详情</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="$pageSizes"
      >
<!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
    <el-dialog
        title="学生报名信息详情"
        width="800px"
        append-to-body
        :show-close="false"
        :visible.sync="modal.detail"
        :close-on-click-modal="false"
        center
    >
      <div>
        <component
            v-if="modal.detail"
            :ref="'detail'"
            :is="'detail'"
            :row="row"
            :data=null
            :mode=null
            :data-row="selectRow?selectRow:{}"
            @searchs="searchs"

        />
<!--        @save-complete="dialogFormComponent.saveComplete"-->
      </div>
      <div class="flex-center" style="margin-top:20px ">
        <el-button
            type="default"
            @click="fanHui"
            size="small"
        >返回</el-button
        >
      </div>
    </el-dialog>
<!--    &lt;!&ndash; 详情 &ndash;&gt;-->
<!--    <el-dialog :visible.sync="modal.detail"> </el-dialog>-->
<!--    &lt;!&ndash; 详情 &ndash;&gt;-->
<!--    <el-dialog :visible.sync="modal.add"> </el-dialog>-->
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import detail from "@/views/shiWuQu/shiWuQuYiXueXiaoBaoMing/detail";

import { getList } from "@/api/enrollment";
import {
  getshiWuQuList,
  baoMingPiliangShenHeTongGuo,
  baoMingShenHeBuTongGuo,
  baoMingShenHeTongGuo,
  studentTiaoJiao,
  gongShiLuQuJieGuo,
  tiaoJiaoXueXiaoList,
  youFuLeiXingList,
  addYouFuLeiXing,
  getFaSongLuQuTongZhiRenShu,
  gongShiLuQuRenShu,
  faSongLuQuTongZhi,
  xuShengBaoDao,
    setDuiKouLeiBie
} from "@/api/shiWuQuBaoMingLieBiao";
import {pref} from "@/utils/common";
import {getDepts} from "@/api/common";
export default {
  mixins: [ ModalMixin],
  data() {
    return {
      baoDaoStatus:'',
      baoDaoShow:false,
      faSongLuQuShow:false,
      luQuTongZhi:{
        content:'',
        studentIds:[]
      },
      youFu:{
        content:'',
        id:''
      },
      id:'',//学生ID
      mark:'',//学生审核通过备注
      status:'',//学生驳回类型
      reason:'',
      selectRow:{},
      youFuShow:false,
      tiaoJiSchoolList:[],
      tiaoJiXinXi:{},
      beiZhuShow:false,
      studentIds:[],
      idList:[],
      prefixDeptCode: this.$store.getters.deptCode,
      radio:1,
      tiaoJiShow:false,
      boHuiYuanYin:1,
      dialogVisible:false,
      row:'',
      tableData:{},
      saveLoading: false,
      role: this.$store.getters.role,
      total:'',
      search: {
        pageNumber: 1,
        pageSize: 10,
        schoolType: 1,
        nature:2,
        period:2
      },
      schoolList:[],
      youFuLeiXingList:[

      ],
      modal: {
        add: false,
        detail: false,
      },
      baodao:{

      },
      duiKouShow:false,
      duiKouStatus:''
    };
  },
components:{detail},
  created() {
     this.getTableData()
  if(this.role=="COUNTY_ADMIN"){
    youFuLeiXingList({},this.prefixDeptCode).then(res=>{
      this.youFuLeiXingList=res
      console.log(res,"res")
    })
  }

    if(this.role!="SCHOOL"){
      this.getSchoolList()
    }
  },
  methods: {
    change(e){
      this.$forceUpdate()
    },
    duiKouBiaoJi(row){
      this.id=row.id
      this.duiKouStatus=row.signType
      this.duiKouShow=true
    },
    duiKouBiaoJi1(){
      setDuiKouLeiBie({id:this.id,type:this.duiKouStatus},this.prefixDeptCode).then(res=>{
        if(res){
          this.$message.success("操作成功")
          this.getTableData()
          this.duiKouShow=false
        }
      })
    },
    getSchoolList(){
      console.log(this.$store.getters,"getters")
      getDepts({type:1,period:2,deptCode:this.prefixDeptCode},this.prefixDeptCode).then(res=>{
      this.schoolList=res
        console.log(res,"res")
      })
    },
    exportData(){
      let params = this.search;
      this.$download(
          `${pref}${this.prefixDeptCode}/five/export/exportStudent`,
          params,
          "xls",
          "选择拟报名学校统计列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    close(val){
      this.switchModal('add', false)
    },
    searchs(val) {
      this.search=val
      this.getTableData()
    },
    handleSelectionChange(val) {
     this.idList=val
    },
    getTableData1(){
      this.search.pageNumber=1
      this.getTableData()
    },
    getTableData() {
      getshiWuQuList(this.search,this.prefixDeptCode).then((res) =>{
        console.log(res,this.search,"res")
        this.total=Number(res.total)
        this.tableData=res

      })
    },
    piLiangShenHeTongGuo(){
      this.$confirm('确认完成审核？所有初审已通过的学生，教育局审核状态都会改为已通过，请慎重使用。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let studentIds=[]
        let shenHe=this.search
        this.idList.forEach((item) =>{
        studentIds.push(item.id)
        })
        shenHe.studentIds=studentIds
        baoMingPiliangShenHeTongGuo(shenHe,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.idList=[]
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    gongShiLuQuJieGuo(){
      // let studentIds=[]
      let gongShi=this.search
      // this.idList.forEach((item) =>{
      //   studentIds.push(item.id)
      // })
      // gongShi.studentIds=studentIds
      gongShiLuQuRenShu(gongShi,this.prefixDeptCode).then(res=>{
      if(res.status){
        this.$confirm(''+res.message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {

          gongShiLuQuJieGuo(gongShi,this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '公布录取结果成功!'
              });
              // this.idList=[]
              this.getTableData()
            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.$alert(''+res.message, '提示', {
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }

      })


    },
    xiuGaiXinXi(){
      this.status=2
      this.boHuiYuanYin=1
    },
    buKeZaiBao(){
      this.status=3
      this.boHuiYuanYin=2
    },
    tiaoJi(){
       this.$refs.detail.tiaoJi()
    },
    tiaoJi1(){
      this.$confirm('确认要调剂该学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        studentTiaoJiao(this.tiaoJiXinXi,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '调剂成功!'
            })
            this.tiaoJiXinXi={}
            this.tiaoJiShow=false
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    tongGuo(){
      this.$refs.detail.tongGuo()
    },
    buTongGuo(){
      this.$refs.detail.buTongGuo()
    },
    xiaYiTiao(){
      this.$refs.detail.xiaYiTiao()
    },
    fanHui(){
      this.$refs.detail.fanHui()
      this.switchModal('detail', false)
    },
    save(){
       this.$refs.addForm.save()
       this.getTableData()
      // this.switchModal('add', false)
    },
    add(){
      this.switchModal("add", true)
      this.getTableData()
    },
    // 详情
    detail(row) {
      this.row=row
      this.selectRow=this.search
      console.log(this.search,"search")
      console.log(row);
      this.switchModal('detail', true)
    },
    shenHeBuTongGuo(){
      this.$confirm('确认该学生信息无误，审核不通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeBuTongGuo({id:this.id,status:this.status,reason:this.reason},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.dialogVisible=false
            this.id=''
            this.status=''
            this.reason=''
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });

    },
    beiZhuAdd(){
      this.$confirm('确认该学生信息无误，审核通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeTongGuo({id:this.id,mark:this.mark},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.id=''
            this.mark=''
            this.beiZhuShow=false
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    quXiao(){
      this.duiKouShow=false
      this.baoDaoShow=false
      this.dialogVisible=false
      this.beiZhuShow=false
      this.tiaoJiShow=false
      this.youFu={}
      this.youFuShow=false
      this.tiaoJiXinXi={}
      this.id=''
      this.mark=''
      this.reason=''
      this.status=''
      this.faSongLuQuShow=false
      this.getTableData()
    },
    // 通过
    pass(row) {
      if(this.role == 'COUNTY_ADMIN' || this.role == 'AUDITOR'){
        this.$confirm('确认该学生信息无误，审核通过', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          baoMingShenHeTongGuo({id:row.id,mark:this.mark},this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '审核成功!'
              });
              this.id=''
              this.mark=''
              this.beiZhuShow=false
              this.getTableData()
            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.beiZhuShow=true
        this.id=row.id
      }

    },
    // 不通过
    fail(row) {
      this.dialogVisible=true
      this.id=row.id
    },
    // 调剂
    adjust(row) {
      console.log(row,"row")
      this.tiaoJiXinXi={
        id: row.id,
        signId: row.signId,
        studentName:row.studentName,
        schoolName:row.schoolName,
      }
       tiaoJiaoXueXiaoList({key:row.id},this.prefixDeptCode).then(res=>{
         this.tiaoJiSchoolList=res
       })
      this.tiaoJiShow=true
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
    // 报到
    report(row) {
      console.log(row,"row")
      this.baodao.studentId=row.id
      this.baoDaoShow=true

    },
    baoDao(){
      this.$confirm('确认该学生'+`${this.baodao.status==2?'未报到':'报到'}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
            xuShengBaoDao(this.baodao,this.prefixDeptCode).then(res=>{
                  if(res){
                  this.$message({
                    type: 'success',
                     message: '操作成功!'
                   });
                   }
                   this.baoDaoShow=false
                   this.getTableData()
            })


      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
