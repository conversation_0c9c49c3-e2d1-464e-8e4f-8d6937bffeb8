<template>
  <div>
    <h5>学生基本信息</h5>
    <el-descriptions  title="" >
      <el-descriptions-item label="学生姓名">{{form.studentName}}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{form.idCard}}</el-descriptions-item>
      <el-descriptions-item label="性别">{{form.gender==1?'男':form.gender==2?'女':''}}</el-descriptions-item>
      <el-descriptions-item label="户口性质">{{form.huokouType==1?'市五区':form.huokouType==0?'非市五区':''}}</el-descriptions-item>
      <el-descriptions-item label="出生日期">{{form.birthday}}</el-descriptions-item>
      <el-descriptions-item label="民族">{{form.nation}}</el-descriptions-item>
    </el-descriptions>
    <div v-if="form.hukou">
      <h5>户口信息</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="户主姓名">{{form.hukou.masterName}}</el-descriptions-item>
        <el-descriptions-item label="户主身份证号">{{form.hukou.masterIdCard}}</el-descriptions-item>
        <el-descriptions-item label="户号">{{form.hukou.houseNum}}</el-descriptions-item>
        <el-descriptions-item label="户口本登记地址">{{form.hukou.rangeName!=null?form.hukou.rangeName+form.hukou.address:form.hukou.address}}</el-descriptions-item>
        <el-descriptions-item label="户主与该生关系">{{form.hukou.relation}}</el-descriptions-item>
      </el-descriptions>
      <!--      <div class="pic-box">-->
      <!--        <div v-if="form.hukou.imgIndex">-->
      <!--          <el-image-->

      <!--              :src="imgQianZhui+form.hukou.imgIndex"-->
      <!--              :preview-src-list="[imgQianZhui+form.hukou.imgIndex]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>学生所属户口薄首页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.hukou.imgMaster">-->
      <!--          <el-image-->

      <!--              :src="imgQianZhui+form.hukou.imgMaster"-->
      <!--              :preview-src-list="[imgQianZhui+form.hukou.imgMaster]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>学生所属户主页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.hukou.imgStudent">-->
      <!--          <el-image-->

      <!--              :src="imgQianZhui+form.hukou.imgStudent"-->
      <!--              :preview-src-list="[imgQianZhui+form.hukou.imgStudent]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>学生户口页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.hukou.imgGuardianOneIndex">-->
      <!--          <el-image-->

      <!--              :src="imgQianZhui+form.hukou.imgGuardianOneIndex"-->
      <!--              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianOneIndex]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>监护人1户口本首页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.hukou.imgGuardianOneHukou">-->
      <!--          <el-image-->

      <!--              :src="imgQianZhui+form.hukou.imgGuardianOneHukou"-->
      <!--              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianOneHukou]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>监护人1户口页照片</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.hukou.imgGuardianTwoIndex">-->
      <!--          <el-image-->

      <!--              :src="imgQianZhui+form.hukou.imgGuardianTwoIndex"-->
      <!--              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianTwoIndex]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>监护人2户口本首页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.hukou.imgGuardianTwoHukou">-->
      <!--          <el-image-->

      <!--              :src="imgQianZhui+form.hukou.imgGuardianTwoHukou"-->
      <!--              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianTwoHukou]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>监护人2户口本人页</p>-->
      <!--        </div>-->
      <!--      </div>-->
      <div>
        <ul>
          <viewer :images="hukouImage" class="images" >
            <div  v-for="(o, index) in hukouImage" :key="index" style="padding: 20px">
              <!--                <el-card :body-style="{ padding: '0px' }">-->
              <!--                  <div style="padding: 14px;">-->
              <!--                    <span>{{o.title}}</span>-->
              <!--                  </div>-->
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span>{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <h5 v-if="form.twins!=null">多胞胎</h5>
    <el-table
        v-if="form.twins!=null"
        :data="form.twins"
        style="width: 100%;"
        border
    >
      <el-table-column
          prop="name"
          label="姓名"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="gender"
          label="性别"
          align="center"
      >
        <template slot-scope="scope">
          {{scope.row.gender=='1'?'男':'女'}}
        </template>
      </el-table-column>
    </el-table>

    <h5 v-if="form.guardians!=null">监护人信息</h5>
    <el-table
        v-if="form.guardians!=null"
        :data="form.guardians"
        style="width: 100%;"
        border
    >
      <el-table-column
          prop="guardianName"
          label="姓名"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="birthday"
          label="出生日期"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="nation"
          label="民族"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="relation"
          label="关系"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="phone"
          label="联系电话"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="address"
          label="户籍所在地"
          align="center"
      >
      </el-table-column>
    </el-table>
    <!--    <div class="pic-box"  v-if="form.guardians">-->
    <!--      <div v-if="form.guardians[0]">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.guardians[0].imgIdCard"-->
    <!--            :preview-src-list="[imgQianZhui+form.guardians[0].imgIdCard]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>监护人1身份证照片</p>-->
    <!--      </div>-->
    <!--      <div v-if="form.guardians[1]">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.guardians[1].imgIdCard"-->
    <!--            :preview-src-list="[imgQianZhui+form.guardians[1].imgIdCard]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>监护人2身份证照片</p>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <H5>补充图片</H5>-->
    <!--    <div class="pic-box">-->
    <!--      <div v-if="form.otherImg.imgBirth">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.otherImg.imgBirth"-->
    <!--            :preview-src-list="[imgQianZhui+form.otherImg.imgBirth]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>出生证明材料照片</p>-->
    <!--      </div>-->
    <!--      <div v-if="form.otherImg.imgMarry">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.otherImg.imgMarry"-->
    <!--            :preview-src-list="[imgQianZhui+form.otherImg.imgMarry]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>父母结婚证照片（选填）</p>-->
    <!--      </div>-->
    <!--      <div v-if="form.otherImg.imgVaccines">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.otherImg.imgVaccines"-->
    <!--            :preview-src-list="[imgQianZhui+form.otherImg.imgVaccines]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>疫苗接种证-->
    <!--        </p>-->
    <!--      </div>-->
    <!--      <div v-if="form.otherImg.imgOne">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.otherImg.imgOne"-->
    <!--            :preview-src-list="[imgQianZhui+form.otherImg.imgOne]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>补充材料</p>-->
    <!--      </div>-->
    <!--      <div v-if="form.otherImg.imgTwo">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.otherImg.imgTwo"-->
    <!--            :preview-src-list="[imgQianZhui+form.otherImg.imgTwo]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>补充材料</p>-->
    <!--      </div>-->
    <!--      <div v-if="form.otherImg.imgThree">-->
    <!--        <el-image-->
    <!--            :src="imgQianZhui+form.otherImg.imgThree"-->
    <!--            :preview-src-list="[imgQianZhui+form.otherImg.imgThree]"-->
    <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
    <!--          <i class="el-icon-picture-outline"></i>-->
    <!--        </div> </el-image>-->
    <!--        <p>补充材料</p>-->
    <!--      </div>-->
    <!--    </div>-->
    <div>
      <ul>
        <viewer :images="jianHuRenImage" class="images" >
          <div  v-for="(o, index) in jianHuRenImage" :key="index" style="padding: 20px">
            <!--                <el-card :body-style="{ padding: '0px' }">-->
            <!--                  <div style="padding: 14px;">-->
            <!--                    <span>{{o.title}}</span>-->
            <!--                  </div>-->
            <div v-if="o.path!=''">
              <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
              <div style="padding:10px;">
                <span>{{o.name}}</span>
              </div>
            </div>
            <!--                </el-card>-->
          </div>
        </viewer>
      </ul>
    </div>
    <div v-if="buChongImage.length>0">
      <ul>
        <viewer :images="buChongImage" class="images" >
          <div  v-for="(o, index) in buChongImage" :key="index" style="padding: 20px">
            <!--                <el-card :body-style="{ padding: '0px' }">-->
            <!--                  <div style="padding: 14px;">-->
            <!--                    <span>{{o.title}}</span>-->
            <!--                  </div>-->
            <div v-if="o.path!=''">
              <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
              <div style="padding:10px;">
                <span>{{o.name}}</span>
              </div>
            </div>

            <!--                </el-card>-->
          </div>
        </viewer>
      </ul>
    </div>
    <div v-if="form.house.houseType==1">
      <h5>使用《房屋所有权证》信息报名</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="房屋所有权人姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="所有权人与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="所有权证发放时间">{{form.house.documentTime}}</el-descriptions-item>
        <el-descriptions-item label="房权证号（证件编号">{{form.house.documentNum}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <!--      <div class="pic-box">-->
      <!--        <div v-if="form.house.imgHouse">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgHouse"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgHouse]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>房屋所有权证信息页照片（有具体门牌号页）</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgHouseDoor">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgHouseDoor"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgHouseDoor]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p> 房屋所有权证信息页照片（有房权号页）</p>-->
      <!--        </div>-->
      <!--&lt;!&ndash;        <div v-if="form.house.imgWater">&ndash;&gt;-->
      <!--&lt;!&ndash;          <el-image&ndash;&gt;-->
      <!--&lt;!&ndash;              :src="imgQianZhui+form.house.imgWater"&ndash;&gt;-->
      <!--&lt;!&ndash;              :preview-src-list="[imgQianZhui+form.house.imgWater]"&ndash;&gt;-->
      <!--&lt;!&ndash;          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">&ndash;&gt;-->
      <!--&lt;!&ndash;            <i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;          </div> </el-image>&ndash;&gt;-->
      <!--&lt;!&ndash;          <p>水电费票据</p>&ndash;&gt;-->
      <!--&lt;!&ndash;        </div>&ndash;&gt;-->
      <!--&lt;!&ndash;        <div v-if="form.house.imgInvoice">&ndash;&gt;-->
      <!--&lt;!&ndash;          <el-image&ndash;&gt;-->
      <!--&lt;!&ndash;              :src="imgQianZhui+form.house.imgInvoice"&ndash;&gt;-->
      <!--&lt;!&ndash;              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"&ndash;&gt;-->
      <!--&lt;!&ndash;          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">&ndash;&gt;-->
      <!--&lt;!&ndash;            <i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;          </div> </el-image>&ndash;&gt;-->
      <!--&lt;!&ndash;          <p>购房发票</p>&ndash;&gt;-->
      <!--&lt;!&ndash;        </div>&ndash;&gt;-->
      <!--&lt;!&ndash;        <div v-if="form.house.imgProperty">&ndash;&gt;-->
      <!--&lt;!&ndash;          <el-image&ndash;&gt;-->
      <!--&lt;!&ndash;              :src="imgQianZhui+form.house.imgProperty"&ndash;&gt;-->
      <!--&lt;!&ndash;              :preview-src-list="[imgQianZhui+form.house.imgProperty]"&ndash;&gt;-->
      <!--&lt;!&ndash;          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">&ndash;&gt;-->
      <!--&lt;!&ndash;            <i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;          </div> </el-image>&ndash;&gt;-->
      <!--&lt;!&ndash;          <p>物业费票据</p>&ndash;&gt;-->
      <!--&lt;!&ndash;        </div>&ndash;&gt;-->
      <!--&lt;!&ndash;        <div v-if="form.house.imgGas">&ndash;&gt;-->
      <!--&lt;!&ndash;          <el-image&ndash;&gt;-->
      <!--&lt;!&ndash;              :src="imgQianZhui+form.house.imgGas"&ndash;&gt;-->
      <!--&lt;!&ndash;              :preview-src-list="[imgQianZhui+form.house.imgGas]"&ndash;&gt;-->
      <!--&lt;!&ndash;          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">&ndash;&gt;-->
      <!--&lt;!&ndash;            <i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;          </div> </el-image>&ndash;&gt;-->
      <!--&lt;!&ndash;          <p>燃气费票据</p>&ndash;&gt;-->
      <!--&lt;!&ndash;        </div>&ndash;&gt;-->
      <!--&lt;!&ndash;        <div v-if="form.house.imgHeating">&ndash;&gt;-->
      <!--&lt;!&ndash;          <el-image&ndash;&gt;-->
      <!--&lt;!&ndash;              :src="imgQianZhui+form.house.imgHeating"&ndash;&gt;-->
      <!--&lt;!&ndash;              :preview-src-list="[imgQianZhui+form.house.imgHeating]"&ndash;&gt;-->
      <!--&lt;!&ndash;          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">&ndash;&gt;-->
      <!--&lt;!&ndash;            <i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;          </div> </el-image>&ndash;&gt;-->
      <!--&lt;!&ndash;          <p>取暖费票照片</p>&ndash;&gt;-->
      <!--&lt;!&ndash;        </div>&ndash;&gt;-->
      <!--&lt;!&ndash;        <div v-if="form.house.imgOtherOne">&ndash;&gt;-->
      <!--&lt;!&ndash;          <el-image&ndash;&gt;-->
      <!--&lt;!&ndash;              :src="imgQianZhui+form.house.imgOtherOne"&ndash;&gt;-->
      <!--&lt;!&ndash;              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"&ndash;&gt;-->
      <!--&lt;!&ndash;          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">&ndash;&gt;-->
      <!--&lt;!&ndash;            <i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;          </div> </el-image>&ndash;&gt;-->
      <!--&lt;!&ndash;          <p>补充材料</p>&ndash;&gt;-->
      <!--&lt;!&ndash;        </div>&ndash;&gt;-->
      <!--&lt;!&ndash;        <div v-if="form.house.imgOtherTwo">&ndash;&gt;-->
      <!--&lt;!&ndash;          <el-image&ndash;&gt;-->
      <!--&lt;!&ndash;              :src="imgQianZhui+form.house.imgOtherTwo"&ndash;&gt;-->
      <!--&lt;!&ndash;              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"&ndash;&gt;-->
      <!--&lt;!&ndash;          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">&ndash;&gt;-->
      <!--&lt;!&ndash;            <i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;          </div> </el-image>&ndash;&gt;-->
      <!--&lt;!&ndash;          <p>补充材料</p>&ndash;&gt;-->
      <!--&lt;!&ndash;        </div>&ndash;&gt;-->
      <!--        <div>-->
      <!--          <ul>-->
      <!--            <viewer :images="fangChanZhengImage" class="images" >-->
      <!--              <div  v-for="(o, index) in fangChanZhengImage" :key="index" style="padding: 20px">-->
      <!--                &lt;!&ndash;                <el-card :body-style="{ padding: '0px' }">&ndash;&gt;-->
      <!--                &lt;!&ndash;                  <div style="padding: 14px;">&ndash;&gt;-->
      <!--                &lt;!&ndash;                    <span>{{o.title}}</span>&ndash;&gt;-->
      <!--                &lt;!&ndash;                  </div>&ndash;&gt;-->
      <!--                <img :src="o.path" class="image" :alt="o.title+'-'+o.name" >-->
      <!--                <div style="padding:10px;">-->
      <!--                  <span>{{o.name}}</span>-->
      <!--                </div>-->

      <!--                &lt;!&ndash;                </el-card>&ndash;&gt;-->
      <!--              </div>-->
      <!--            </viewer>-->
      <!--          </ul>-->
      <!--        </div>-->
      <!--      </div>-->
      <div>
        <ul>
          <viewer :images="fangChanZhengImage" class="images" >
            <div  v-for="(o, index) in fangChanZhengImage" :key="index" style="padding: 20px">
              <!--                <el-card :body-style="{ padding: '0px' }">-->
              <!--                  <div style="padding: 14px;">-->
              <!--                    <span>{{o.title}}</span>-->
              <!--                  </div>-->
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span>{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <div v-if="form.house.houseType==2">
      <h5>房产信息-不动产权证书</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="不动产第XXX号">{{form.house.documentNum}}</el-descriptions-item>
        <el-descriptions-item label="房主姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="房主与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="所有权证发放时间">{{form.house.documentTime}}</el-descriptions-item>
        <!--    <el-descriptions-item label="不动产权单元号（选填）">{{form.house.documentTime}}</el-descriptions-item>-->
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <!--        <div v-if="form.house.imgHouse">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgHouse"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgHouse]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>不动产信息图片</p>-->
        <!--        </div>-->
        <!--        <div v-if="form.house.imgWater">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgWater"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgWater]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>水电费票据</p>-->
        <!--        </div>-->
        <!--        <div v-if="form.house.imgInvoice">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgInvoice"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>购房发票</p>-->
        <!--        </div>-->
        <!--        <div v-if="form.house.imgProperty">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgProperty"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgProperty]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>物业费票据</p>-->
        <!--        </div>-->
        <!--        <div v-if="form.house.imgGas">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgGas"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgGas]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>燃气费票据</p>-->
        <!--        </div>-->
        <!--        <div v-if="form.house.imgHeating">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgHeating"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgHeating]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>取暖费票照片</p>-->
        <!--        </div>-->
        <!--        <div v-if="form.house.imgOtherOne">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgOtherOne"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>补充材料</p>-->
        <!--        </div>-->
        <!--        <div v-if="form.house.imgOtherTwo">-->
        <!--          <el-image-->
        <!--              :src="imgQianZhui+form.house.imgOtherTwo"-->
        <!--              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"-->
        <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
        <!--            <i class="el-icon-picture-outline"></i>-->
        <!--          </div> </el-image>-->
        <!--          <p>补充材料</p>-->
        <!--        </div>-->
        <div>
          <ul>
            <viewer :images="fangChanZhengImage" class="images" >
              <div  v-for="(o, index) in fangChanZhengImage" :key="index" style="padding: 20px">
                <!--                <el-card :body-style="{ padding: '0px' }">-->
                <!--                  <div style="padding: 14px;">-->
                <!--                    <span>{{o.title}}</span>-->
                <!--                  </div>-->
                <div v-if="o.path!=''">
                  <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                  <div style="padding:10px;">
                    <span>{{o.name}}</span>
                  </div>
                </div>

                <!--                </el-card>-->
              </div>
            </viewer>
          </ul>
        </div>
      </div>
    </div>
    <div v-if="form.house.houseType==3">
      <h5>房产信息-购房合同（协议）</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="买受人">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label=" 身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="签约时间">{{form.house.documentTime}}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{form.house.documentNum}}</el-descriptions-item>
        <el-descriptions-item label="买受人与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <!--      <div class="pic-box">-->
      <!--        <div v-if="form.house.imgHouse">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgHouse"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgHouse]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>购房合同（封皮)</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgBuyOne">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgBuyOne"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgBuyOne]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>买受人信息所在页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgHouseDoor">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgHouseDoor"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgHouseDoor]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p> 房屋门牌号所在页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgBuyTwo">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgBuyTwo"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgBuyTwo]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>买受人签字盖章页</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgWater">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgWater"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgWater]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>水电费票据</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgInvoice">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgInvoice"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>购房发票</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgProperty">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgProperty"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgProperty]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>物业费票据</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgGas">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgGas"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgGas]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>燃气费票据</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgHeating">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgHeating"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgHeating]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>取暖费票照片</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgOtherOne">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgOtherOne"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>补充材料</p>-->
      <!--        </div>-->
      <!--        <div v-if="form.house.imgOtherTwo">-->
      <!--          <el-image-->
      <!--              :src="imgQianZhui+form.house.imgOtherTwo"-->
      <!--              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"-->
      <!--          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--            <i class="el-icon-picture-outline"></i>-->
      <!--          </div> </el-image>-->
      <!--          <p>补充材料</p>-->
      <!--        </div>-->
      <!--      </div>-->
      <div>
        <ul>
          <viewer :images="fangChanZhengImage" class="images" >
            <div  v-for="(o, index) in fangChanZhengImage" :key="index" style="padding: 20px">
              <!--                <el-card :body-style="{ padding: '0px' }">-->
              <!--                  <div style="padding: 14px;">-->
              <!--                    <span>{{o.title}}</span>-->
              <!--                  </div>-->
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span>{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <div v-if="form.house.houseType==4">
      <h5>小产权房信息报名</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="房主姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="房主与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div>
        <ul>
          <viewer :images="fangChanZhengImage" class="images" >
            <div  v-for="(o, index) in fangChanZhengImage" :key="index" style="padding: 20px">
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span>{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <div v-if="form.house.houseType==5">
      <h5>使用廉租房/公租房信息报名</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="承租人">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="承租人身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="  承租人与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div>
        <ul>
          <viewer :images="fangChanZhengImage" class="images" >
            <div  v-for="(o, index) in fangChanZhengImage" :key="index" style="padding: 20px">
              <!--                <el-card :body-style="{ padding: '0px' }">-->
              <!--                  <div style="padding: 14px;">-->
              <!--                    <span>{{o.title}}</span>-->
              <!--                  </div>-->
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span>{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <div v-if="form.house.houseType==6">
      <h5>房产-自然村</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="房主姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="房主身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label=" 与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div>
        <ul>
          <viewer :images="fangChanZhengImage" class="images" >
            <div  v-for="(o, index) in fangChanZhengImage" :key="index" style="padding: 20px">
              <!--                <el-card :body-style="{ padding: '0px' }">-->
              <!--                  <div style="padding: 14px;">-->
              <!--                    <span>{{o.title}}</span>-->
              <!--                  </div>-->
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span>{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <div >
      <h5 v-if="form.juzhu[0].juzhuCode">外地户籍学生补充信息</h5>
      <el-descriptions  title="" v-if="form.juzhu[0].juzhuCode">
        <el-descriptions-item label="学生居住证编号">{{form.juzhu[0].juzhuCode}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人姓名">{{form.juzhu[0].juzhuName}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人身份证号">{{form.juzhu[0].idCard}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人详细地址">{{form.juzhu[0].rangeName+form.juzhu[0].address}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人与该生关系">{{form.juzhu[0].relation}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions  title="" v-if="form.juzhu[1].juzhuCode">
        <el-descriptions-item label="监护人1居住证编号">{{form.juzhu[1].juzhuCode}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人姓名">{{form.juzhu[1].juzhuName}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人身份证号">{{form.juzhu[1].idCard}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人详细地址">{{form.juzhu[1].rangeName+form.juzhu[1].address}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人与该生关系">{{form.juzhu[1].relation}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions  title="" v-if="form.juzhu[2].juzhuCode">
        <el-descriptions-item label="监护人2居住证编号">{{form.juzhu[2].juzhuCode}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人姓名">{{form.juzhu[2].juzhuName}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人身份证号">{{form.juzhu[2].idCard}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人详细地址">{{form.juzhu[2].rangeName+form.juzhu[2].address}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人与该生关系">{{form.juzhu[2].relation}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.juzhu[1].imgJuzhu">
          <el-image
              :src="imgQianZhui+form.juzhu[1].imgJuzhu"
              :preview-src-list="[imgQianZhui+form.juzhu[1].imgJuzhu]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生监护人1居住证照片</p>
        </div>
        <div v-if="form.juzhu[2].imgJuzhu">
          <el-image
              :src="imgQianZhui+form.juzhu[2].imgJuzhu"
              :preview-src-list="[imgQianZhui+form.juzhu[2].imgJuzhu]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生监护人2居住证照片</p>
        </div>
        <div v-if="form.juzhu[0].imgJuzhu">
          <el-image
              :src="imgQianZhui+form.juzhu[0].imgJuzhu"
              :preview-src-list="[imgQianZhui+form.juzhu[0].imgJuzhu]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生居住证照片</p>
        </div>
      </div>
    </div>
    <el-dialog
        title="不通过原因"
        :visible.sync="dialogVisible"
        align="center"
        width="500px"
        append-to-body
    >
      <div>
        <el-radio-group v-model="status">
          <el-radio :label="2">驳回-修改信息</el-radio>
          <el-radio :label="3">驳回-不可再报</el-radio>
        </el-radio-group>
        <!--        <el-button type="primary" @click="xiuGaiXinXi">驳回-修改信息</el-button>-->
        <!--        <el-button type="info" @click="buKeZaiBao">驳回-驳回-不可再报</el-button>-->
        <el-input type="textarea" placeholder="请输入驳回原因" v-model="reason" style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="shenHeBuTongGuo"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="备注"
        :visible.sync="beiZhuShow"
        align="center"
        width="500px"
        append-to-body
    >
      <div>
        <el-input type="textarea" placeholder="请输备注" v-model="mark" style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="beiZhuAdd"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="调剂"
        :visible.sync="tiaoJiShow"
        width="500px"
        append-to-body
    >
      <div style="display:flex;width: 100%;justify-content: space-between"><span>ID:{{ tiaoJiXinXi.signId }}</span><span>学生姓名:{{tiaoJiXinXi.studentName}}</span><span>报名学校:{{tiaoJiXinXi.schoolName}}</span> </div>
      <p>调剂学校选择：</p>
      <div>
        <el-select
            v-model="tiaoJiXinXi.adjustSchoolId"
            placeholder="选择调剂学校"
            clearable
            filterable
        >
          <el-option
              v-for="item in tiaoJiSchoolList"
              :key="item.schoolId"
              :label="item.schoolName"
              :value="item.schoolId"
          >
          </el-option>
        </el-select>
      </div>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="tiaoJi1"
      >确 定</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  baoMingShenHeBuTongGuo,
  baoMingShenHeTongGuo,
  getshiWuQuDetail, getshiWuQuList,
  getshiWuQuShenHeDetail,
  studentTiaoJiao, tiaoJiaoXueXiaoList
} from "@/api/shiWuQuBaoMingLieBiao";
import {pref} from "@/utils/common";
export default {
  props:{row:[Object], dataRow:[Object]},
  name: "detail",
  data(){
    return{
      id:'',//学生ID
      mark:'',//学生审核通过备注
      status:'',//学生驳回类型
      reason:'',
      boHuiYuanYin:'',
      tiaoJiSchoolList:[],
      tiaoJiXinXi:{},
      tiaoJiShow:false,
      beiZhuShow:false,
      dialogVisible:false,
      prefixCode:this.$store.getters.prefixCode,
      prefixDeptCode: this.$store.getters.deptCode,
      imgQianZhui:`${process.env.VUE_APP_BASE_API}${pref}${this.$store.getters.deptCode}`,
      hukouImage: [],
      jianHuRenImage:[],
      buChongImage:[],
      fangChanZhengImage:[],
      buDongChanImage:[],
      gouFangImage:[],
      xiaoChanQuanImage:[],
      lianZuFagnImage:[],
      ziRanCunImage:[],
      juzhuZhengImage:[],
      role: this.$store.getters.role,
      form:{},
      search:{},
      total:0,
      tableData:[],
      studentId:'', //下一条学生id
      index:0,//下一条学生index值
    }
  },
  created() {
    this.getDetail()
    this.search=this.dataRow
    console.log(this.prefixDeptCode,this.search)
  /*  getshiWuQuList(this.search,this.prefixDeptCode).then(data => {
      this.tableData = data.records
      this.total=Math.ceil(data.total/this.search.pageSize)
      //console.log(this.studentId)

    })*/

  },
  mounted() {
// 获取viewer实例
    const viewer = this.$refs.viewer.viewer;

    // 监听show事件
    viewer.events.on('show', (e) => {
      // 创建一个div作为overlay
      const overlay = document.createElement('div');
      overlay.style.position = 'absolute';
      overlay.style.bottom = '10px';
      overlay.style.left = '10px';
      overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
      overlay.style.color = 'white';
      overlay.style.padding = '5px';
      overlay.textContent = e.image.title; // 设置文字说明

      // 将overlay添加到图片上
      viewer.viewer.appendChild(overlay);
    });
  },
  methods:{
    getDetail(){
      getshiWuQuShenHeDetail({key:this.row.mongoStudentId},this.prefixDeptCode).then(res=>{
        this.form=res
        if(res.hukou.imgIndex){
          this.hukouImage.push({title:'户口信息',path:res.hukou.imgIndex,name:'学生所属户口薄首页'})
        }
        if(res.hukou.imgMaster){
          this.hukouImage.push({title:'户口信息',path:res.hukou.imgMaster,name:'学生所属户主页'})
        }
        if(res.hukou.imgStudent){
          this.hukouImage.push({title:'户口信息',path:res.hukou.imgStudent,name:'学生户口页'})
        }
        if(res.hukou.imgGuardianOneIndex){
          this.hukouImage.push({title:'户口信息',path:res.hukou.imgGuardianOneIndex,name:'监护人1户口本人页'})
        }
        if(res.hukou.imgGuardianOneHukou){
          this.hukouImage.push({title:'户口信息',path:res.hukou.imgGuardianOneHukou,name:'监护人1户口页照片'})
        }
        if(res.hukou.imgGuardianTwoIndex){
          this.hukouImage.push({title:'户口信息',path:res.hukou.imgGuardianTwoIndex,name:'监护人2户口本首页'})
        }
        if(res.hukou.imgGuardianTwoHukou){
          this.hukouImage.push({title:'户口信息',path:res.hukou.imgGuardianTwoHukou,name:'监护人2户口本人页'})
        }
        if(res.guardians){
          if(res.guardians[0].imgIdCard){
            this.jianHuRenImage.push({title:'监护人信息',path:res.guardians[0].imgIdCard,name:'监护人一身份证照片'})
          }
          if(res.guardians.length>=2){
            this.jianHuRenImage.push({title:'监护人信息',path:res.guardians[1].imgIdCard,name:'监护人二身份证照片'})
          }
        }

        if(!res.otherImg){
          this.form.otherImg={
            imgBirth:''
          }
        }
        else {
          if(res.otherImg.imgBirth){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgBirth,name:'出生证明'})
          }

          if(res.otherImg.imgMarry){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgMarry,name:'父母结婚证'})
          }
          if(res.otherImg.imgVaccines){
            this.buChongImage.push({title:'补充图片',path: res.otherImg.imgVaccines,name:'疫苗接种证'})
          }
          if(res.otherImg.imgOne){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgOne,name:'补充材料一'})
          }
          if(res.otherImg.imgTwo){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgTwo,name:'补充材料二'})
          }
          if(res.otherImg.imgThree){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgThree,name:'补充材料三'})
          }
        }
        if(!res.juzhu){
          this.form.juzhu=[{},{},{}]
        }
        else if(res.juzhu.length==1) {
          this.form.juzhu[1]={}
          this.form.juzhu[2]={}
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[0].imgJuzhu,name:'学生个人居住证照片'})
        }else if(res.juzhu.length==2) {
          this.form.juzhu[2]={}
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[0].imgJuzhu,name:'学生个人居住证照片'})
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[1].imgJuzhu,name:'学生监护人1居住证照片'})

        }else {
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[0].imgJuzhu,name:'学生个人居住证照片'})
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[1].imgJuzhu,name:'学生监护人1居住证照片'})
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[2].imgJuzhu,name:'学生监护人2居住证照片'})
        }
        if(!res.house){
          this.form.house={
          }
        }
        else {
          if(res.house.houseType==1){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'房屋所有权证信息页照片（有具体门牌号页）'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouseDoor,name:'房屋所有权证信息页照片（有房权号页）'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==2){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'不动产信息图片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==3){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'购房合同（封皮)'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgBuyOne,name:'买受人信息所在页'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouseDoor,name:'房屋门牌号所在页'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgBuyTwo,name:'买受人签字盖章页'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==4){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'小产权凭证照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==5){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'房屋租赁证照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==6){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'住房证明'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
        }


      })
    },
    tiaoJi() {
      this.tiaoJiXinXi={
        id: this.form.id,
        signId: this.form.signId,
        studentName:this.form.studentName,
        schoolName:this.form.schoolName,
      }
      tiaoJiaoXueXiaoList({key:this.form.id},this.prefixDeptCode).then(res=>{
        this.tiaoJiSchoolList=res
      })
      this.tiaoJiShow=true
    },
    tiaoJi1() {
      this.$confirm('确认要调剂该学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        studentTiaoJiao(this.tiaoJiXinXi,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '调剂成功!'
            })
            this.getForm(this.tiaoJiXinXi.mongoStudentId)
            this.tiaoJiXinXi={}
            this.tiaoJiShow=false
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    buTongGuo() {
      this.dialogVisible=true
    },
    fanHui(){
      if(this.search.pageNumber>this.total){
        this.search.pageNumber=this.search.pageNumber-1
      }

      this.$emit("searchs",this.search)
      this.index=0
    },
    beiZhuAdd(){
      this.$confirm('确认该学生信息无误，审核通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeTongGuo({id:this.form.id,mark:this.mark},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.getForm(this.form.mongoStudentId)
            this.form.mongoStudentId=''
            this.mark=''
            this.beiZhuShow=false

          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });},
    shenHeBuTongGuo(){
      this.$confirm('确认该学生信息无误，审核不通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeBuTongGuo({id:this.form.id,status:this.status,reason:this.reason},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.dialogVisible=false
            this.getForm(this.form.mongoStudentId)
            this.form.mongoStudentId=''
            this.status=''
            this.reason=''
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    tongGuo() {
      if(this.role == 'COUNTY_ADMIN' || this.role == 'AUDITOR'){
        this.$confirm('确认该学生信息无误，审核通过', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          baoMingShenHeTongGuo({id:this.form.id,mark:this.mark},this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '审核成功!'
              });
              this.getForm(this.form.mongoStudentId)
              this.form.mongoStudentId=''
              this.mark=''
              this.beiZhuShow=false

            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.beiZhuShow=true
      }

    },
    quXiao(){
      this.dialogVisible=false
      this.beiZhuShow=false
      this.tiaoJiShow=false
      this.tiaoJiXinXi={}
      this.id=''
      this.mark=''
      this.reason=''
      this.status=''
    },
    xiuGaiXinXi(){
      this.status=2
      this.boHuiYuanYin=1
    },
    buKeZaiBao(){
      this.status=3
      this.boHuiYuanYin=2
    },
    shangYiTiao() {
      if (
          this.index <= this.tableData.length - 1 &&
          this.search.pageNumber >= 1
      ) {
        this.tableData.forEach((element, index) => {
          if (element.id == this.form.id) {
            if (index == 0) {
              this.$message.error("已是第一条数据");
            } else {
              this.studentId = this.tableData[index - 1].mongoStudentId;
              this.index = index - 1;
              this.getForm(this.studentId);
            }
          }
        });
      } else {
        this.search.pageNumber = this.search.pageNumber - 1;
        if (this.search.pageNumber < 1) {
          this.$message.error("已是第一条数据");
        } else {
          this.index = 0;
          // getshiWuQuList(this.search,this.prefixDeptCode).then((res) => {
          //   this.tableData = res.data.list;
          //   this.studentId = this.tableData[this.tableData.length - 1].id;
          //   this.index = 0;
          //   this.getForm(this.studentId);
          // });
        }
      }
    },
    xiaYiTiao() {
      // console.log('下一条',this.tableData.length,this.search,this.total)
      if(this.index<this.tableData.length-1&&this.search.pageNumber<=this.total){
        this.tableData.forEach((element,index)=>{
          if(element.id ==this.form.id){
            if(index==this.tableData.length-1){
              this.$message.error("已是最后一条数据")
            }else {
              this.studentId=this.tableData[index+1].mongoStudentId
              this.index=index+1
              this.getForm(this.studentId)
              //console.log(this.studentId)
            }

          }
        })
      }else {
        if(this.search.pageNumber<this.total){
          this.search.pageNumber=this.search.pageNumber+1
          if(this.search.pageNumber>this.total){
            this.$message.error("已是最后一条数据")
          }else {this.index=0
            // getshiWuQuList(this.search,this.prefixDeptCode)
            //     .then(data => {
            //           this.tableData = data.records
            //           this.studentId=this.tableData[0].id
            //           this.index=0
            //           this.total=Math.ceil(data.total/this.search.pageSize)
            //           this.getForm(this.studentId)
            //           //console.log(this.studentId)
            //
            //         }
            //     )
          }
        }else {
          this.$message.error("已是最后一条数据")
        }


      }
    },
    getForm(row){
      this.myWorksImgsArray=[]
      this.FormList=[]
      getshiWuQuShenHeDetail({key:row},this.prefixDeptCode).then(res=>{
        this.form=res

        if(res.guardians){
          if(res.guardians[0].imgIdCard){
            this.jianHuRenImage.push({title:'监护人信息',path:res.guardians[0].imgIdCard,name:'监护人一身份证照片'})
          }
          if(res.guardians.length>=2){
            this.jianHuRenImage.push({title:'监护人信息',path:res.guardians[1].imgIdCard,name:'监护人二身份证照片'})
          }
        }
        if(!res.otherImg){
          this.form.otherImg={
            imgBirth:''
          }
        }else {
          if(res.otherImg.imgBirth){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgBirth,name:'出生证明'})
          }

          if(res.otherImg.imgMarry){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgMarry,name:'父母结婚证'})
          }
          if(res.otherImg.imgVaccines){
            this.buChongImage.push({title:'补充图片',path: res.otherImg.imgVaccines,name:'疫苗接种证'})
          }
          if(res.otherImg.imgOne){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgOne,name:'补充材料一'})
          }
          if(res.otherImg.imgTwo){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgTwo,name:'补充材料二'})
          }
          if(res.otherImg.imgThree){
            this.buChongImage.push({title:'补充图片',path:res.otherImg.imgThree,name:'补充材料三'})
          }
        }
        if(!res.juzhu){
          this.form.juzhu=[{},{},{}]
        }else if(res.juzhu.length==1) {
          this.form.juzhu[1]={}
          this.form.juzhu[2]={}
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[0].imgJuzhu,name:'学生个人居住证照片'})
        }else if(res.juzhu.length==2) {
          this.form.juzhu[2]={}
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[0].imgJuzhu,name:'学生个人居住证照片'})
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[1].imgJuzhu,name:'学生监护人1居住证照片'})

        }else {
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[0].imgJuzhu,name:'学生个人居住证照片'})
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[1].imgJuzhu,name:'学生监护人1居住证照片'})
          this.juzhuZhengImage.push({title:'居住证',path:res.juzhu[2].imgJuzhu,name:'学生监护人2居住证照片'})
        }
        if(!res.house){
          this.form.house={
          }
        }else {
          if(res.house.houseType==1){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'房屋所有权证信息页照片（有具体门牌号页）'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouseDoor,name:'房屋所有权证信息页照片（有房权号页）'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==2){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'不动产信息图片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==3){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'购房合同（封皮)'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgBuyOne,name:'买受人信息所在页'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouseDoor,name:'房屋门牌号所在页'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgBuyTwo,name:'买受人签字盖章页'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==4){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'小产权凭证照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==5){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'房屋租赁证照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
          if(res.house.houseType==6){
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHouse,name:'住房证明'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgWater,name:'水电费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgInvoice,name:'购房发票'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgProperty,name:'物业费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgGas,name:'燃气费票据'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgHeating,name:'取暖费票照片'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherOne,name:'补充材料'})
            this.fangChanZhengImage.push({title:'房产信息',path:res.house.imgOtherTwo,name:'补充材料'})
          }
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
.image {
  width: 100%;
  height:80px;
  //display: block;
}
.images {
  display: grid;
  grid-template-columns: 150px 150px 150px 150px;
  grid-template-rows: 120px 120px 120px;
}
.pic-box {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  .el-image {
    width: 150px;
    height: 100px;
    border: 1px solid #dadada;
    border-radius: 4px;
  }
  p {
    text-align: center;
  }
}
</style>
