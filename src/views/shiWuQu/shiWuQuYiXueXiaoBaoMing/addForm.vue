<template>
  <div class="county-primary">
    <el-select
        filterable
        v-model="form.schoolId"
        placeholder="选择报名学校"
        @change="getGradeList"
        v-if="role != 'SCHOOL'"
    >
      <el-option
          v-for="item in schoolList"
          :key="item.id"
          :label="item.deptName"
          :value="item.id"
      >
      </el-option>
    </el-select>
    <el-form :model="form" label-width="140px" ref="form" :rules="rules" class="demo-form-inline">
      <div class="ad-form-header">基本信息</div>
      <div class="flex-start ad-form-wrap">
        <el-form-item label="姓名" prop="studentName" >
          <el-input v-model="form.studentName" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号码"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select
              v-model="form.gender"
              clearable
              placeholder="请选择性别"
          >
            <el-option
                v-for="item in xingBieList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出生日期" prop="birthday">
          <el-date-picker
              v-model="form.birthday"
              clearable
              type="date"
              format="yyyy-MM-dd"
              placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="户口性质" prop="huokouType">
          <el-select
              v-model="form.huokouType"
              placeholder="请选择户口性质"
              clearable
          >
            <el-option
                v-for="item in huKouList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="民族" prop="nation">
          <el-input v-model="form.nation" placeholder="请输入民族"></el-input>
        </el-form-item>
        <el-form-item label="是否优抚对象" prop="careStatus">
          <el-select
              v-model="form.careStatus"
              placeholder="请选择是否优抚对象"
          >
            <el-option
                v-for="item in youFuDuiXiangList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="ad-form-header">户口信息</div>
      <div class="flex-start ad-form-wrap">
        <el-form-item label="户主姓名" prop="hukou.masterName" >
          <el-input v-model="form.hukou.masterName" placeholder="请输入户主姓名" ></el-input>
        </el-form-item>
        <el-form-item label="户主身份证号" prop="hukou.masterIdCard">
          <el-input v-model="form.hukou.masterIdCard" placeholder="请输入户主身份证号"></el-input>
        </el-form-item>
        <el-form-item label="户号" prop="hukou.houseNum">
          <el-input v-model="form.hukou.houseNum" placeholder="请输入户号"></el-input>
        </el-form-item>
        <el-form-item label="户主与该生关系" prop="hukou.relation">
          <el-select
              v-model="form.hukou.relation"
              placeholder="请选择户主与该生关系"
          >
            <el-option
                v-for="item in gaiShengGuanXiList"
                :key="item.value"
                :label="item.label"
                :value="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="户本登记地址" prop="hukou.address">
          <el-select
              v-model="form.hukou.rangeName"
              placeholder="选择户口地址"
              clearable
              filterable
              v-if="form.huokouType == 1"
          >
            <el-option
                v-for="item in optionsList"
                :key="item.rangeName"
                :label="item.rangeName"
                :value="item.rangeName"
            >
            </el-option>
          </el-select>
          <el-input v-model="form.hukou.address" placeholder="请输入户本登记地址"></el-input>
        </el-form-item>
      </div>
      <div>
        <el-row>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">

              <UploadPic
                  textPic="学生所属户口薄首页"
                  v-model="form.hukou.imgIndex"
                  :required="true"
                  @onChange="onChange('hukou.imgIndex')"
                  @onRemove="onRemove('hukou.imgIndex')"
              />
            </el-form-item>
          </el-col>
          <!--          <el-col :span="4">-->
          <!--            <el-form-item   prop="" label-width="0">-->

          <!--              <UploadPic-->
          <!--                  textPic="学生所属户口薄首页"-->
          <!--                  v-model="form.hukou.companyImg"-->
          <!--                  :required="true"-->
          <!--                  @onChange="onChange('imageInfo.companyImg')"-->
          <!--                  @onRemove="onRemove('imageInfo.companyImg')"-->
          <!--              />-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">

              <UploadPic
                  textPic="学生所属户口主页"
                  v-model="form.hukou.imgMaster"
                  :required="true"
                  @onChange="onChange('hukou.imgMaster')"
                  @onRemove="onRemove('hukou.imgMaster')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">

              <UploadPic

                  textPic="学生户口页"
                  v-model="form.hukou.imgStudent"
                  :required="true"
                  @onChange="onChange('hukou.imgStudent')"
                  @onRemove="onRemove('hukou.imgStudent')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">
              <UploadPic
                  textPic="监护人1户口本首页"
                  v-model="form.hukou.imgGuardianOneIndex"
                  :required="true"
                  @onChange="onChange('hukou.imgGuardianOneIndex')"
                  @onRemove="onRemove('hukou.imgGuardianOneIndex')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">
              <UploadPic
                  textPic="监护人1户口本人页"
                  v-model="form.hukou.imgGuardianOneHukou"
                  :required="true"
                  @onChange="onChange('hukou.imgGuardianOneHukou')"
                  @onRemove="onRemove('hukou.imgGuardianOneHukou')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">
              <UploadPic
                  textPic="监护人2户口本首页"
                  v-model="form.hukou.imgGuardianTwoIndex"
                  :required="false"
                  @onChange="onChange('hukou.imgGuardianTwoIndex')"
                  @onRemove="onRemove('hukou.imgGuardianTwoIndex')"
              />
            </el-form-item>
          </el-col>
          <!--          <el-col :span="4">-->
          <!--          -->
          <!--          </el-col>-->
        </el-row>
        <el-form-item   prop="" label-width="0">
          <UploadPic
              textPic="监护人2户口本人页"
              v-model="form.hukou.imgGuardianTwoHukou"
              :required="false"
              @onChange="onChange('hukou.imgGuardianTwoHukou')"
              @onRemove="onRemove('hukou.imgGuardianTwoHukou')"
          />
        </el-form-item>
        <div style="color: red">
          注：
          <p>1.所有项均为必填项，请核查后仔细填写，确保无误！</p>
          <p>2.上传的照片大小应小于6M！</p>
          <p>3.监护人户口页上传监护人1必填</p>
        </div>
        <div class="ad-form-header">监护人信息</div>
        <div>监护人一:</div>
        <div style="display: flex">
          <div style="flex: 1;" >
            <UploadPic
                textPic="监护人1身份证照片"
                v-model="form.jianHuRen1.imgIdCard"
                :required="true"
                @onChange="onChange('jianHuRen1.imgIdCard')"
                @onRemove="onRemove('jianHuRen1.imgIdCard')"
            />
          </div>
          <div style="flex: 2">
            <el-form-item label="姓名" prop="jianHuRen1.guardianName">
              <el-input v-model="form.jianHuRen1.guardianName" placeholder="请输入姓名" style="width: 215px" @change="sfzxx"></el-input>
            </el-form-item>
            <el-form-item label="性别" prop="jianHuRen1.gender">
              <el-input v-model="form.jianHuRen1.gender" placeholder="请输入性别"  style="width: 215px" @input="change()"></el-input>
            </el-form-item>
            <el-form-item label="民族" prop="jianHuRen1.nation">
              <el-input v-model="form.jianHuRen1.nation" style="width: 215px" placeholder="请输入民族" @input="change()"></el-input>
            </el-form-item>
            <el-form-item label="出生日期" prop="jianHuRen1.birthday" >
              <el-date-picker
                  @input="change()"
                  v-model="form.jianHuRen1.birthday"
                  clearable
                  type="date"
                  format="yyyy-MM-dd"
                  placeholder="请选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="住址" prop="jianHuRen1.address">
              <el-input @input="change()" v-model="form.jianHuRen1.address" style="width: 215px"></el-input>
            </el-form-item>
            <el-form-item label="公民身份证号" prop="jianHuRen1.idCard">
              <el-input @input="change()" v-model="form.jianHuRen1.idCard" style="width: 215px"></el-input>
            </el-form-item>
            <el-form-item label="与学生关系" prop="jianHuRen1.relation">
              <el-select
                  v-model="form.jianHuRen1.relation"
                  placeholder="请选择与学生关系"
                  style="width: 215px"
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="监护人1手机号" prop="jianHuRen1.phone">
              <el-input @input="change()" v-model="form.jianHuRen1.phone" style="width: 215px"></el-input>
            </el-form-item>

          </div>
        </div>
        <div>监护人二:</div>
        <div style="display: flex">
          <div style="flex: 1">
            <UploadPic
                textPic="监护人2身份证照片"
                v-model="form.guardians[1].imgIdCard"
                :required="false"
                @onChange="onChange('guardians[1].imgIdCard')"
                @onRemove="onRemove('guardians[1].imgIdCard')"
            />
          </div>
          <div style="flex: 2">
            <el-form-item label="姓名">
              <el-input  v-model="form.guardians[1].guardianName" placeholder="请输入姓名" style="width: 215px" @input="change()"></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <el-input v-model="form.guardians[1].gender" placeholder="请输入性别" style="width: 215px" @input="change()"></el-input>
            </el-form-item>
            <el-form-item label="民族">
              <el-input  v-model="form.guardians[1].nation" style="width: 215px" placeholder="请输入民族" @input="change()"></el-input>
            </el-form-item>
            <el-form-item label="出生日期">
              <el-date-picker
                  @input="change()"
                  v-model="form.guardians[1].birthday"
                  clearable
                  type="date"
                  format="yyyy-MM-dd"
                  placeholder="请选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="住址">
              <el-input @input="change()" v-model="form.guardians[1].address" style="width: 215px"></el-input>
            </el-form-item>
            <el-form-item label="公民身份证号">
              <el-input @input="change()" v-model="form.guardians[1].idCard" style="width: 215px"></el-input>
            </el-form-item>
            <el-form-item label="与学生关系">
              <el-select
                  v-model="form.guardians[1].relation"
                  placeholder="请选择与学生关系"
                  style="width: 215px"
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="监护人2手机号">
              <el-input v-model="form.guardians[1].phone" style="width: 215px"></el-input>
            </el-form-item>
            <!--            <el-form-item >-->
            <!--              <UploadPic-->
            <!--                  textPic="监护人2身份证照片"-->
            <!--                  v-model="form.guardians[1].imgIdCard"-->
            <!--                  :required="true"-->
            <!--                  @onChange="onChange('guardians[1].imgIdCard')"-->
            <!--                  @onRemove="onRemove('guardians[1].imgIdCard')"-->
            <!--              />-->
            <!--            </el-form-item>-->
          </div>
        </div>
        <div class="ad-form-header">补充图片</div>
        <el-row>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">

              <UploadPic
                  textPic="出生证明材料"
                  v-model="form.otherImg.imgBirth"
                  :required="false"
                  @onChange="onChange('otherImg.imgBirth')"
                  @onRemove="onRemove('otherImg.imgBirth')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">

              <UploadPic
                  textPic="父母结婚证照片"
                  v-model="form.otherImg.imgMarry"
                  :required="false"
                  @onChange="onChange('otherImg.imgMarry')"
                  @onRemove="onRemove('otherImg.imgMarry')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">

              <UploadPic
                  textPic="疫苗接种证"
                  v-model="form.otherImg.imgVaccines"
                  :required="false"
                  @onChange="onChange('otherImg.imgVaccines')"
                  @onRemove="onRemove('otherImg.imgVaccines')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">

              <UploadPic

                  textPic="补充材料"
                  v-model="form.otherImg.imgOne"
                  :required="false"
                  @onChange="onChange('otherImg.imgOne')"
                  @onRemove="onRemove('otherImg.imgOne')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">
              <UploadPic
                  textPic="补充材料"
                  v-model="form.otherImg.imgTwo"
                  :required="false"
                  @onChange="onChange('otherImg.imgTwo')"
                  @onRemove="onRemove('otherImg.imgTwo')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item   prop="" label-width="0">
              <UploadPic
                  textPic=" 补充材料"
                  v-model="form.otherImg.imgThree"
                  :required="false"
                  @onChange="onChange('otherImg.imgThree')"
                  @onRemove="onRemove('otherImg.imgThree')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="ad-form-header">多胞胎信息</div>
      <div v-for="(item,index) in form.twins" :key="index">
        <el-form-item label="学生姓名">
          <el-input v-model="form.twins[index].name"></el-input>
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input v-model="form.twins[index].idCard"></el-input>
        </el-form-item>
        <el-form-item label="性别">
          <el-radio v-model="form.twins[index].gender" label="1">男</el-radio>
          <el-radio v-model="form.twins[index].gender" label="2">女</el-radio>
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" @click="addDuoBaoTai">新增</el-button>
        <el-button type="primary" @click="delDuoBaoTai">删除</el-button>
      </div>
      <div class="ad-form-header" style="margin-top: 20px">
        <el-checkbox-group v-model="fangWuCheckList" style="display: inline">
          <el-checkbox label="房屋所有权信息" style="color: black;"></el-checkbox>
        </el-checkbox-group> </div>
      <div v-if="fangWuCheckList[0]=='房屋所有权信息'">
        <el-form-item label="房产类型" prop="fangChanId" :rules="fangWuCheckList[0]=='房屋所有权信息'?[{
          type: 'string',
          required: true,
          message: '请选择房产类型',
          trigger: ['blur', 'change']
        }]:{}" >
          <el-select
              v-model="form.fangChanId"
              placeholder="请选择房产类型"
              clearable
              filterable
              @change="fanChanChange"
          >
            <el-option
                v-for="item in fangChanNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <div>
          <div  v-if="form.fangChanId=='1'" >
            <el-form-item label="房屋所有权姓名" prop="house.ownerName">
              <el-input v-model="form.house.ownerName" placeholder="请输入房屋所有权姓名"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="身份证号" prop="house.idCard">
              <el-input v-model="form.house.idCard" placeholder="请输入身份证号"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="所有权发证时间" :rules="activeName==1?[{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        }]:{}" prop="house.documentTime">
              <el-date-picker
                  @input="input"
                  v-model="form.house.documentTime"
                  clearable
                  type="date"
                  placeholder="请选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="房权证号（证件编号）" prop="house.documentNum" :rules="activeName==1?[{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        }]:{}" >
              <el-input v-model="form.house.documentNum" placeholder="请输入证件编号"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="所有权与该生关系" prop="house.relation">
              <el-select
                  @input="input"
                  style="width: 210px"
                  v-model="form.house.relation"
                  placeholder="请选择与该生关系"
                  clearable
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="房屋详细地址" prop="house.rangeName">
              <el-select
                  @change="cunChu"
                  style="width: 300px;margin-right: 20px"
                  v-model="form.house.rangeName"
                  placeholder="选择房屋地址-可模糊搜索"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in optionsList"
                    :key="item.rangeName"
                    :label="item.rangeName"
                    :value="item.rangeName"
                >
                </el-option>
              </el-select>
              <el-input  style="width: 300px" v-model="form.house.address" placeholder="请输入房屋详细地址"></el-input>
            </el-form-item>
            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="房屋所有权证信息页照片（有具体门牌号页）"
                      v-model="form.house.imgHouseDoor"
                      :required="true"
                      @onChange="onChange('house.imgHouseDoor')"
                      @onRemove="onRemove('house.imgHouseDoor')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="房屋所有权证信息页照片（有房权号页）"
                      v-model="form.house.imgHouse"
                      :required="true"
                      @onChange="onChange('house.imgHouse')"
                      @onRemove="onRemove('house.imgHouse')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="水电费票据"
                      v-model="form.house.imgWater"
                      :required="false"
                      @onChange="onChange('house.imgWater')"
                      @onRemove="onRemove('house.imgWater')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="购房发票"
                      v-model="form.house.imgInvoice"
                      :required="false"
                      @onChange="onChange('house.imgInvoice')"
                      @onRemove="onRemove('house.imgInvoice')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="物业费票据"
                      v-model="form.house.imgProperty"
                      :required="false"
                      @onChange="onChange('house.imgProperty')"
                      @onRemove="onRemove('house.imgProperty')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="燃气费票据"
                      v-model="form.house.imgGas"
                      :required="false"
                      @onChange="onChange('house.imgGas')"
                      @onRemove="onRemove('house.imgGas')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="取暖费票照片"
                      v-model="form.house.imgHeating"
                      :required="false"
                      @onChange="onChange('house.imgHeating')"
                      @onRemove="onRemove('house.imgHeating')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic=" 补充材料"
                      v-model="form.house.imgOtherOne"
                      :required="false"
                      @onChange="onChange('house.imgOtherOne')"
                      @onRemove="onRemove('house.imgOtherOne')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div v-if="form.fangChanId=='2'" >
            <el-form-item label="权利人姓名" prop="house.ownerName">
              <el-input v-model="form.house.ownerName" placeholder="请输入房屋权利人姓名"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="身份证号" prop="house.idCard">
              <el-input v-model="form.house.idCard" placeholder="请输入身份证号"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="所有权发证时间" :rules="activeName==2?[{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        }]:{}" prop="house.documentTime">
              <el-date-picker
                  @input="input"
                  v-model="form.house.documentTime"
                  clearable
                  type="date"
                  placeholder="请选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="不动产证号" :rules="activeName==2?[{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        }]:{}" prop="house.documentNum">
              <el-input v-model="form.house.documentNum" placeholder="不动产证号"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="所有权与该生关系" prop="house.relation">
              <el-select
                  @input="input"
                  style="width: 210px"
                  v-model="form.house.relation"
                  placeholder="请选择与该生关系"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="房屋详细地址" prop="house.rangeName">
              <el-select
                  @input="input"
                  style="width: 300px;margin-right: 20px"
                  v-model="form.house.rangeName"
                  placeholder="选择房屋地址-可模糊搜索"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in optionsList"
                    :key="item.rangeName"
                    :label="item.rangeName"
                    :value="item.rangeName"
                >
                </el-option>
              </el-select>
              <el-input  style="width: 300px" v-model="form.house.address" placeholder="请输入房屋详细地址"></el-input>
            </el-form-item>
            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="房屋所有权证信息页照片"
                      v-model="form.house.imgHouse"
                      :required="true"
                      @onChange="onChange('imageInfo.companyImg')"
                      @onRemove="onRemove('imageInfo.companyImg')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="水电费票据"
                      v-model="form.house.imgWater"
                      :required="false"
                      @onChange="onChange('house.imgWater')"
                      @onRemove="onRemove('house.imgWater')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="购房发票"
                      v-model="form.house.imgInvoice"
                      :required="false"
                      @onChange="onChange('house.imgInvoice')"
                      @onRemove="onRemove('house.imgInvoice')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="物业费票据"
                      v-model="form.house.imgProperty"
                      :required="false"
                      @onChange="onChange('house.imgProperty')"
                      @onRemove="onRemove('house.imgProperty')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="燃气费票据"
                      v-model="form.house.imgGas"
                      :required="false"
                      @onChange="onChange('house.imgGas')"
                      @onRemove="onRemove('house.imgGas')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="取暖费票照片"
                      v-model="form.house.imgHeating"
                      :required="false"
                      @onChange="onChange('house.imgHeating')"
                      @onRemove="onRemove('house.imgHeating')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic=" 补充材料"
                      v-model="form.house.imgOtherOne"
                      :required="false"
                      @onChange="onChange('house.imgOtherOne')"
                      @onRemove="onRemove('house.imgOtherOne')"
                  />
                </el-form-item>
              </el-col>
            </el-row></div>
          <div  v-if="form.fangChanId=='3'" >
            <el-form-item label="买受人" prop="house.ownerName">
              <el-input v-model="form.house.ownerName" placeholder="请输入买受人姓名"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="身份证号" prop="house.idCard">
              <el-input v-model="form.house.idCard" placeholder="请输入身份证号"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="签约时间" prop="house.documentTime" :rules="activeName==3?[{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        }]:{}">
              <el-date-picker
                  @change="cunChu"
                  v-model="form.house.documentTime"
                  clearable
                  type="date"
                  placeholder="请选择签约日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label=" 合同编号" :rules="activeName==3?[{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        }]:{}" prop="house.documentNum">
              <el-input v-model="form.house.documentNum" placeholder="请输入合同编号"  @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="买受人与该生关系" prop="house.relation">
              <el-select
                  @change="cunChu"
                  style="width: 210px"
                  v-model="form.house.relation"
                  placeholder="请选择与该生关系"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="房屋详细地址" prop="house.rangeName">
              <el-select
                  @change="cunChu"
                  style="width: 300px;margin-right: 20px"
                  v-model="form.house.rangeName"
                  placeholder="选择房屋地址-可模糊搜索"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in optionsList"
                    :key="item.rangeName"
                    :label="item.rangeName"
                    :value="item.rangeName"
                >
                </el-option>
              </el-select>
              <el-input  style="width: 300px" v-model="form.house.address" placeholder="请输入房屋详细地址"  @change="cunChu"></el-input>
            </el-form-item>
            <el-row>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="购房合同（封皮)"
                      v-model="form.house.imgHouse"
                      :required="true"
                      @onChange="onChange('house.imgHouse')"
                      @onRemove="onRemove('house.imgHouse')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="买受人信息所在页"
                      v-model="form.house.imgBuyOne"
                      :required="true"
                      @onChange="onChange('house.imgBuyOne')"
                      @onRemove="onRemove('house.imgBuyOne')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="房屋门牌号所在页"
                      v-model="form.house.imgHouseDoor"
                      :required="true"
                      @onChange="onChange('house.imgHouseDoor')"
                      @onRemove="onRemove('house.imgHouseDoor')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="买受人签字盖章页"
                      v-model="form.house.imgBuyTwo"
                      :required="true"
                      @onChange="onChange('house.imgBuyTwo')"
                      @onRemove="onRemove('house.imgBuyTwo')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="水电费票据"
                      v-model="form.house.imgWater"
                      :required="false"
                      @onChange="onChange('house.imgWater')"
                      @onRemove="onRemove('house.imgWater')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="购房发票"
                      v-model="form.house.imgInvoice"
                      :required="false"
                      @onChange="onChange('house.imgInvoice')"
                      @onRemove="onRemove('house.imgInvoice')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="物业费票据"
                      v-model="form.house.imgProperty"
                      :required="false"
                      @onChange="onChange('house.imgProperty')"
                      @onRemove="onRemove('house.imgProperty')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="燃气费票据"
                      v-model="form.house.imgGas"
                      :required="false"
                      @onChange="onChange('house.imgGas')"
                      @onRemove="onRemove('house.imgGas')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="取暖费票照片"
                      v-model="form.house.imgHeating"
                      :required="false"
                      @onChange="onChange('house.imgHeating')"
                      @onRemove="onRemove('house.imgHeating')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic=" 补充材料"
                      v-model="form.house.imgOtherOne"
                      :required="false"
                      @onChange="onChange('house.imgOtherOne')"
                      @onRemove="onRemove('house.imgOtherOne')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div  v-if="form.fangChanId=='4'" >
            <el-form-item label="房主姓名" prop="house.ownerName">
              <el-input v-model="form.house.ownerName" placeholder="请输入房屋房屋姓名" @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="身份证号" prop="house.idCard">
              <el-input v-model="form.house.idCard" placeholder="请输入身份证号" @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="与该生关系" prop="house.relation">
              <el-select
                  @change="cunChu"
                  style="width: 210px"
                  v-model="form.house.relation"
                  placeholder="请选择与该生关系"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="房屋详细地址" prop="house.rangeName">
              <el-select
                  @change="cunChu"
                  style="width: 300px;margin-right: 20px"
                  v-model="form.house.rangeName"
                  placeholder="选择房屋地址-可模糊搜索"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in optionsList"
                    :key="item.rangeName"
                    :label="item.rangeName"
                    :value="item.rangeName"
                >
                </el-option>
              </el-select>
              <el-input  style="width: 300px" v-model="form.house.address" placeholder="请输入房屋详细地址"    @change="cunChu"></el-input>
            </el-form-item>
            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="小产权凭证照片"
                      :required="true"
                      v-model="form.house.imgHouse"
                      @onChange="onChange('house.imgHouse')"
                      @onRemove="onRemove('house.imgHouse')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="水电费票据"
                      v-model="form.house.imgWater"
                      :required="false"
                      @onChange="onChange('house.imgWater')"
                      @onRemove="onRemove('house.imgWater')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="购房发票"
                      v-model="form.house.imgInvoice"
                      :required="false"
                      @onChange="onChange('house.imgInvoice')"
                      @onRemove="onRemove('house.imgInvoice')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="物业费票据"
                      v-model="form.house.imgProperty"
                      :required="false"
                      @onChange="onChange('house.imgProperty')"
                      @onRemove="onRemove('house.imgProperty')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="燃气费票据"
                      v-model="form.house.imgGas"
                      :required="false"
                      @onChange="onChange('house.imgGas')"
                      @onRemove="onRemove('house.imgGas')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="取暖费票照片"
                      v-model="form.house.imgHeating"
                      :required="false"
                      @onChange="onChange('house.imgHeating')"
                      @onRemove="onRemove('house.imgHeating')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic=" 补充材料"
                      v-model="form.house.imgOtherOne"
                      :required="false"
                      @onChange="onChange('house.imgOtherOne')"
                      @onRemove="onRemove('house.imgOtherOne')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div  v-if="form.fangChanId=='5'" >
            <el-form-item label="承租人" prop="house.ownerName">
              <el-input v-model="form.house.ownerName" placeholder="请输入承租人姓名" @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="承租人身份证号" prop="house.idCard">
              <el-input v-model="form.house.idCard" placeholder="请输入承租人身份证号" @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="承租人与该生关系" prop="house.relation">
              <el-select
                  @change="cunChu"
                  style="width: 210px"
                  v-model="form.house.relation"
                  placeholder="请选择与该生关系"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="房屋详细地址" prop="house.rangeName">
              <el-select
                  @change="cunChu"
                  style="width: 300px;margin-right: 20px"
                  v-model="form.house.rangeName"
                  placeholder="选择房屋地址-可模糊搜索"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in optionsList"
                    :key="item.rangeName"
                    :label="item.rangeName"
                    :value="item.rangeName"
                >
                </el-option>
              </el-select>
              <el-input  style="width: 300px" v-model="form.house.address" placeholder="请输入房屋详细地址" @change="cunChu"></el-input>
            </el-form-item>
            <el-row>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="房屋租赁凭证照片"
                      v-model="form.house.imgHouse"
                      :required="true"
                      @onChange="onChange('house.imgHouse')"
                      @onRemove="onRemove('house.imgHouse')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="水电费票据"
                      v-model="form.house.imgWater"
                      :required="false"
                      @onChange="onChange('house.imgWater')"
                      @onRemove="onRemove('house.imgWater')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @change="cunChu"
                      textPic="购房发票"
                      v-model="form.house.imgInvoice"
                      :required="false"
                      @onChange="onChange('house.imgInvoice')"
                      @onRemove="onRemove('house.imgInvoice')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="物业费票据"
                      v-model="form.house.imgProperty"
                      :required="false"
                      @onChange="onChange('house.imgProperty')"
                      @onRemove="onRemove('house.imgProperty')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="燃气费票据"
                      v-model="form.house.imgGas"
                      :required="false"
                      @onChange="onChange('house.imgGas')"
                      @onRemove="onRemove('house.imgGas')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic="取暖费票照片"
                      v-model="form.house.imgHeating"
                      :required="false"
                      @onChange="onChange('house.imgHeating')"
                      @onRemove="onRemove('house.imgHeating')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @change="cunChu"
                      textPic=" 补充材料"
                      v-model="form.house.imgOtherOne"
                      :required="false"
                      @onChange="onChange('house.imgOtherOne')"
                      @onRemove="onRemove('house.imgOtherOne')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div  v-if="form.fangChanId=='6'" >
            <el-form-item label="房主姓名" prop="house.ownerName">
              <el-input v-model="form.house.ownerName" placeholder="请输入房主姓名" @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="房主身份证号" prop="house.idCard">
              <el-input v-model="form.house.idCard" placeholder="请输入房主身份证号" @change="cunChu"></el-input>
            </el-form-item>
            <el-form-item label="所有权与该生关系" prop="house.relation">
              <el-select
                  @input="input"
                  style="width: 210px"
                  v-model="form.house.relation"
                  placeholder="请选择与该生关系"
                  clearable
              >
                <el-option
                    v-for="item in gaiShengGuanXiList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="房屋详细地址" prop="house.rangeName">
              <el-select
                  @input="input"
                  style="width: 300px;margin-right: 20px"
                  v-model="form.house.rangeName"
                  placeholder="选择房屋地址-可模糊搜索"
                  clearable
                  filterable
              >
                <el-option
                    v-for="item in optionsList"
                    :key="item.rangeName"
                    :label="item.rangeName"
                    :value="item.rangeName"
                >
                </el-option>
              </el-select>
              <el-input  style="width: 300px" v-model="form.house.address" placeholder="请输入房屋详细地址" @change="cunChu"></el-input>
            </el-form-item>
            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="住房证明照片"
                      v-model="form.house.imgHouse"
                      :required="true"
                      @onChange="onChange('house.imgHouse')"
                      @onRemove="onRemove('house.imgHouse')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="水电费票据"
                      v-model="form.house.imgWater"
                      :required="false"
                      @onChange="onChange('house.imgWater')"
                      @onRemove="onRemove('house.imgWater')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">

                  <UploadPic
                      @input="input"
                      textPic="购房发票"
                      v-model="form.house.imgInvoice"
                      :required="false"
                      @onChange="onChange('house.imgInvoice')"
                      @onRemove="onRemove('house.imgInvoice')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="物业费票据"
                      v-model="form.house.imgProperty"
                      :required="false"
                      @onChange="onChange('house.imgProperty')"
                      @onRemove="onRemove('house.imgProperty')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="燃气费票据"
                      v-model="form.house.imgGas"
                      :required="false"
                      @onChange="onChange('house.imgGas')"
                      @onRemove="onRemove('house.imgGas')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic="取暖费票照片"
                      v-model="form.house.imgHeating"
                      :required="false"
                      @onChange="onChange('house.imgHeating')"
                      @onRemove="onRemove('house.imgHeating')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item   prop="" label-width="0">
                  <UploadPic
                      @input="input"
                      textPic=" 补充材料"
                      v-model="form.house.imgOtherOne"
                      :required="false"
                      @onChange="onChange('house.imgOtherOne')"
                      @onRemove="onRemove('house.imgOtherOne')"
                  />
                </el-form-item>
              </el-col>
            </el-row></div>
        </div>
      </div>
      <div class="ad-form-header" v-if="form.huokouType == 0">
        <el-checkbox-group v-model="fangWuCheckList1" style="display: inline">
          <el-checkbox label="居住证信息" style="color: black;"></el-checkbox>
        </el-checkbox-group>
      </div>
      <div style="display: flex;justify-content: space-between" v-if="fangWuCheckList1[0]=='居住证信息'">
        <div>
          <h5 style="color: red">学生本人居住证</h5>
          <el-form-item label="居住证编号">
            <el-input v-model="form.juzhu[0].juzhuCode" placeholder="请输入居住证编号"></el-input>
          </el-form-item>
          <el-form-item label="居住人姓名">
            <el-input v-model="form.juzhu[0].juzhuName" placeholder="请输入居住人姓名"></el-input>
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="form.juzhu[0].idCard" placeholder="请输入身份证号"></el-input>
          </el-form-item>
          <el-form-item label="房屋详细地址">
            <el-select
                style="width: 210px;margin-right: 20px"
                v-model="form.juzhu[0].rangeName"
                placeholder="选择房屋地址-可模糊搜索"
                clearable
                filterable
            >
              <el-option
                  v-for="item in optionsList"
                  :key="item.rangeName"
                  :label="item.rangeName"
                  :value="item.rangeName"
              >
              </el-option>
            </el-select>
            <el-input  style="width: 210px" v-model="form.juzhu[0].address" placeholder="请输入房屋详细地址"></el-input>
          </el-form-item>
          <el-form-item label="居住人与该生关系">
            <el-select
                style="width: 210px"
                v-model="form.juzhu[0].relation"
                placeholder="请选择与该生关系"
                clearable
                filterable
            >
              <el-option
                  v-for="item in gaiShengGuanXiList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item >
            <UploadPic
                textPic="居住证照片"
                v-model="form.juzhu[0].imgJuzhu"
                :required="true"
                @onChange="onChange('juzhu[0].imgJuzhu')"
                @onRemove="onRemove('juzhu[0].imgJuzhu')"
            />
          </el-form-item>
        </div>
        <div>
          <h5 style="color: red">监护人1居住证</h5>
          <el-form-item label="居住证编号">
            <el-input v-model="form.juzhu[1].juzhuCode" placeholder="请输入居住证编号"></el-input>
          </el-form-item>
          <el-form-item label="居住人姓名">
            <el-input v-model="form.juzhu[1].juzhuName" placeholder="请输入居住人姓名"></el-input>
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="form.juzhu[1].idCard" placeholder="请输入身份证号"></el-input>
          </el-form-item>
          <el-form-item label="房屋详细地址">
            <el-select
                style="width: 210px;margin-right: 20px"
                v-model="form.juzhu[1].rangeName"
                placeholder="选择房屋地址-可模糊搜索"
                clearable
                filterable
            >
              <el-option
                  v-for="item in optionsList"
                  :key="item.rangeName"
                  :label="item.rangeName"
                  :value="item.rangeName"
              >
              </el-option>
            </el-select>
            <el-input  style="width: 210px" v-model="form.juzhu[1].address" placeholder="请输入房屋详细地址"></el-input>
          </el-form-item>
          <el-form-item label="居住人与该生关系">
            <el-select
                style="width: 210px"
                v-model="form.juzhu[1].relation"
                placeholder="请选择与该生关系"
                clearable
            >
              <el-option
                  v-for="item in gaiShengGuanXiList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item >
            <UploadPic
                textPic="居住证照片"
                v-model="form.juzhu[1].imgJuzhu"
                :required="true"
                @onChange="onChange('juzhu[1].imgJuzhu')"
                @onRemove="onRemove('juzhu[1].imgJuzhu')"
            />
          </el-form-item>
        </div>
        <div>
          <h5 style="color: red">监护人2居住证</h5>
          <el-form-item label="居住证编号">
            <el-input v-model="form.juzhu[2].juzhuCode" placeholder="请输入居住证编号"></el-input>
          </el-form-item>
          <el-form-item label="居住人姓名">
            <el-input v-model="form.juzhu[2].juzhuName" placeholder="请输入居住人姓名"></el-input>
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="form.juzhu[2].idCard" placeholder="请输入身份证号"></el-input>
          </el-form-item>
          <el-form-item label="房屋详细地址">
            <el-select
                style="width: 210px;margin-right: 20px"
                v-model="form.juzhu[2].rangeName"
                placeholder="选择房屋地址-可模糊搜索"
                clearable
                filterable
            >
              <el-option
                  v-for="item in optionsList"
                  :key="item.rangeName"
                  :label="item.rangeName"
                  :value="item.rangeName"
              >
              </el-option>
            </el-select>
            <el-input  style="width: 210px" v-model="form.juzhu[2].address" placeholder="请输入房屋详细地址"></el-input>
          </el-form-item>
          <el-form-item label="居住人与该生关系">
            <el-select
                style="width: 210px"
                v-model="form.juzhu[2].relation"
                placeholder="请选择与该生关系"
                clearable
            >
              <el-option
                  v-for="item in gaiShengGuanXiList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item >
            <UploadPic
                textPic="居住证照片"
                v-model="form.juzhu[2].imgJuzhu"
                :required="false"
                @onChange="onChange('juzhu[2].imgJuzhu')"
                @onRemove="onRemove('juzhu[2].imgJuzhu')"
            />
          </el-form-item>

        </div>

      </div>
      <div style="display: flex">
        <el-checkbox v-model="tongYiShow"></el-checkbox>
        <div style="color: red;font-weight: bolder;font-size: 22px">本人郑重承诺以上提交的材料均真实有效，如有不实，自愿承担相关责任和后果</div>
      </div>
<!--      <div style="margin-top: 20px;display: flex;justify-content: center">-->
<!--        <el-button type="primary" @click="save">提交报名</el-button>-->
<!--        <el-button type="primary" @click="fanHui">返回</el-button>-->
<!--      </div>-->

    </el-form>
  </div>
</template>

<script>
import UploadPic from "@/components/UploadPic";
import {getDepts} from "@/api/common";
import {idCardValidator} from "@/utils/validator";
import {addBaoMing, zhaoShengFanWei} from "@/api/shiWuQuBaoMingLieBiao";

export default {
  name: "addForm",
  data(){
    return{
      role: this.$store.getters.role,
      tongYiShow:false,
      fangWuCheckList:[],
      fangWuCheckList1:[],
      fangChanNameList:[
        {
          label:'房屋所有权姓名',
          value:'1'
        },
        {
          label:'使用《不动产权证书》信息报名',
          value:'2'
        },
        {
          label:'使用购房合同信息报名',
          value:'3'
        },
        {
          label:'小产权房信息报名',
          value:'4'
        },
        {
          label:'使用廉租房/公租房信息报名',
          value:'5'
        },
        {
          label:'自然村',
          value:'6'
        },
      ],
      current:1,
      prefixCode:this.$store.getters.prefixCode,
      prefixDeptCode: this.$store.getters.deptCode,
      duoBaoTaiList:[],
      activeNames: ['1'],
      form:{
        jianHuRen1:{
          guardianName:''
        },
        imageInfo:{},
        twins:[],
        otherImg: {},
        hukou:{},
        guardians:[
          {},
          {}
        ],
        house:{
          "studentId": 0,
          "houseType": 0,
          "documentNum": "",
          "ownerName": "",
          "idCard": "",
          "relation": "",
          "rangeName": "",
          "address": "",
          "documentTime": "",
          "imgHouse": "",
          "imgHouseDoor": "",
          "imgWater": "",
          "imgInvoice": "",
          "imgProperty": "",
          "imgGas": "",
          "imgHeating": "",
          "imgOtherOne": "",
          "imgOtherTwo": "",
          "imgBuyOne": "",
          "imgBuyTwo": ""
        },
        juzhu:[{},{},{}]
      },
      optionsList:[],
      huKouList:[
        {
          name: '主城区户籍 (丛台区、复兴区、邯山区、经开区、冀南新区)',
          value:1},
        {
          name: '非主城区户籍',
          value:0},
      ],
      schoolList:[],
      rules:{
        studentName: [{
          type: 'string',
          required: true,
          message: '请填写学生姓名',
          trigger: ['blur']
        },
          {
            required: true,
            message: "请输入10字以内汉字",
            trigger: "blur",
            max: 10,
          },
        ],
        idCard: [{
          required: true,
          trigger: "blur",
          validator: idCardValidator,
        },],
        gender: {
          type: 'string',
          required: true,
          message: '请选择男或女',
          trigger: ['blur', 'change']
        },
        birthday: {
          required: true,
          message: '请选择出生日期',
          trigger: ['blur', 'change']
        },
        nation: {
          type: 'string',
          required: true,
          message: '请输入民族',
          trigger: ['blur']
        },
        huokouType: {
          required: true,
          message: '请选择户口性质',
          trigger: ['blur', 'change']
        },
        careStatus: {
          required: true,
          message: '请选择是否优抚对象',
          trigger: ['blur', 'change']
        },
        'hukou.masterName':{
          required: true,
          message: '请输入户住姓名',
          trigger: ['blur', 'change']
        },
        'hukou.masterIdCard':{
          required: true,
          message: '请输入户主身份证号',
          trigger: ['blur', 'change']
        },
        'hukou.houseNum':{
          required: true,
          message: '请输入户号',
          trigger: ['blur', 'change']
        },
        'hukou.relation':{
          required: true,
          message: '请选择与户主关系',
          trigger: ['blur', 'change']
        },
        'hukou.address':{
          required: true,
          message: '请输入户本登记地址',
          trigger: ['blur', 'change']
        },
        'jianHuRen1.guardianName': {
          type: 'string',
          required: true,
          message: '请填写监护人姓名',
          trigger: ['blur']
        },
        'jianHuRen1.relation': {
          type: 'string',
          required: true,
          message: '请选择与学生关系',
          trigger: ['blur', 'change']
        },
        'jianHuRen1.phone': {
          type: 'string',
          required: true,
          message: '请填写监护人电话',
          trigger: ['blur']
        },
        'jianHuRen1.idCard': {
          type: 'string',
          required: true,
          message: '请填写监护人身份证',
          trigger: ['blur']
        },
        'jianHuRen1.address': {
          type: 'string',
          required: true,
          message: '请填写监护人地址',
          trigger: ['blur']
        },
        'jianHuRen1.birthday': {
          required: true,
          message: '请输入监护人出生日期',
          trigger: ['blur']
        },
        'jianHuRen1.gender': {
          type: 'string',
          required: true,
          message: '请输入监护人性别',
          trigger: ['blur', 'change']
        },
        'jianHuRen1.nation': {
          type: 'string',
          required: true,
          message: '请输入监护人民族',
          trigger: ['blur', 'change']
        },
        'house.rangeName':{
          type: 'string',
          required: true,
          message: '请选择房屋地址',
          trigger: ['blur', 'change']
        },
        'house.ownerName':{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        },
        'house.idCard':{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        },
        'house.relation':{
          type: 'string',
          required: true,
          message: '请输入必填信息',
          trigger: ['blur', 'change']
        }
      },
      youFuDuiXiangList:[
        {
          value: 1,
          label: '是'},
        {
          value:  0,
          label: '否'
        }
      ],
      youFuLeiXingList:[
        {
          value:'1',
          label:'普通学生'
        },
        {
          value:'2',
          label:'优抚对象'
        },
        {
          value:'3',
          label:'教师子女'
        },
        {
          value:'4',
          label:'人才引进'
        },
        {
          value:'5',
          label:'优秀企业家子女'
        },
        {
          value:'6',
          label:'其他'
        },
      ],
      xingBieList:[
        {
          value: '1',
          label: '男'
        },
        {
          value: '2',
          label: '女'
        }
      ],
      gaiShengGuanXiList:[
        {
          value: '1',
          label: '父子'
        },
        {
          value: '2',
          label: '父女'
        }
        ,
        {
          value: '3',
          label: '母子'
        },
        {
          value: '4',
          label: '母女'
        },
        {
          value: '5',
          label: '祖孙'
        },
        {
          value: '6',
          label: '外祖孙'
        },
        {
          value: '7',
          label: '本人'
        }
      ],
      activeName: '1',
      isUpadate:false,
    }
  },
  components:{UploadPic},
  created() {
    console.log(this.$store.getters.userInfo.deptId)
    if(this.$store.getters.userInfo.roleCode=='SCHOOL'){
      this.form.schoolId=this.$store.getters.userInfo.deptId
      this.getList()
    }else {
      this.huoQuXueXiaoList()
    }

  },
  methods:{
    fanChanChange(value){
      this.activeName=value
    },
    getGradeList(val){
      zhaoShengFanWei({
        schoolId:val,
        code:this.prefixDeptCode,
        period:2,
        nature:2,
        type:2
      }).then(res=>{
        this.optionsList=res
      })
    },
    getList(){
      zhaoShengFanWei({
        schoolId:this.form.schoolId||'',
        type:2

      }).then(res=>{
        this.optionsList=res
      })
    },
    huoQuXueXiaoList(){
      getDepts({deptCode:this.prefixDeptCode,level:3}).then(res=>{
        this.schoolList = res
      })
    },
    change(e){
      this.$forceUpdate()
    },
    //识别出身份证信息
    sfzxx(){
      this.$forceUpdate()
    },
// 选择文件
    handleChange1(file, fileLists) {
      console.log(file);
      console.log(fileLists);
      // 本地服务器路径
      console.log(URL.createObjectURL(file.raw));
      // 虚拟盘符
      // JavaScript无法获取本地文件路径
      // JavaScript不支持对本地资源的操作
      console.log(document.getElementsByClassName("el-upload__input")[0].value);
    },
    handleClick(tab, event) {
      this.form.house={}
      console.log(tab._props.name, this.activeName,"ddd");
    },
    fanHui(){
      this.$emit()
      // this.$router.go(-1)
    },
    addDuoBaoTai(){
      if(this.form.twins.length<3){
        this.form.twins.push({


        })
      }else{
        this.$message.error('最多添加三胞胎')
      }
    },
    delDuoBaoTai(){
      if(this.form.twins.length>0){
        this.form.twins.pop()
      }else{
        this.$message.error('已无多胞胎信息')
      }
    },

    handleChange(val) {
      console.log(val);
    },
    // 头像上传后回调
    onChange(valid) {
      console.log(this.form);
      this.$refs["form"].clearValidate(valid);
    },
    // 头像删除后回调
    onRemove(valid) {
      this.$refs["form"].validateField(valid);
    },
    save(){
      var show=true
      this.form.type=1
      this.form.jianHuRen1.tyle=1
      this.form.guardians[0]=this.form.jianHuRen1
      this.form.guardians[0].birthday=this.$dayJs(this.form.jianHuRen1.birthday).format('YYYY-MM-DD')
      var model={
        studentName:this.form.studentName,
        idCard:this.form.idCard,
        gender:this.form.gender,
        birthday:this.$dayJs(this.form.birthday).format('YYYY-MM-DD'),
        schoolId:this.form.schoolId,
        huokouType:this.form.huokouType,
        careStatus:this.form.careStatus,
        nation: this.form.nation,
        type:1}
      if(JSON.stringify(this.form.house)!='{}'&&this.fangWuCheckList[0]=="房屋所有权信息"){
        this.form.house.houseType=this.form.fangChanId
        model.house=this.form.house
        model.house.documentTime=this.$dayJs(this.form.house.documentTime).format('YYYY-MM-DD')
      }
      if(JSON.stringify(this.form.guardians[0])!='{}'){
        model.guardians=[]
        this.form.guardians[0].type=1
        model.guardians[0]=this.form.guardians[0]
      }
      if(JSON.stringify(this.form.guardians[1])!='{}'){
        this.form.guardians[1].type=2
        model.guardians[1]=this.form.guardians[1]
      }
      if(JSON.stringify(this.form.hukou)!='{}'){
        model.hukou=this.form.hukou
      }
      if(this.form.twins.length>0){
        model.twins=this.form.twins
      }
      if(JSON.stringify(this.form.otherImg)!='{}'){
        model.otherImg=this.form.otherImg
      }
      if(this.form.juzhu.length>0){
        if(JSON.stringify(this.form.juzhu[0])!='{}'||JSON.stringify(this.form.juzhu[1])!='{}'||JSON.stringify(this.form.juzhu[2])!='{}')  {
          model.juzhu=[]
        }

        if(JSON.stringify(this.form.juzhu[0])!='{}'&&this.fangWuCheckList1[0]=="居住证信息"){
          if(this.form.juzhu[0].juzhuCode&&this.form.juzhu[0].juzhuName&&this.form.juzhu[0].idCard&&this.form.juzhu[0].rangeName
              &&this.form.juzhu[0].address&&this.form.juzhu[0].relation&&this.form.juzhu[0].imgJuzhu){
            this.form.juzhu[0].type=1
            model.juzhu[0]=this.form.juzhu[0]
            show=true
          }else {
            this.$message.error('学生本人居住证不完整')
            show=false
          }

        }
        if(JSON.stringify(this.form.juzhu[1])!='{}'){
          if(this.form.juzhu[1].juzhuCode&&this.form.juzhu[1].juzhuName&&this.form.juzhu[1].idCard&&this.form.juzhu[1].rangeName
              &&this.form.juzhu[1].address&&this.form.juzhu[1].relation&&this.form.juzhu[1].imgJuzhu){
            this.form.juzhu[1].type=2
            model.juzhu[1]=this.form.juzhu[1]
            show=true
          }else {
            this.$message.error('监护人1居住证不完整')
            show=false
          }

        }
        if(JSON.stringify(this.form.juzhu[2])!='{}'){
          if(this.form.juzhu[2].juzhuCode&&this.form.juzhu[2].juzhuName&&this.form.juzhu[2].idCard&&this.form.juzhu[2].rangeName
              &&this.form.juzhu[2].address&&this.form.juzhu[2].relation&&this.form.juzhu[2].imgJuzhu){
            this.form.juzhu[2].type=3
            model.juzhu[2]=this.form.juzhu[2]
            show=true
          }else {
            this.$message.error('监护人2居住证不完整')
            show=false
          }

        }

        if(JSON.stringify(this.form.guardians[1])!='{}'){
          if(
              this.form.guardians[1].guardianName&&
              this.form.guardians[1].idCard&&
              this.form.guardians[1].gender&&
              this.form.guardians[1].nation&&
              this.form.guardians[1].birthday&&
              this.form.guardians[1].relation&&
              this.form.guardians[1].address&&
              this.form.guardians[1].phone&&
              this.form.guardians[1].imgIdCard
          ){
            this.form.guardians[1].type=2
            model.guardians[1]=this.form.guardians[1]
            model.guardians[1].birthday=this.$dayJs(this.form.guardians[1].birthday).format('YYYY-MM-DD')
            show=true
          }else {
            this.$message.error('监护人2信息不完整')
            show=false
          }

        }

      }
      if(show) {
        this.$refs['form'].validate(async valid => {
          if (valid) {
            if(this.tongYiShow){
              addBaoMing(model, this.prefixDeptCode).then(res => {
                if (res) {
                  this.$message.success('保存成功')
                  this.$emit('close', false)
                }
              })
            }else {
              this.$message.error('请勾选承诺条款')
            }

          }
        })
      }
    },

  }
}
</script>

<style scoped lang="scss">
.county-primary {
  .ad-form-header {
    background-color: #EBEBEB;
    line-height: 30px;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 4px;
  }
  .ad-form-wrap {
    flex-wrap: wrap;
    .el-form-item {
      flex: 0 0 50%;
    }
    .long-item {
      flex: 0 0 66.6666%;
    }
  }

}
</style>
