<template>
  <div>
    <div class="sd-option-container">
      <!--class="sd-search"-->
      <div class="sd-search">

        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.signId"
                placeholder="报名ID"
                class="sd-w-200"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.keywords"
                placeholder="姓名或身份证号"
                class="sd-w-200"
                clearable
            ></el-input>
          </el-form-item>
<!--          <el-form-item>-->
<!--            <el-select size="small" v-model="search.schoolId" placeholder="报名学校">-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item  v-if="
                role == 'COUNTY_ADMIN' || role == 'AUDITOR'
              ">
            <el-select filterable clearable size="small" v-model="search.schoolId" placeholder="分配学校">
              <el-option
                  v-for="item in schoolList"
                  :key="item.id"
                  :label="item.deptName"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item  v-if="
                role == 'COUNTY_ADMIN' || role == 'AUDITOR'
              ">
            <el-select filterable clearable size="small" v-model="search.enterSchoolId" placeholder="录取学校" >
              <el-option
                  v-for="item in schoolList"
                  :key="item.id"
                  :label="item.deptName
"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.twinsStatus"
                placeholder="是否多胞胎"
                class="sd-w-150"
                clearable
            >
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item  >
            <el-select filterable clearable size="small" v-model="search.hukouAddress" placeholder="选择户籍所在小区/村庄">
              <el-option
                  v-for="item in huJiList"
                  :key="item.id"
                  :label="item.rangeName"
                  :value="item.rangeName"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item >
            <el-select filterable clearable size="small" v-model="search.houseAddress" placeholder="选择房产所在小区/村庄">
              <el-option
                  v-for="item in huJiList"
                  :key="item.id"
                  :label="item.rangeName"
                  :value="item.rangeName"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item  >
            <el-select filterable clearable size="small" v-model="search.juzhuAddress" placeholder="选择居住证所在小区/村庄">
              <el-option
                  v-for="item in huJiList"
                  :key="item.id"
                  :label="item.rangeName"
                  :value="item.rangeName"
              >
              </el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.signType"-->
<!--                placeholder="类别"-->
<!--                class="sd-w-150"-->
<!--            >-->
<!--              <el-option label="房户一致" :value="1"></el-option>-->
<!--              <el-option label="户口" :value="2"></el-option>-->
<!--              <el-option label="房产" :value="3"></el-option>-->
<!--              <el-option label="外来经商务工" :value="4"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-select
                size="small"
                v-model="search.schoolStatus"
                placeholder="学校审核状态"
                class="sd-w-150"
                clearable
            >
              <el-option label="审核中" :value="0"></el-option>
              <el-option label="已通过" :value="1"></el-option>
              <el-option label="已驳回-补充信息" :value="2"></el-option>
              <el-option label="退回教育局" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.eduStatus"
                placeholder="教育局审核状态"
                class="sd-w-150"
                clearable
            >
              <el-option label="审核中" :value="0"></el-option>
              <el-option label="已通过" :value="1"></el-option>
              <el-option label="已驳回-补充信息" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.houseStatus"
                placeholder="房管局审核状态"
                class="sd-w-130"
                clearable
            >
              <el-option label="待审核" :value="0"></el-option>
              <el-option label="通过" :value="1"></el-option>
              <el-option label="不通过" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.policeStatus"
                placeholder="公安-户口审核"
                class="sd-w-130"
                clearable
            >
              <el-option label="待审核" :value="0"></el-option>
              <el-option label="通过" :value="1"></el-option>
              <el-option label="不通过" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.juzhuStatus"
                placeholder="公安-居住证审核"
                class="sd-w-130"
                clearable
            >
              <el-option label="待审核" :value="0"></el-option>
              <el-option label="通过" :value="1"></el-option>
              <el-option label="不通过" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.signStatus"
                placeholder="是否报到"
                class="sd-w-130"
                clearable
            >
              <el-option label="未报到" :value="2"></el-option>
              <el-option label="已报到" :value="1"></el-option>
              <el-option label="待报到" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.publicStatus"
                placeholder="是否公示"
                class="sd-w-130"
                clearable
            >
              <el-option label="未公示" :value="0"></el-option>
              <el-option label="公示" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData1"
            ></el-button>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="warning"
                icon="el-icon-download"
                @click="exportData"
                v-if="
                role == 'COUNTY_ADMIN'  || role == 'SCHOOL'|| role == 'AUDITOR'
              "

            >导出报名信息</el-button
            ><!--                || role == 'AUDITOR'-->
            <!--            <el-button-->
            <!--                size="small"-->
            <!--                type="warning"-->
            <!--                icon="el-icon-download"-->
            <!--                v-if="-->
            <!--                role == 'COUNTY_ADMIN' || role == 'AUDITOR' || role == 'SCHOOL'-->
            <!--              "-->
            <!--            >下载入户调查单</el-button-->
            <!--            >-->
            <el-button
                size="small"
                type="success"
                icon="el-icon-check"
                v-if="role == 'COUNTY_ADMIN'"
                @click="piLiangShenHeTongGuo"
            >批量审核通过</el-button
            >
             <el-button
                size="small"
                type="primary"
                icon="el-icon-s-promotion"
                v-if="role == 'COUNTY_ADMIN'"
                @click="gongShiLuQuJieGuo"
            >公示录取结果</el-button
            >
            <el-button
                size="small"
                type="primary"

                v-if="role == 'COUNTY_ADMIN'"
                @click="fenPeiSchool1"
            >分配学校</el-button
            >
            <el-button
                size="small"
                type="primary"
                icon="el-icon-s-promotion"
                v-if="role == 'SCHOOL'"
                @click="faSongLuQu"
            >发送录取通知书</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe @selection-change="handleSelectionChange">
<!--      <el-table-column-->
<!--          type="selection"-->
<!--          width="55">-->
<!--      </el-table-column>-->
      <el-table-column
          align="center"
          label="报名ID"
          prop="signId"
          width="90"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="idCard"
          width="150"
      ></el-table-column>
      <el-table-column align="center" prop="hukouAddress" label="户籍所在小区/村庄"></el-table-column>
      <el-table-column align="center" prop="houseAddress" label="房产所在小区/村庄"></el-table-column>
      <el-table-column align="center" prop="juzhuAddress" label="居住证所在小区/村庄"></el-table-column>
<!--      <el-table-column align="center" label="报名类别" prop="signType">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.signType == 1 ? "房户一致" :scope.row.signType == 2?"户口":scope.row.signType == 3?'房产':scope.row.signType == 4?'':'外来务工经商' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <!--      <el-table-column-->
      <!--          align="center"-->
      <!--          label="购房时间"-->
      <!--          prop="aId"-->
      <!--          width="150"-->
      <!--      ></el-table-column>-->
      <el-table-column
          align="center"
          label="分配学校"
          prop="schoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="录取学校"
          prop="enterSchoolName"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="多胞胎个数"
          prop="twinsNum"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="对口类型"
          prop="signTypeStr"
      >
      </el-table-column>
      <el-table-column align="center" label="是否优抚对象" prop="careStatus">
        <template slot-scope="scope">
          <span>{{ scope.row.careStatus == 1 ? "是" :scope.row.careStatus == 0?"否":'' }}</span>
        </template>
      </el-table-column>

<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="调剂学校"-->
<!--          prop="adjustSchoolName"-->
<!--      ></el-table-column>-->
      <el-table-column
          align="center"
          label="报名时间"
          prop="createTime"
          width="150"
      ></el-table-column>
      <el-table-column
          align="center"
          label="房产审核"
          prop="houseStatus"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.houseStatus == 1 ? "通过" :scope.row.houseStatus == 0?"待审核":scope.row.houseStatus == 2?"未通过":'' }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="公安-户口审核"
          prop="policeStatus"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.policeStatus == 1 ? "通过" :scope.row.policeStatus == 0?"待审核":scope.row.policeStatus == 2?"未通过":'--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="公安-居住证审核"
          prop="policeStatus"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.juzhuStatus == 1 ? "通过" :scope.row.juzhuStatus == 0?"待审核":scope.row.juzhuStatus == 2?"未通过":'--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="学校审核状态"
          prop="schoolStatus"
          width="130"
      >
        <template slot-scope="scope">
          <span>{{scope.row.schoolStatus == 1 ? "通过" :scope.row.schoolStatus == 0?"待审核":scope.row.schoolStatus == 2?"驳回补充信息":scope.row.schoolStatus == 3?'驳回不允许报名':scope.row.schoolStatus == 4?'退回教育局':''}}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="教育局审核状态"
          prop="eduStatus"
          width="130"
      >
        <template slot-scope="scope">
          <span>{{scope.row.eduStatus == 1 ? "通过" :scope.row.eduStatus == 0?"待审核":scope.row.eduStatus == 2?"驳回补充信息":scope.row.eduStatus == 3?'驳回不允许报名':''}}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="是否公示"
          prop="publicStatus"
      >
        <template slot-scope="scope">
          <template>{{scope.row.publicStatus==0?'未公示':scope.row.publicStatus==1?'公示':''}}</template>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="是否报到"
          prop="signStatus"
      >
        <template slot-scope="scope">
          <template>{{scope.row.signStatus==0?'未报到':scope.row.signStatus==1?'报到':scope.row.signStatus==2?'未报到':''}}</template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="mark"></el-table-column>
      <el-table-column align="center" label="优抚类别" prop="careType"></el-table-column>
      <el-table-column align="center" label="操作" width="300" fixed="right">
        <template slot-scope="{ row }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail(row)"
          >详情</el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.publicSecurityReviewStatus == '通过'"
              @click="pass(row)"
          >通过</el-link
          >
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              @click="fail(row)"
              v-if="role == 'COUNTY_ADMIN' || role == 'AUDITOR'"
          >不通过</el-link
          >
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              @click="fail(row)"
              v-if="role == 'SCHOOL'"
          >驳回</el-link
          >
          <el-link
              icon="el-icon-s-tools"
              type="warning"
              :underline="false"
              @click="adjust(row)"
              v-if="role == 'COUNTY_ADMIN' "
          >分配学校</el-link
          >
          <el-link
              icon="el-icon-s-tools"
              type="warning"
              :underline="false"
              @click="chongXinadjust(row)"
              v-if="role == 'COUNTY_ADMIN' "
          >重新分配学校</el-link
          >
          <el-link
              icon="el-icon-s-tools"
              type="warning"
              :underline="false"
              @click="tiaoJiJiLu(row)"
              v-if="role == 'COUNTY_ADMIN' "
          >分配记录</el-link
          >
<!--          || role == 'AUDITOR'-->
          <el-link
              icon="el-icon-s-tools"
              type="warning"
              :underline="false"
              @click="report(row)"
              v-if="role == 'SCHOOL'"
          >报到</el-link
          >
          <el-link
              icon="el-icon-s-tools"
              type="warning"
              :underline="false"
              @click="youfu(row)"
              v-if="row.careStatus==1&&role == 'COUNTY_ADMIN'"
          >优抚类别</el-link
          >
          <el-link
              icon="el-icon-s-tools"
              type="warning"
              :underline="false"
              @click="duiKouBiaoJi(row)"
              v-if="role == 'SCHOOL'||role == 'COUNTY_ADMIN'"
          >对口标记</el-link
          >
          <el-link
              icon="el-icon-delete-solid"
              type="danger"
              :underline="false"
              @click="cheXiaoGongShi(row)"
              v-if="role == 'COUNTY_ADMIN' "
          >撤销公示</el-link
          >
          <el-link
              icon="el-icon-delete-solid"
              type="danger"
              :underline="false"
              @click="deleteStudent(row)"
              v-if="role == 'COUNTY_ADMIN'&&delStudentQuanXian "
          >删除报名信息</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="$pageSizes"
      >
        <span style="color: #606266;font-size: 14px">共(含多胞胎){{signNum }}人</span>
        <!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
    <el-dialog
        title="学生报名信息详情"
        width="800px"
        append-to-body
        :show-close="false"
        :visible.sync="modal.detail"
        :close-on-click-modal="false"
        center
    >
      <div>
        <component
            v-if="modal.detail"
            :ref="'detail'"
            :is="'detail'"
            :row="row"
            :data=null
            :mode=null
            :data-row="selectRow?selectRow:{}"
            @searchs="searchs"

        />
        <!--        @save-complete="dialogFormComponent.saveComplete"-->
      </div>
      <div class="flex-center" style="margin-top:20px ">
        <el-button
            type="success"
            @click="tongGuo"
            size="small"
        >通过</el-button
        >
        <el-button
            type="danger"
            @click="buTongGuo"
            size="small"
        v-if="role=='SCHOOL'"
        >驳回</el-button
        >
        <el-button
            type="danger"
            @click="buTongGuo"
            size="small"
            v-if="role == 'COUNTY_ADMIN' || role == 'AUDITOR'"
        >不通过</el-button
        >
        <el-button
            type="default"
            @click="fanHui"
            size="small"
        >返回</el-button
        >
        <el-button type="primary" @click="shangYiTiao" :loading="saveLoading" size="small">上一条</el-button>
        <el-button
            type="primary"
            @click="xiaYiTiao"
            :loading="saveLoading"
            size="small"
        >下一条</el-button
        >
<!--        <el-button-->
<!--            type="primary"-->
<!--            @click="tiaoJi"-->
<!--            :loading="saveLoading"-->
<!--            size="small"-->
<!--        >调剂</el-button-->
<!--        >-->
      </div>
    </el-dialog>
    <el-dialog
        title="驳回原因"
        :visible.sync="dialogVisible"
        align="center"
        width="500px"
    >
      <div>
        <el-radio-group v-model="status">
          <el-radio :label="2">驳回-修改信息</el-radio>
          <el-radio :label="4"  v-if="role == 'SCHOOL'">退回教育局</el-radio>
        </el-radio-group>
<!--        <el-button type="primary" @click="xiuGaiXinXi">驳回-修改信息</el-button>-->
<!--        <el-button type="info" @click="buKeZaiBao" v-if="role == 'SCHOOL'">退回教育局</el-button>-->
        <el-input type="textarea" placeholder="请输入驳回原因" v-model="reason"  style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="shenHeBuTongGuo"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="备注"
        :visible.sync="beiZhuShow"
        align="center"
        width="500px"
    >
      <div>
        <el-input type="textarea" placeholder="请输备注" v-model="mark" style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="beiZhuAdd"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="分配"
        :visible.sync="tiaoJiShow"
        width="500px"
    >
      <el-form
          style="margin-top: 20px"
          :model="tiaoJiXinXi"
          ref="tiaoJiXinXi"
          :rules="tiaoJiXinXiRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item prop="schoolId" label="分配学校：">
          <el-select
              v-model="tiaoJiXinXi.schoolId"
              placeholder="分配学校"
              clearable
              filterable
          >
            <el-option
                v-for="item in schoolList"
                :key="item.id"
                :label="item.deptName"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="content" label="分配原因：">
          <el-input @input="change" type="textarea" style="width:250px" placeholder="请输入重新分配原因" v-model="tiaoJiXinXi.content"></el-input>
        </el-form-item>
      </el-form>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="tiaoJi1"
      >确 定</el-button>
    </span>
    </el-dialog>
    <el-dialog
        title="重新分配"
        :visible.sync="chongXinShow"
        width="500px"
    >
      <el-form
          style="margin-top: 20px"
          :model="chongXin"
          ref="chongXin"
          :rules="chongXinRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item prop="schoolId" label="重新分配学校：">
          <el-select
              v-model="chongXin.schoolId"
              placeholder="重新分配学校"
              clearable
              filterable
          >
            <el-option
                v-for="item in schoolList"
                :key="item.id"
                :label="item.deptName"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="content" label="重新分配原因：">
          <el-input @input="change" type="textarea" style="width:250px" placeholder="请输入重新分配原因" v-model="chongXin.content"></el-input>
        </el-form-item>
      </el-form>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="chongXin1"
      >确 定</el-button>
    </span>
    </el-dialog>
    <el-dialog
        title="优抚对象"
        :visible.sync="youFuShow"
        width="500px"
    >
      <p>优抚类型</p>
      <el-select
          v-model="youFu.content"
          placeholder="请选择优抚类型"
          filterable
          clearable
      >
        <el-option
            v-for="item in youFuLeiXingList"
            :key="item.message"
            :label="item.message"
            :value="item.message"
        >
        </el-option>
      </el-select>
      <el-input v-if="youFu.content=='其他'" v-model="youFu.content" placeholder="其他类型请输入" ></el-input>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="youFu1"
      >确 定</el-button>
    </span>
    </el-dialog>
    <el-dialog
        title="分配学校"
        :visible.sync="fenPeiSchoolShow"
        width="500px"
    >
      <div>已选择学生数量:{{this.renShu}}</div>
      <el-form
          style="margin-top: 20px"
          :model="fenPeiSchool"
          ref="fenPeiSchool"
          :rules="fenPeiSchoolRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item prop="schoolId" label="分配学校：">
          <el-select
              v-model="fenPeiSchool.schoolId"
              placeholder="分配学校"
              clearable
              filterable
          >
            <el-option
                v-for="item in schoolList"
                :key="item.id"
                :label="item.deptName"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="content" label="分配原因：">
          <el-input @input="change" type="textarea" style="width:250px" placeholder="请输入重新分配原因" v-model="fenPeiSchool.content"></el-input>
        </el-form-item>
      </el-form>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="fenPei"
      >确 定</el-button>
    </span>
    </el-dialog>
    <el-dialog
        title="发送录取通知书"
        :visible.sync="faSongLuQuShow"
        width="500px"
    >
      <div>已选择学生数量:{{luQuTongZhi.renShu}}</div>
      <el-input type="textarea" placeholder="请输入录取内容" v-model="luQuTongZhi.content" @input="change"></el-input>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="faSongLuQuJieGuo"
      >确 定</el-button>
    </span>
    </el-dialog>
    <el-dialog
        title="报到"
        :visible.sync="baoDaoShow"
        align="center"
        width="500px"
    >
      <div>
        <el-radio-group v-model="baodao.status">
          <el-radio :label="2">未报到</el-radio>
          <el-radio :label="1">报到</el-radio>
        </el-radio-group>
        <!--         <el-button type="primary" @click="xiuGaiXinXi">驳回-修改信息</el-button>-->
        <!--         <el-button type="info" @click="buKeZaiBao">驳回-驳回-不可再报</el-button>-->
        <el-input v-if="baodao.status==2" type="textarea" placeholder="请输入未报到原因" v-model="baodao.content"  style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="baoDao"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="对口标记"
        :visible.sync="duiKouShow"
        align="center"
        width="500px"
    >
      <div style="text-align: left;">
        <el-radio-group v-model="duiKouStatus">
          <el-radio :label="1">房户一致两对口</el-radio>
          <el-radio :label="3">房产一对口</el-radio>
          <el-radio :label="4">随迁子女</el-radio>
          <el-radio :label="2">户口一对口</el-radio>
        </el-radio-group>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="duiKouBiaoJi1"
        >确 定</el-button>
      </div>
    </el-dialog>
    <!--调剂记录-->
    <el-dialog
        title="调剂记录"
        :visible.sync="tiaoJiJiLuShow"
        width="60%"
    >
      <el-table
          :data="jiLuData"
          style="width: 100%"
      >
        <el-table-column
            align="center"
            label="学生姓名"
            prop="studentName"
            width="120"
        >
        </el-table-column>
        <el-table-column
            align="center"
            label="身份证号"
            prop="idCard"
            width="170"
        ></el-table-column>
        <el-table-column
            align="center"
            label="操作账号"
            prop="username"
            width="180"
        ></el-table-column>
        <el-table-column
            align="center"
            label="现分配学校"
            prop="fromSchoolName"
            width="180"
        >
        </el-table-column>
        <el-table-column
            align="center"
            label="分入学校"
            prop="toSchoolName"
            width="180"
        ></el-table-column>
        <el-table-column
            align="center"
            label="分配时间"
            prop="createTime"
            width="180"
        ></el-table-column>
        <el-table-column
            align="center"
            label="重新分配原因"
            prop="content"
            width="180"
        ></el-table-column>
      </el-table>
      <span
          slot="footer"
          class="dialog-footer"
      >
            <el-button @click="tiaoJiJiLuShow = false">取 消</el-button>
          </span>
    </el-dialog>
    <!-- 撤销公示-->
    <el-dialog
        title="撤销公示原因"
        :visible.sync="cheXiaoShow"
        width="500px"
    >
      <el-form
          style="margin-top: 20px"
          :model="cheXiaoForm"
          ref="cheXiaoForm"
          :rules="cheXiaoRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item prop="content" label="撤销公示原因：">
          <el-input   @input="change" type="textarea" style="width:250px" placeholder="请输入撤销公示原因" v-model="cheXiaoForm.content"></el-input>
        </el-form-item>
      </el-form>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="cheXiao"
      >确 定</el-button>
    </span>
    </el-dialog>
    <!-- 删除学生报名信息-->
    <el-dialog
        title="删除学生报名信息"
        :visible.sync="deleteShow"
        width="500px"
    >
      <el-form
          style="margin-top: 20px"
          :model="deleteForm"
          ref="deleteForm"
          :rules="deleteRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item prop="content" label="删除学生原因：">
          <el-input   @input="change" type="textarea" style="width:250px" placeholder="请输入删除学生原因" v-model="deleteForm.content"></el-input>
        </el-form-item>
      </el-form>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="del"
      >确 定</el-button>
    </span>
    </el-dialog>
    <!--    &lt;!&ndash; 详情 &ndash;&gt;-->
    <!--    <el-dialog :visible.sync="modal.detail"> </el-dialog>-->
    <!--    &lt;!&ndash; 详情 &ndash;&gt;-->
    <!--    <el-dialog :visible.sync="modal.add"> </el-dialog>-->
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import detail from "@/views/shiWuQu/shiWuQuYiZhengJianBaoMing/detail";
import {deleteEnrollInfo} from "@/api/sysConfig";
import {getPermissionStatus} from "@/api/funcSwitch";
import { getList } from "@/api/enrollment";
import {
  getshiWuQuList,
  baoMingPiliangShenHeTongGuo,
  baoMingShenHeBuTongGuo,
  baoMingShenHeTongGuo,
  gongShiLuQuJieGuo,
  youFuLeiXingList,
  addYouFuLeiXing,
  fenPeiXueXiao,
  getFaSongLuQuTongZhiRenShu,
  faSongLuQuTongZhi,
  xuShengBaoDao,
  zhaoShengFanWei,
  fenPeiRenShu,
  setDuiKouLeiBie,
  tiaoJiJiLu,
  chongXinFenPei,
  cheXiaoGongShiFenPei
} from "@/api/shiWuQuBaoMingLieBiao";
import {pref} from "@/utils/common";
import {getDepts} from "@/api/common";
export default {
  mixins: [ ModalMixin],
  data() {
    return {
      delStudentQuanXian:true,
      gongShiQuanXian:true,
      faBuLuQuQuanXian:true,
      cheXiaoForm:{},
      cheXiaoShow:false,
      deleteShow:false,
      deleteForm:{

      },
      cheXiaoRules: {
        content: [{
          required: true,
          message: "请输入撤销原因",
          trigger: "blur",
        },
          {required: true, min: 4, max: 200, message: '请输入4-200个文字', trigger: 'blur'}
        ]
      },
      deleteRules: {
        content: [{
          required: true,
          message: "请输入删除原因",
          trigger: "blur",
        },
          {required: true, min: 4, max: 200, message: '请输入4-200个文字', trigger: 'blur'}
        ]
      },
      huJiList:[],
      huJiList1:[],
      huJiList2:[],
      baoDaoStatus:'',
      baoDaoShow:false,
      faSongLuQuShow:false,
      luQuTongZhi:{
        content:'',
        renShu:"",
        studentIds:[]
      },
      fenPeiSchool:{
        schoolId:'',
        studentIds:[]
      },
      schoolList:[],
      youFu:{
        content:'',
        id:''
      },
      id:'',//学生ID
      mark:'',//学生审核通过备注
      status:'',//学生驳回类型
      reason:'',
      selectRow:{},
      youFuShow:false,
      fenPeiSchoolShow:false,
      tiaoJiJiLuShow:false,
      jiLuData:[],
      tiaoJiSchoolList:[],
      tiaoJiXinXi:{},
      beiZhuShow:false,
      studentIds:[],
      idList:[],
      chongXin:{},
      fenPeiSchoolRules:{
        schoolId: [
          {
            required: true,
            message: "请选择分配学校",
            trigger: "change",
          },
        ],
        content: [{
          required: true,
          message: "请输入分配原因",
          trigger: "blur",
        },
          {required: true, min: 2, max: 500, message: '请输入2-500个文字', trigger: 'blur'}
        ]
      },
      tiaoJiXinXiRules:{
        schoolId: [
          {
            required: true,
            message: "请选择分配学校",
            trigger: "change",
          },
        ],
        content: [{
          required: true,
          message: "请输入分配原因",
          trigger: "blur",
        },
          {required: true, min: 2, max: 500, message: '请输入2-500个文字', trigger: 'blur'}
        ]
      },
      chongXinRules:{
        schoolId: [
          {
            required: true,
            message: "请选择重新分配学校",
            trigger: "change",
          },
        ],
        content: [{
          required: true,
          message: "请输入重新分配原因",
          trigger: "blur",
        },
          {required: true, min: 2, max: 500, message: '请输入2-500个文字', trigger: 'blur'}
        ]
      },
      prefixDeptCode: this.$store.getters.deptCode,
      radio:1,
      tiaoJiShow:false,
      chongXinShow:false,
      boHuiYuanYin:1,
      dialogVisible:false,
      row:'',
      tableData:{},
      saveLoading: false,
      role: this.$store.getters.role,
      total:'',
      signNum:'',
      search: {
        pageNumber: 1,
        pageSize: 10,
        type:2,
        // schoolType: 1,
        // nature:2,
        // period:2
      },
      youFuLeiXingList:[

      ],
      modal: {
        add: false,
        detail: false,
      },
      renShu:'',//分配人数
      baodao:{

      },
      duiKouShow:false,
      duiKouStatus:''
    };
  },
  components:{detail},
  created() {
    this.quanXian('DELETE_STUDENT',1)
    this.quanXian('PUBLIC_STUDENT', 2)
    this.quanXian('NOTICE_STUDENT', 3)
    this.getTableData()
    if(this.role=="COUNTY_ADMIN"){
    youFuLeiXingList({},this.prefixDeptCode).then(res=>{
      this.youFuLeiXingList=res
      console.log(res,"res")
    })
    }
    if(this.role!="SCHOOL"){
      this.getSchoolList()
    }
    this.getZhaoShengfanWei()
  },
  methods: {
    quanXian(row,el){
      getPermissionStatus({key:row},this.prefixDeptCode).then(res=>{
        if(el==1){
          this.delStudentQuanXian=res
        }
        if(el==2){
          this.gongShiQuanXian=res
        }
        if(el==3){
          this.faBuLuQuQuanXian=res
        }
        console.log(res,'res')
      })
    },
    del(){
      this.$refs.deleteForm.validate((valid) => {
        if (valid) {
          this.$confirm('确认要删除该学生', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            deleteEnrollInfo({key:this.deleteForm.id,content:this.deleteForm.content}, this.prefixDeptCode).then(res => {
              this.cheXiaoForm.content = ''
              this.cheXiaoShow = false
              this.getTableData()
              this.$message
                  .success('删除成功')
            })
          })
        }
      })
    },
    deleteStudent(row){
      this.deleteForm=row
      this.deleteShow=true
    },
    cheXiaoGongShi(row){
      this.cheXiaoShow=true
      this.cheXiaoForm.id=row.id
    },
    cheXiao(){
      this.$refs.cheXiaoForm.validate((valid) => {
        if (valid) {
          this.$confirm('确认要撤销该学生的公示录取结果', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            cheXiaoGongShiFenPei(this.cheXiaoForm,this.prefixDeptCode).then(res=>{
              this.cheXiaoForm.content=''
              this.cheXiaoShow=false
              this.getTableData()
              this.$message
                  .success('撤销成功')
            })

          })}
      })
    },
    change(e){
      this.$forceUpdate()
    },
    duiKouBiaoJi(row){
      this.id=row.id
      this.duiKouStatus=row.signType
      this.duiKouShow=true
    },
    duiKouBiaoJi1(){
      setDuiKouLeiBie({id:this.id,type:this.duiKouStatus},this.prefixDeptCode).then(res=>{
        if(res){
          this.$message.success("操作成功")
          this.duiKouShow=false
          this.getTableData()
        }
      })
    },
    getZhaoShengfanWei(){
      zhaoShengFanWei({code:this.prefixDeptCode,type:2}).then(res=>{
        this.huJiList=res
         console.log(res,"res")
      })
    },
    getSchoolList(){
      console.log(this.$store.getters,"getters")
      getDepts({type:1,period:2,deptCode:this.prefixDeptCode},this.prefixDeptCode).then(res=>{
        this.schoolList=res
        console.log(res,"res")
      })
    },
    fenPeiSchool1(){
      this.fenPeiSchoolShow=true
      this.fenPeiSchool.schoolId=""
      this.fenPeiSchool.content=""
      this.fenPeiSchool.studentIds=[]
      let studentIds=[]
      let fenPei=this.search
      this.idList.forEach((item) =>{
        studentIds.push(item.id)
      })
      fenPei.studentIds=studentIds
      fenPeiRenShu(fenPei,this.prefixDeptCode).then(res=>{
        this.renShu=res.length||0
      if(this.renShu>0){
        fenPei.studentIds=res
      }

        console.log(res,"res")
      })
      this.fenPeiSchool=fenPei
    },
    fenPei(){
      this.$refs.fenPeiSchool.validate((valid) => {
        if (valid) {
      this.$confirm('确认要分配该些学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        fenPeiXueXiao(this.fenPeiSchool, this.prefixDeptCode).then(res => {
          if (res) {
            this.$message({
              type: 'success',
              message: '分配成功!'
            })
            this.getTableData()
            this.fenPeiSchool = {}
            this.fenPeiSchoolShow = false
          }
        })
      })
        }
      })
    },
    faSongLuQu(){
      this.faSongLuQuShow=true
      this.luQuTongZhi.content=""
      this.luQuTongZhi.studentIds=[]
      let studentIds=[]
      let luqu1= {}
      luqu1=this.search
      this.idList.forEach((item) =>{
        studentIds.push(item.id)
      })
      luqu1.studentIds=studentIds
      getFaSongLuQuTongZhiRenShu(luqu1,this.prefixDeptCode).then(res=>{
      luqu1.renShu=res.length
        luqu1.studentIds=res
        this.luQuTongZhi=luqu1
      })
    },
    faSongLuQuJieGuo(){
      this.$confirm('确认要为该些学生发送录取通知', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        faSongLuQuTongZhi(this.luQuTongZhi, this.prefixDeptCode).then(res => {
          if (res) {
            this.$message({
              type: 'success',
              message: '发送成功!'
            })
            this.getTableData()
            this.luQuTongZhi = {}
            this.faSongLuQuShow = false
          }
        })
      })
    },
    youfu(row){
      this.youFu.id=row.id;
      this.youFuShow=true;
    },
    youFu1(){
      addYouFuLeiXing(this.youFu,this.prefixDeptCode).then(res=>{
        if(res){
          this.$message.success("添加优抚对象成功")
          this.youFuShow=false
          this.youFu={}
        }
      })
    },
    exportData(){
      let params = this.search;
      this.$download(
          `${pref}${this.prefixDeptCode}/five/export/exportStudentPaper`,
          params,
          "xls",
          "不确定拟报名学校统计列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    close(val){
      this.switchModal('add', false)
    },
    searchs(val) {
      this.search=val
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.idList=val
    },
    getTableData1(){
      this.search.pageNumber=1
      this.getTableData()
    },
    getTableData() {
      getshiWuQuList(this.search,this.prefixDeptCode).then((res) =>{
        console.log(res,this.search,"res")
        this.total=Number(res.total)
        this.signNum=res.signNum
        this.tableData=res

      })
    },
    piLiangShenHeTongGuo(){
      this.$confirm('确认完成审核？所有初审已通过的学生，教育局审核状态都会改为已通过，请慎重使用。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let studentIds=[]
        let shenHe=this.search
        this.idList.forEach((item) =>{
          studentIds.push(item.id)
        })
        shenHe.studentIds=studentIds
        baoMingPiliangShenHeTongGuo(shenHe,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.idList=[]
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    gongShiLuQuJieGuo(){
      this.$confirm('确认要公布录取结果吗？此操作会将所有审核通过的学生将审核状态改为“成功录取”，请慎重使用', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let studentIds=[]
        let gongShi=this.search
        this.idList.forEach((item) =>{
          studentIds.push(item.id)
        })
        gongShi.studentIds=studentIds
        gongShiLuQuJieGuo(gongShi,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '公布录取结果成功!'
            });
            this.idList=[]
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });

    },
    xiuGaiXinXi(){
      this.status=2
      this.boHuiYuanYin=1
    },
    buKeZaiBao(){
      this.status=3
      this.boHuiYuanYin=2
    },
    tiaoJi(){
      this.$refs.detail.tiaoJi()
    },
    tiaoJi1(){
      this.$refs.tiaoJiXinXi.validate((valid) => {
        if (valid) {
      this.$confirm('确认要分配该学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
       fenPeiXueXiao(this.tiaoJiXinXi,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '分配成功!'
            })
            this.getTableData()
            this.tiaoJiXinXi={}
            this.tiaoJiShow=false
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
        }
      })
    },
    tongGuo(){
      this.$refs.detail.tongGuo()
    },
    buTongGuo(){
      this.$refs.detail.buTongGuo()
    },
    shangYiTiao(){
      this.$refs.detail.shangYiTiao()
    },
    xiaYiTiao(){
      this.$refs.detail.xiaYiTiao()
    },
    fanHui(){
      this.$refs.detail.fanHui()
      this.switchModal('detail', false)
    },
    save(){
      this.$refs.addForm.save()
      // this.switchModal('add', false)
    },
    add(){
      this.switchModal("add", true)
    },
    // 详情
    detail(row) {
      this.row=row
      this.selectRow=this.search
      console.log(this.search,"search")
      console.log(row);
      this.switchModal('detail', true)
    },
    shenHeBuTongGuo(){
      this.$confirm('确认该学生信息无误，审核不通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeBuTongGuo({id:this.id,status:this.status,reason:this.reason},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.dialogVisible=false
            this.id=''
            this.status=''
            this.reason=''
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });

    },
    beiZhuAdd(){
      this.$confirm('确认该学生信息无误，审核通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeTongGuo({id:this.id,mark:this.mark},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.id=''
            this.mark=''
            this.beiZhuShow=false
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    quXiao(){
      this.chongXinShow=false
      this.deleteShow=false
      this.deleteForm={}
      this.duiKouShow=false
      this.baoDaoShow=false
      this.fenPeiSchool={}
      this.youFu={}
      this.tiaoJiXinXi={}
      this.idList=[]
      this.id=''
      this.mark=''
      this.reason=''
      this.status=''
      this.youFuShow=false
      this.dialogVisible=false
      this.beiZhuShow=false
      this.tiaoJiShow=false
      this.fenPeiSchoolShow=false
      this.faSongLuQuShow=false
      this.cheXiaoForm.content=''
      this.cheXiaoForm.id=''
      this.cheXiaoShow=false
      this.getTableData()
    },
    // 通过
    pass(row) {
      if(this.role == 'COUNTY_ADMIN' || this.role == 'AUDITOR'){
        this.$confirm('确认该学生信息无误，审核通过', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          baoMingShenHeTongGuo({id:row.id,mark:this.mark},this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '审核成功!'
              });
              this.id=''
              this.mark=''
              this.beiZhuShow=false
              this.getTableData()
            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.beiZhuShow=true
        this.id=row.id
      }
    },
    // 不通过
    fail(row) {
      this.dialogVisible=true
      this.id=row.id
    },
    // 调剂
    adjust(row) {
      console.log(row,"row")
      this.tiaoJiXinXi={
        studentIds: [row.id],
      }
      this.tiaoJiShow=true
    },
    chongXinadjust(row){
      this.chongXin.studentId=row.id
      this.chongXinShow=true
    },
    chongXin1(){
      this.$refs.chongXin.validate((valid) => {
        if (valid) {
      this.$confirm('确认要分配该些学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        chongXinFenPei(this.chongXin, this.prefixDeptCode).then(res => {
          if (res) {
            this.$message({
              type: 'success',
              message: '分配成功!'
            })
            this.getTableData()
            this.chongXin = {}
            this.chongXinShow = false
          }
        })
      })
        }
      })
    },
    tiaoJiJiLu(row){
      this.tiaoJiJiLuShow=true
      tiaoJiJiLu({id:row.id},this.prefixDeptCode).then(res=>{
        this.jiLuData=res
      })
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
    // 报到
    report(row) {
      console.log(row,"row")
      this.baodao.studentId=row.id
      this.baoDaoShow=true

    },
    baoDao(){
      this.$confirm('确认该学生'+`${this.baodao.status==2?'未报到':'报到'}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        xuShengBaoDao(this.baodao,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          }
          this.baoDaoShow=false
          this.getTableData()
        })


      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
