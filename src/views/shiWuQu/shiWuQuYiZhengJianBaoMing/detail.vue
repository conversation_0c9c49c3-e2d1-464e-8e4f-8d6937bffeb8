<template>
  <div>
    <h5 v-if="form.student!=null">学生基本信息</h5>
    <!--     v-for="item in form.student"-->
    <el-descriptions v-if="form.student!=null"  title=""  :colon='false'>
      <el-descriptions-item  v-for="item in form.student" :key="item.sort"><span :style="item.redFlag?'color:red':''" >{{item.filedName+':'+item.value}}</span> </el-descriptions-item>
    </el-descriptions>
    <div v-if="form.hukou!=null">
      <h5>户口信息</h5>
      <el-descriptions  title=""  :colon='false'>
        <el-descriptions-item   v-for="item in form.hukou" :key="item.sort"><span :style="item.redFlag?'color:red':''" >{{item.filedName+':'+item.value}}</span> </el-descriptions-item>
      </el-descriptions>
      <div v-if="form.hukouImageList!=null">
        <ul >
          <viewer v-if="form.hukouImageList!=null"  :images="form.hukouImgList" class="images" >
            <div  v-for="(o, index) in form.hukouImgList" :key="index" style="padding: 20px">
              <!--                <el-card :body-style="{ padding: '0px' }">-->
              <!--                  <div style="padding: 14px;">-->
              <!--                    <span>{{o.title}}</span>-->
              <!--                  </div>-->
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span :style="o.redFlag?'color:red':''">{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <h5 v-if="form.twins!=null">多胞胎</h5>
    <el-table
        v-if="form.twins!=null"
        :data="form.twins"
        style="width: 100%;"
        border
    >
      <el-table-column
          prop="name"
          label="姓名"
          width="180"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.name.redFlag?'color:red':''"> {{scope.row.name.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.idCard.redFlag?'color:red':''"> {{scope.row.idCard.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="gender"
          label="性别"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.gender.redFlag?'color:red':''"> {{scope.row.gender.value==1?'男':'女'}}</span></template>
      </el-table-column>
    </el-table>

    <h5 v-if="form.guardian!=null">监护人信息</h5>
    <el-table
        v-if="form.guardian!=null"
        :data="form.guardian"
        style="width: 100%;"
        border
    >
      <el-table-column
          prop="guardianName"
          label="姓名"
          width="180"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.guardianName.redFlag?'color:red':''"> {{scope.row.guardianName.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.idCard.redFlag?'color:red':''"> {{scope.row.idCard.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="birthday"
          label="出生日期"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.birthday.redFlag?'color:red':''"> {{scope.row.birthday.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="nation"
          label="民族"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.nation.redFlag?'color:red':''"> {{scope.row.nation.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="relation"
          label="关系"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.relation.redFlag?'color:red':''"> {{scope.row.relation.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="phone"
          label="联系电话"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.phone.redFlag?'color:red':''"> {{scope.row.phone.value}}</span></template>
      </el-table-column>
      <el-table-column
          prop="address"
          label="户籍所在地"
          align="center"
      >
        <template slot-scope="scope" ><span :style="scope.row.address.redFlag?'color:red':''"> {{scope.row.address.value}}</span></template>
      </el-table-column>
    </el-table>
    <div>
      <ul v-if="form.guardianImgList!=null">
        <viewer  :images="form.guardianImgList" class="images" >
          <div  v-for="(o, index) in form.guardianImgList" :key="index" style="padding: 20px">
            <!--                <el-card :body-style="{ padding: '0px' }">-->
            <!--                  <div style="padding: 14px;">-->
            <!--                    <span>{{o.title}}</span>-->
            <!--                  </div>-->
            <div v-if="o.path!=''">
              <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
              <div style="padding:10px;">
                <span :style="o.redFlag?'color:red':''">{{o.name}}</span>
              </div>
            </div>
            <!--                </el-card>-->
          </div>
        </viewer>
      </ul>
    </div>
    <div v-if="form.otherImgList!=null">
      <ul>
        <viewer :images="form.otherImgList" class="images" >
          <div  v-for="(o, index) in form.otherImgList" :key="index" style="padding: 20px">
            <!--                <el-card :body-style="{ padding: '0px' }">-->
            <!--                  <div style="padding: 14px;">-->
            <!--                    <span>{{o.title}}</span>-->
            <!--                  </div>-->
            <div v-if="o.path!=''">
              <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
              <div style="padding:10px;">
                <span :style="o.redFlag?'color:red':''">{{o.name}}</span>
              </div>
            </div>

            <!--                </el-card>-->
          </div>
        </viewer>
      </ul>
    </div>
    <div v-if="form.house.length>0">
      <div :style="form.house[0].redFlag?'color:red':''">{{form.house[0].value}}</div>
      <el-descriptions  title=""  :colon='false'>
        <el-descriptions-item v-for="item in form.house" :key="item.sort" v-if="item.reflectName!='houseTypeStr'"><span :style="item.redFlag?'color:red':''" >{{item.filedName+':'+item.value}}</span> </el-descriptions-item>
      </el-descriptions>
      <div>
        <ul>
          <viewer v-if="form.houseImgList!=null"  :images="form.houseImgList" class="images" >
            <div  v-for="(o, index) in form.houseImgList" :key="index" style="padding: 20px">
              <!--                <el-card :body-style="{ padding: '0px' }">-->
              <!--                  <div style="padding: 14px;">-->
              <!--                    <span>{{o.title}}</span>-->
              <!--                  </div>-->
              <div v-if="o.path!=''">
                <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
                <div style="padding:10px;">
                  <span :style="o.redFlag?'color:red':''">{{o.name}}</span>
                </div>
              </div>

              <!--                </el-card>-->
            </div>
          </viewer>
        </ul>
      </div>
    </div>
    <div >
      <h5 v-if="form.juzhu!=null">外地户籍学生补充信息</h5>
      <el-descriptions  title=""  :colon='false' v-if="form.juzhu!=null">
        <el-descriptions-item v-for="item in form.juzhu[0]" :key="item.sort" ><span :style="item.redFlag?'color:red':''" >{{item.filedName+':'+item.value}}</span> </el-descriptions-item>
      </el-descriptions>
      <el-descriptions  title=""  :colon='false' v-if="form.juzhu!=null">
        <el-descriptions-item v-for="item in form.juzhu[1]" :key="item.sort" ><span :style="item.redFlag?'color:red':''" >{{item.filedName+':'+item.value}}</span> </el-descriptions-item>
      </el-descriptions>
      <viewer v-if="form.juzhuImgList!=null" :images="form.juzhuImgList" class="images" >
        <div   v-for="(o, index) in form.juzhuImgList" :key="index" style="padding: 20px">
          <!--                <el-card :body-style="{ padding: '0px' }">-->
          <!--                  <div style="padding: 14px;">-->
          <!--                    <span>{{o.title}}</span>-->
          <!--                  </div>-->
          <div v-if="o.path!=''">
            <img :src="imgQianZhui+o.path" class="image" :alt="o.title+'-'+o.name" >
            <div style="padding:10px;">
              <span :style="o.redFlag?'color:red':''">{{o.name}}</span>
            </div>
          </div>

          <!--                </el-card>-->
        </div>
      </viewer>
    </div>

    <h5>审核情况</h5>
    <el-table
        :data="form.auditDetails"
        style="width: 100%"
        height="300px"
        border
    >
      <el-table-column
          prop="createTime"
          label="操作时间"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="roleName"
          label="角色"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="studentName"
          label="姓名"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="creatorName"
          label="操作账号"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="typeDes"
          label="操作类型"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="content"
          label="操作内容"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          label="操作"
          width="80"
          fixed="right"
          align="center"
      >
        <template slot-scope="{ row }" v-if="row.mongoStudentId!=null">
          <el-link
              type="primary"
              :underline="false"
              @click="detail(row)"
          >详情</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        title="不通过原因"
        :visible.sync="dialogVisible"
        align="center"
        width="500px"
        append-to-body
    >
      <div>
        <el-radio-group v-model="status">
          <el-radio :label="2">驳回-修改信息</el-radio>
          <el-radio :label="4" v-if="role == 'SCHOOL'">退回教育局</el-radio>
        </el-radio-group>
<!--        <el-button type="primary" @click="xiuGaiXinXi">驳回-修改信息</el-button>-->
<!--        <el-button type="info" @click="buKeZaiBao" v-if="roel=='SCHOOL'">退回教育局</el-button>-->
        <el-input type="textarea" placeholder="请输入驳回原因" v-model="reason" style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="shenHeBuTongGuo"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="备注"
        :visible.sync="beiZhuShow"
        align="center"
        width="500px"
        append-to-body
    >
      <div>
        <el-input type="textarea" placeholder="请输备注" v-model="mark" style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="beiZhuAdd"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="调剂"
        :visible.sync="tiaoJiShow"
        width="500px"
        append-to-body
    >
      <div style="display:flex;width: 100%;justify-content: space-between"><span>ID:{{ tiaoJiXinXi.signId }}</span><span>学生姓名:{{tiaoJiXinXi.studentName}}</span><span>报名学校:{{tiaoJiXinXi.schoolName}}</span> </div>
      <p>调剂学校选择：</p>
      <div>
        <el-select
            v-model="tiaoJiXinXi.adjustSchoolId"
            placeholder="选择调剂学校"
            clearable
            filterable
        >
          <el-option
              v-for="item in tiaoJiSchoolList"
              :key="item.schoolId"
              :label="item.schoolName"
              :value="item.schoolId"
          >
          </el-option>
        </el-select>
      </div>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="tiaoJi1"
      >确 定</el-button>
    </span>
    </el-dialog>
    <el-dialog
        :title="title"
        width="800px"
        append-to-body
        :show-close="false"
        :visible.sync="modal.detail"
        :close-on-click-modal="false"
        center
    >
      <div>
        <component
            v-if="modal.detail"
            :ref="'detail1'"
            :is="'detail1'"
            :row="row1"
            :data=null
            :mode=null
        />
        <!--        @save-complete="dialogFormComponent.saveComplete"-->
      </div>
      <div class="flex-center" style="margin-top:20px ">
        <el-button
            type="default"
            @click="fanHui1"
            size="small"
        >返回</el-button
        >
        <!--        || role == 'AUDITOR'-->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  baoMingShenHeBuTongGuo,
  baoMingShenHeTongGuo,
  getshiWuQuDetail, getshiWuQuDetail1, getshiWuQuList,
  studentTiaoJiao, tiaoJiaoXueXiaoList
} from "@/api/shiWuQuBaoMingLieBiao";
import modalMixin from "@/mixins/ModalMixin";
import detail1 from "@/views/shiWuQu/shiWuQuYiZhengJianBaoMing/detail1";
import {pref} from "@/utils/common";
export default {
  props:{row:[Object], dataRow:[Object]},
  name: "detail",
  mixins:[modalMixin],
  data(){
    return{
      title:'',
      modal: {
        detail: false,
      },
      row1:'',
      role: this.$store.getters.role,
      id:'',//学生ID
      mark:'',//学生审核通过备注
      status:'',//学生驳回类型
      reason:'',
      boHuiYuanYin:'',
      tiaoJiSchoolList:[],
      tiaoJiXinXi:{},
      tiaoJiShow:false,
      beiZhuShow:false,
      dialogVisible:false,
      prefixCode:this.$store.getters.prefixCode,
      prefixDeptCode: this.$store.getters.deptCode,
      imgQianZhui:`${process.env.VUE_APP_BASE_API}${pref}${this.$store.getters.deptCode}`,
      hukouImage: [],
      jianHuRenImage:[],
      buChongImage:[],
      fangChanZhengImage:[],
      buDongChanImage:[],
      gouFangImage:[],
      xiaoChanQuanImage:[],
      lianZuFagnImage:[],
      ziRanCunImage:[],
      juzhuZhengImage:[],
      form:{},
      search:{},
      total:0,
      tableData:[],
      studentId:'', //下一条学生id
      index:0,//下一条学生index值
    }
  },
  components:{detail1},
  created() {
    this.getDetail()
    this.search=this.dataRow
    console.log(this.prefixDeptCode,this.search)
    // getshiWuQuList(this.search,this.prefixDeptCode).then(data => {
    //   this.tableData = data.records
    //   this.total=Math.ceil(data.total/this.search.pageSize)
    //   //console.log(this.studentId)
    //
    // })

  },
  methods:{
    detail(row){
      this.row1=row
      // this.selectRow=this.search
      console.log(this.search,"search")
      console.log(row);
      this.title=row.createTime+row.typeDes+ '信息详情'
      this.switchModal('detail', true)
    },
    getDetail(){
      getshiWuQuDetail1({key:this.row.id},this.prefixDeptCode).then(res=>{
        this.form=res
      })
    },
    tiaoJi() {
      this.tiaoJiXinXi={
        id: this.form.id,
        signId: this.form.signId,
        studentName:this.form.studentName,
        schoolName:this.form.schoolName,
      }
      tiaoJiaoXueXiaoList({key:this.form.id},this.prefixDeptCode).then(res=>{
        this.tiaoJiSchoolList=res
      })
      this.tiaoJiShow=true
    },
    tiaoJi1() {
      this.$confirm('确认要调剂该学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        studentTiaoJiao(this.tiaoJiXinXi,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '调剂成功!'
            })
            this.getForm(this.tiaoJiXinXi.id)
            this.tiaoJiXinXi={}
            this.tiaoJiShow=false
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    buTongGuo() {
      this.dialogVisible=true
    },
    fanHui1(){
      this.switchModal('detail', false)
    },
    fanHui(){
      if(this.search.pageNumber>this.total){
        this.search.pageNumber=this.search.pageNumber-1
      }

      this.$emit("searchs",this.search)
      this.index=0
    },
    beiZhuAdd(){
      this.$confirm('确认该学生信息无误，审核通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeTongGuo({id:this.form.id,mark:this.mark},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.getForm(this.form.id)
            this.form.id=''
            this.mark=''
            this.beiZhuShow=false

          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });},
    shenHeBuTongGuo(){
      this.$confirm('确认该学生信息无误，审核不通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeBuTongGuo({id:this.form.id,status:this.status,reason:this.reason},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.dialogVisible=false
            this.getForm(this.form.id)
            this.form.id=''
            this.status=''
            this.reason=''
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    tongGuo() {
      if(this.role == 'COUNTY_ADMIN' || this.role == 'AUDITOR'){
        this.$confirm('确认该学生信息无误，审核通过', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          baoMingShenHeTongGuo({id:this.form.id,mark:this.mark},this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '审核成功!'
              });
              this.getForm(this.form.id)
              this.form.id=''
              this.mark=''
              this.beiZhuShow=false

            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.beiZhuShow=true
      }
    },
    quXiao(){
      this.dialogVisible=false
      this.beiZhuShow=false
      this.tiaoJiShow=false
      this.tiaoJiXinXi={}
      this.id=''
      this.mark=''
      this.reason=''
      this.status=''
    },
    xiuGaiXinXi(){
      this.status=2
      this.boHuiYuanYin=1
    },
    buKeZaiBao(){
      this.status=3
      this.boHuiYuanYin=2
    },
    shangYiTiao() {
      if (
          this.index < this.tableData.length - 1 &&
          this.search.pageNumber >= 1
      ) {
        this.tableData.forEach((element, index) => {
          if (element.id == this.form.id) {
            if (index == 0) {
              this.$message.error("已是第一条数据");
            } else {
              this.studentId = this.tableData[index - 1].id;
              this.index = index - 1;
              this.getForm(this.studentId);
            }
          }
        });
      } else {
        this.search.pageNumber = this.search.pageNumber - 1;
        if (this.search.pageNumber < 1) {
          this.$message.error("已是第一条数据");
        } else {
          this.index = 0;
          getshiWuQuList(this.search,this.prefixDeptCode).then((res) => {
            this.tableData = res.data.list;
            this.studentId = this.tableData[this.tableData.length - 1].id;
            this.index = 0;
            this.getForm(this.studentId);
          });
        }
      }
    },
    xiaYiTiao() {
      // console.log('下一条',this.tableData.length,this.search,this.total)
      if(this.index<this.tableData.length-1&&this.search.pageNumber<=this.total){
        this.tableData.forEach((element,index)=>{
          if(element.id ==this.form.id){
            if(index==this.tableData.length-1){
              this.$message.error("已是最后一条数据")
            }else {
              this.studentId=this.tableData[index+1].id
              this.index=index+1
              this.getForm(this.studentId)
              //console.log(this.studentId)
            }

          }
        })
      }else {
        if(this.search.pageNumber<this.total){
          this.search.pageNumber=this.search.pageNumber+1
          if(this.search.pageNumber>this.total){
            this.$message.error("已是最后一条数据")
          }else {this.index=0
            getshiWuQuList(this.search,this.prefixDeptCode)
                .then(data => {
                      this.tableData = data.records
                      this.studentId=this.tableData[0].id
                      this.index=0
                      this.total=Math.ceil(data.total/this.search.pageSize)
                      this.getForm(this.studentId)
                      //console.log(this.studentId)

                    }
                )
          }
        }else {
          this.$message.error("已是最后一条数据")
        }


      }
    },
    getForm(row){
      this.myWorksImgsArray=[]
      this.FormList=[]
      getshiWuQuDetail1({key:row},this.prefixDeptCode).then(res=>{
        this.form=res
      })
    },
  }
}
</script>

<style scoped lang="scss">
.image {
  width: 100%;
  height:80px;
  //display: block;
}
.images {
  display: grid;
  grid-template-columns: 150px 150px 150px 150px;
  grid-template-rows: 120px 120px 120px;
}
.pic-box {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  .el-image {
    width: 150px;
    height: 100px;
    border: 1px solid #dadada;
    border-radius: 4px;
  }
  p {
    text-align: center;
  }
}
</style>
