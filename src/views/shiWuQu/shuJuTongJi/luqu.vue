<template>
  <div>
<!--    <el-button type="primary" size="small" @click="exportList">导出</el-button>-->
    <el-table :data="tableData.records" border stripe @selection-change="handleSelectionChange">
      <el-table-column
          align="center"
          label="序号"
          prop="index"
          width="90"
      >
        <template slot-scope="scope">
          <span>{{
              scope.$index + (search.pageNumber - 1) * search.pageSize + 1
            }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="学校名称"
          prop="schoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="选择拟报名学校人数（含已调出）"
          prop="schoolNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="不确定拟报名学校人数（含学校退回教育局未重新分配人数）"
          prop="distributeNum"
      ></el-table-column>
<!--      ，以教育局审核通过为主-->
      <el-table-column
          align="center"
          label="本区县调出人数"
          prop="adjustToNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="本区县调入人数"
          prop="adjustFromNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="市级调入人数"
          prop="spanToNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="市级调出人数"
          prop="spanFormNum"
      ></el-table-column>
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="是否公示"-->
<!--          prop="publicStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <template>{{scope.row.publicStatus==0?'未公布':scope.row.publicStatus==1?'公布':''}}</template>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column
          align="center"
          label="发送录取通知书/条"
          prop="noticeNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="公示/条"
          prop="publicNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报到/条"
          prop="signNum"
      ></el-table-column>
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="是否报到"-->
<!--          prop="signStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <template>{{scope.row.signStatus==0?'待报到':scope.row.signStatus==1?'报到':scope.row.signStatus==2?'未报到':''}}</template>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column
          align="center"
          label="最终录取人数（含多胞胎）"
          prop="enrollNum"
      ></el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="$pageSizes"
      >
        <!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getShuWuLuQuQuTongJiList, getShuWuQuTongJiList2} from "@/api/shiWuQuShuJuTongJi";
import {pref} from "@/utils/common";
export default {
  name: "luqu",
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      total:0,
      search: {
        pageNumber: 1,
        pageSize: 10,
      },
      tableData: {
        records:[]
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods:{
    exportList() {
      let params = {};
      this.$download(
          `${pref}${this.prefixDeptCode}/five/statistic/export`,
          params,
          "xls",
          "市五区统计列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    getTableData(){
      getShuWuLuQuQuTongJiList(this.search,this.prefixDeptCode).then(res=>{
        console.log(res,"res")
        this.tableData = res
        this.total =Number(res.total)
      })
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
  }
}
</script>

<style scoped>

</style>
