<template>
  <div>
<!--    <el-button type="primary" size="small" @click="exportList">导出</el-button>-->
    <el-table :data="tableData.records" border stripe @selection-change="handleSelectionChange">
      <el-table-column
          align="center"
          label="序号"
          prop="index"
          width="90"
      >
        <template slot-scope="scope">
          <span>{{
              scope.$index + (search.pageNumber - 1) * search.pageSize + 1
            }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="报名学校"
          prop="schoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="计划招生数"
          prop="planNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="房户一致两对口"
          prop="hukouAndHouseNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="户口一对口"
          prop="hukouNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="房产一对口"
          prop="houseNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="随迁子女"
          prop="juzhuNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="跨区调入"
          prop="spanNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="合计"
          prop="total"
      ></el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="$pageSizes"
      >
        <!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getShuWuQuTongJiList, getShuWuQuTongJiList2} from "@/api/shiWuQuShuJuTongJi";
import {pref} from "@/utils/common";
export default {
  name: "index",
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      total:0,
      search: {
        pageNumber: 1,
        pageSize: 10,
      },
      tableData: {
        records:[]
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods:{
    exportList() {
      let params = {};
      this.$download(
          `${pref}${this.prefixDeptCode}/five/statistic/export`,
          params,
          "xls",
          "市五区统计列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    getTableData(){
      getShuWuQuTongJiList(this.search,this.prefixDeptCode).then(res=>{
        console.log(res,"res")
         this.tableData = res
         this.total =Number(res.total)
      })
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
  }
}
</script>

<style scoped>

</style>
