<template>
  <div>
    <el-table
        :data="tableData"
        border
        stripe
        v-loading="tableLoading"
    >
      <el-table-column
          prop="menuName"
          label="对应模块"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="exportName"
          label="对应文件"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="excelName"
          label="导出表格名称"
          align="center"

      >
        <template slot-scope="scope">
          <el-popover
              placement="bottom"
              width="auto"
              height="400"
              trigger="click"
          >
            <div>
              <el-input
                  v-model="BiaoGeName.excelName"
                  placeholde="请输入名称"
                  @input="change"
              ></el-input>
              <div class="flex-center">
                <div class="flex-center sd-m-t-15">
                  <el-button type="plain" @click="quXiao" size="small"
                  >取 消</el-button
                  >
                  <el-button
                      type="primary"
                      @click="queDing(scope.$index)"
                      size="small"
                  >保 存</el-button
                  >
                </div>
              </div>
            </div>
            <span
                @click="editBiaoGeShow(scope.row)"
                style="cursor: pointer"
                slot="reference"
            >
              {{ scope.row.excelName }}
              <el-link type="info" :underline="false" icon="el-icon-edit">
              </el-link>
            </span>
          </el-popover>
        </template>
<!--        <el-link slot-scope="{row}" type="primary" @click="editBiaoGeShow(row)"  :underline="false">{{row.excelName }}</el-link>-->
      </el-table-column>
      <el-table-column
          label="操作"
          width="200"
          fixed="right"
          align="center"
      >
        <template slot-scope="{ row }">
          <el-link
              type="primary"
              :underline="false"
              @click="ziDuanEdit(row)"
              icon="el-icon-s-tools"
          >设置</el-link
          >
          <el-link
              type="success"
              :underline="false"
              @click="ziDuanDetail(row)"
              icon="el-icon-view"
          >查看</el-link
          >
        </template>
      </el-table-column>
    </el-table>
<!--    <div class="page-container">-->
<!--      <el-pagination-->
<!--          background-->
<!--          @size-change="handleSizeChange"-->
<!--          @current-change="handleCurrentChange"-->
<!--          :current-page.sync="search.pageNumber"-->
<!--          layout="total, prev, pager, next, sizes"-->
<!--          :page-sizes="$pageSizes"-->
<!--          :total="total"-->
<!--      >-->
<!--      </el-pagination>-->
<!--    </div>-->
    <el-dialog
        title="导出文件名称"
        :visible.sync="BiaoGeShow"
        align="center"
        width="500px"
    >
      <span>excel名称：</span>
      <el-input v-model="BiaoGeName.excelName
" placeholder="请输入表格新名称" style="width: 300px" @input="change"></el-input>
      <div style="margin-top: 40px">
        <el-button @click="quXiao">关闭</el-button>
        <el-button type="primary" @click="queDing">保存</el-button>
      </div>

    </el-dialog>
    <el-dialog
        :title="title"
        :visible.sync="show"
        align="center"
        width="450px">
      <el-tree
          :data="tableData1"
          show-checkbox
          node-key="id"
          :default-checked-keys="XiuGaiXuanZhong"
          ref="menu"
          :props="defaultProps">
            <span class="custom-tree-node" slot-scope="{ node, data }">
        <span>{{ data.groupName}}{{data.exportName}}</span>
            </span>
      </el-tree>
      <div style="margin-top: 40px">
        <el-button @click="quXiao">关闭</el-button>
        <el-button type="primary" @click="queDing1" :loading="loading">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog
        :title="title1"
        :visible.sync="show1"
        align="center"
        width="450px">
      <el-tree
          :data="tableData2"
          node-key="id"
          ref="menu"
          :props="defaultProps">
            <span class="custom-tree-node" slot-scope="{ node, data }" >
        <span>{{ data.groupName}}{{data.exportName}}</span>
            </span>
      </el-tree>
    </el-dialog>
  </div>
</template>

<script>
import tableMixin from "@/mixins/TableMixin";
import {getshiWuQuExportMuBanList,getshiWuQuSheZhiList,baoCunSheZhi,baoCunMuBan} from  "@/api/shiWuQuExportSheZhi"
export default {
  name: "ShiwuquExport",
  mixins:[tableMixin],
  props: {
    deptCode:{
      type: String,
      default: ''},
    deptId:{
      type: String,
      default: ''
    },
    deptName:{
      type: String,
      default: ''
    }
  },
  data(){
    return{
      defaultProps: {
        children: "children",
        label: 'groupName'
      },
      loading:false,
      XiuGaiXuanZhong:[],
      XiuGaiXuanZhong1:[],
      activeName: '1',
      title:'',
      title1:'',
      tableData1: [
          ],
      tableData2:[],
      tableData:[],
      BiaoGeName:{
        excelName:'',
        id:''
      },
      sheZhi:{},
      multipleSelection:[],
      BiaoGeShow:false,
      show:false,
      show1:false,
    }
  },
  created() {

    },
  methods:{
    ziDuanDetail(row){
      this.show1=true
      //this.deptName+'-'+row.menuName+'-'+
      this.title1=row.excelName +'字段详情'
      this.tableData2=[]
      getshiWuQuSheZhiList({key:row.exportType},this.deptCode).then(res=>{
        let tableData=res
        let tableData3=res
        this.tableData2=res
        tableData.forEach((element,index)=>{

              tableData3[index].children=element.children.filter((num)=>{
                return  num.displayStatus==1
              })
              // this.XiuGaiXuanZhong1.push(element1.id)

        })
          this.tableData2=tableData3.filter((num)=>{return num.children.length!=0})
      })
    },
    change(){
      this.$forceUpdate()
    },
    editBiaoGeShow(row){
      // this.BiaoGeShow=true
      console.log(row,"row")
      this.BiaoGeName.id=row.id
      this.BiaoGeName.excelName =row.excelName
    },
    queDing(index){
      baoCunMuBan(this.BiaoGeName,this.deptCode).then(res=>{
        if(res){
          this.tableData[index].excelName = this.BiaoGeName.excelName

          // this.BiaoGeName.id=''
          // this.BiaoGeName.excelName =''
          this.BiaoGeShow=false
          this.getTableData()
          this.$message.success("设置成功")
          this.quXiao();
        }
      })

    },
    queDing1(){
    this.loading=true
      let menuIds = this.$refs.menu.getCheckedKeys().concat(this.$refs.menu.getHalfCheckedKeys())
      let menuIds3=[]
      let form={}
      form.exportType=this.tableData1[0].exportType
      this.tableData1.forEach(element1=>{
        if(element1.children!=null){
          element1.children.forEach(element2=>{
            menuIds3.push(element2)
          })
        }

      })
      console.log(this.tableData1,menuIds)
      menuIds3.forEach(element=>{
        element.displayStatus=2
      })
      menuIds3.forEach(element=>{
        menuIds.forEach(element1=>{
          if(element.id==element1){
            element.displayStatus=1
          }
        })

      })
      form.template=menuIds3
      console.log(form,menuIds)
      baoCunSheZhi(form,this.deptCode).then(res=>{
        if(res){
          this.show=false
          this.getTableData()
          menuIds3=[]
          this.$message.success("设置成功")
        }

      }).finally(
          ()=>{
            this.loading=false
          }
      )
      this.sheZhi={}
    },
    quXiao(){
      const popovers = document.querySelectorAll(".el-popover");
      popovers.forEach((popover) => {
        popover.style.display = "none";
      });
      this.BiaoGeName.id=''
      this.BiaoGeName.excelName =''
      this.BiaoGeShow=false
      this.show=false
      this.sheZhi={}
    },
    ziDuanEdit(row){
      this.XiuGaiXuanZhong=[]
      //this.deptName+'-'+row.menuName+'-'+
      this.title=row.excelName +'字段配置'
      getshiWuQuSheZhiList({key:row.exportType},this.deptCode).then(res=>{
        this.tableData1=res
        this.tableData1.forEach(element=>{
          if(element.children!=null){
            element.children.forEach(element1=>{
              //displayStatus==1是勾选2是未勾选
              if(element1.displayStatus==1){
                this.XiuGaiXuanZhong.push(element1.id)
              }
            })
          }

        })
      })
      this.show=true
    },
    getTableData(){
      getshiWuQuExportMuBanList({},this.deptCode).then(res=>{
        this.tableData=res
      })
    }
  }
}
</script>

<style>

</style>
