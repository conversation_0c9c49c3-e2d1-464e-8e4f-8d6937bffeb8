<template>
  <div>
    <div class="time-set">
      <el-card class="box-card">
        <div slot="header">
          <span>小学报名年龄</span>
        </div>
        <el-form :model="ageForm" label-width="100px">
          <el-form-item prop="beginTime" label="开始时间">
            <el-date-picker
                size="small"
                v-model="ageForm.beginTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmStart(ageForm,4)"
                placeholder="请选择开始时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="endTime" label="结束时间">
            <el-date-picker
                size="small"
                v-model="ageForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmEnd(ageForm,4)"
                placeholder="请选择结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="flex-end">
          <el-button
              type="success"
              size="small"
              :disabled="!ageForm.beginTime || !ageForm.endTime"
              @click="confirmSet(4)"
          >确定</el-button
          >
        </div>
      </el-card>
      <el-card class="box-card">
        <div slot="header">
          <span>选择拟报名学校开始时间</span>
        </div>
        <el-form :model="yiXueXiaoBaoMingForm" label-width="100px">
          <el-form-item prop="beginTime" label="开始时间">
            <el-date-picker
                size="small"
                v-model="yiXueXiaoBaoMingForm.beginTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmStart(yiXueXiaoBaoMingForm,1)"
                placeholder="请选择开始时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="endTime" label="结束时间">
            <el-date-picker
                size="small"
                v-model="yiXueXiaoBaoMingForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmEnd(yiXueXiaoBaoMingForm,1)"
                placeholder="请选择结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="flex-end">
          <el-button
              type="success"
              size="small"
              :disabled="!yiXueXiaoBaoMingForm.beginTime || !yiXueXiaoBaoMingForm.endTime"
              @click="confirmSet(1)"
          >确定</el-button
          >
        </div>
      </el-card>
      <el-card class="box-card">
        <div slot="header">
          <span>不确定拟报名学校开始时间</span>
        </div>
        <el-form :model="yiZhengJianBaoMingForm" label-width="100px">
          <el-form-item prop="beginTime" label="开始时间">
            <el-date-picker
                size="small"
                v-model="yiZhengJianBaoMingForm.beginTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmStart(yiZhengJianBaoMingForm,2)"
                placeholder="请选择开始时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="endTime" label="结束时间">
            <el-date-picker
                size="small"
                v-model="yiZhengJianBaoMingForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmEnd(yiZhengJianBaoMingForm,2)"
                placeholder="请选择结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="flex-end">
          <el-button
              type="success"
              size="small"
              :disabled="!yiZhengJianBaoMingForm.beginTime || !yiZhengJianBaoMingForm.endTime"
              @click="confirmSet(2)"
          >确定</el-button
          >
        </div>
      </el-card>
      <el-card class="box-card">
        <div slot="header">
          <span>截止修改报名时间</span>
        </div>
        <el-form :model="xiuGaiBaoMingJieZhiTimeForm" label-width="100px">
          <el-form-item prop="endTime" label="结束时间">
            <el-date-picker
                size="small"
                v-model="xiuGaiBaoMingJieZhiTimeForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"

                placeholder="请选择结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="flex-end">
          <el-button
              type="success"
              size="small"
              :disabled="!xiuGaiBaoMingJieZhiTimeForm.endTime"
              @click="confirmSet(3)"
          >确定</el-button
          >
        </div>
      </el-card>
      <el-card class="box-card">
        <div slot="header">
          <span>截止学校添加报名时间</span>
        </div>
        <el-form :model="schoolForm" label-width="100px">
          <el-form-item prop="endTime" label="结束时间">
            <el-date-picker
                size="small"
                v-model="schoolForm.endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"

                placeholder="请选择结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="flex-end">
          <el-button
              type="success"
              size="small"
              :disabled="!schoolForm.endTime"
              @click="confirmSet(5)"
          >确定</el-button
          >
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import {getBaoMingShiJianDetailUpdate,getBaoMingShiJianDetail} from "@/api/setting";
export default {
  name: "index",
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      yiXueXiaoBaoMingForm:{
        type:'1',
        beginTime:'',
        endTime:''
      },
      yiZhengJianBaoMingForm:{
        type:'2',
        beginTime:'',
        endTime:''
      },
      xiuGaiBaoMingJieZhiTimeForm:{
        type:'3',
        beginTime:'',
        endTime:''
      },
      // 学校新增报名截止时间
      schoolForm: {
        type:'4',
        beginTime:'',
        endTime:''
      },
      // 报名年龄
      ageForm: {
        type:'5',
        beginTime:'',
        endTime:''
      },
      beginTime:'',
      endTime:''
    }
  },
  created() {
    this.getdeTail()
  },
  methods:{
    getdeTail(){
      getBaoMingShiJianDetail({"key": 111},this.prefixDeptCode).then(res=>{
        this.ageForm=res
      })
      getBaoMingShiJianDetail({"key": 666},this.prefixDeptCode).then(res=>{
        this.yiXueXiaoBaoMingForm=res
      })
      getBaoMingShiJianDetail({"key": 777},this.prefixDeptCode).then(res=>{
       this.yiZhengJianBaoMingForm=res
      })
      getBaoMingShiJianDetail({"key": 888},this.prefixDeptCode).then(res=>{
        this.xiuGaiBaoMingJieZhiTimeForm=res
      })
      getBaoMingShiJianDetail({"key": 999},this.prefixDeptCode).then(res=>{
        this.schoolForm=res
      })
    },
    confirmStart(item,index){
     if(item.beginTime&&item.endTime){
       let startTime = new Date(item.beginTime).getTime()
       let endTime = new Date(item.endTime).getTime()
       if(startTime>endTime){
         this.$message.error('开始时间不能大于结束时间')
         if(index==1){
            this.yiXueXiaoBaoMingForm.beginTime=''
         }else if(index==4) {
            this.ageForm.beginTime=''
         } else {
           this.yiZhengJianBaoMingForm.beginTime=''
         }

       }
     }
    },
    confirmEnd(item,index){
      if(item.beginTime&&item.endTime){
        let startTime = new Date(item.beginTime).getTime()
        let endTime = new Date(item.endTime).getTime()
        if(startTime>endTime){
          this.$message.error('开始时间不能大于结束时间')
          if(index==1){
            this.yiXueXiaoBaoMingForm.endTime=''
          }else if(index==4) {
            this.ageForm.endTime=''
          } else {
            this.yiZhengJianBaoMingForm.endTime=''
          }
        }
     }
    },
    confirmSet(type){
      if(type==1){
        getBaoMingShiJianDetailUpdate(this.yiXueXiaoBaoMingForm,this.prefixDeptCode).then(res=>{
         if(res){
           this.$message.success('设置选择拟学校报名时间成功')
         }
        })
      }else if(type==2){
        getBaoMingShiJianDetailUpdate(this.yiZhengJianBaoMingForm,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message.success('设置不确定拟报名学校报名时间成功')
          }
        })
      }else if(type==3){
        getBaoMingShiJianDetailUpdate(this.xiuGaiBaoMingJieZhiTimeForm,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message.success('设置修改报名截止时间成功')
          }
        })
      }else if(type==4){
        getBaoMingShiJianDetailUpdate(this.ageForm,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message.success('设置报名年龄成功')
          }
        })
      }else if(type==5){
        getBaoMingShiJianDetailUpdate(this.schoolForm,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message.success('设置截止学校添加报名时间成功')
          }
        })
      }
    },
  }
}
</script>

<style scoped>
.time-set {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.box-card {
  width: calc(25% - 20px);
  margin-bottom: 20px;
}
</style>
