<template>
  <div>
    <el-table :data="tableData" border stripe v-loading="tableLoading">
      <el-table-column
        align="center"
        type="index"
        label="序号"
        width="60"
        fixed="left"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="deptName"
        label="区县报名入口"
      ></el-table-column>
      <el-table-column align="center" prop="openStatus" label="状态">
        <template slot-scope="{ row }">
          <span
            :class="{
              enable: row.openStatus == 1,
              disable: row.openStatus == 0,
            }"
            >{{ switchStatus(row.openStatus) }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="tips"
        label="提示"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="pcUrl"
        label="pc报名端地址"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="wxUrl"
        label="微信报名端地址"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="{ row }">
          <el-link
            @click="edit(row)"
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            style="margin-right: 10px"
            >编辑
          </el-link>
          <el-link
            v-if="row.openStatus == 0"
            type="success"
            icon="el-icon-check"
            @click="changeStatus(row)"
            :underline="false"
            style="margin-right: 10px"
            >启用
          </el-link>
          <el-link
            v-if="row.openStatus == 1"
            type="info"
            icon="el-icon-close"
            @click="changeStatus(row)"
            :underline="false"
            >禁用
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑 -->
    <el-dialog
      title="编辑"
      :visible.sync="modal.addOrEdit"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        label-position="right"
        label-width="135px"
      >
        <el-form-item prop="title" label="报名类别：">
          <span>{{ form.deptName }}</span>
        </el-form-item>
        <el-form-item prop="pcUrl" label="pc报名端地址：">
          <el-input
            v-model.trim="form.pcUrl"
            placeholder="请输入地址"
          ></el-input>
        </el-form-item>
        <el-form-item prop="wxUrl" label="微信报名端地址：">
          <el-input
            v-model.trim="form.wxUrl"
            placeholder="请输入地址"
          ></el-input>
        </el-form-item>
        <el-form-item prop="tips" label="提示：">
          <el-input
            type="textarea"
            :rows="2"
            v-model.trim="form.tips"
            placeholder="请输入提示"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ModalMixin from "@/mixins/ModalMixin";
import { getDepts } from "@/api/common";
import { changeOpenStatus, setOpenTips } from "@/api/signConfig";

export default {
  name: "signEntranceManage",
  mixins: [ModalMixin],
  data() {
    return {
      tableData: [],
      modal: {
        addOrEdit: false,
      },
      tableLoading: false,
      form: {
        id: "",
        deptName: "",
        tips: "",
        pcUrl: "",
        wxUrl: ""
      },
      rules: {
        tips: [
          {
            required: true,
            message: "请输入提示",
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    switchStatus() {
      return (val) => (val == "1" ? "启用" : "禁用");
    },
  },
  created() {
    this.getTableData();
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      getDepts({ level: 2 })
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 编辑
    edit(row) {
      console.log(row);
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.id = row.id;
        this.form.deptName = row.deptName;
        this.form.tips = row.tips;
        this.form.pcUrl = row.pcUrl;
        this.form.wxUrl = row.wxUrl;
      });
    },
    // 启用、禁用
    changeStatus(row) {
      this.$confirm(
        `确定${row.openStatus == 1 ? "禁用" : "启用"}【 ${row.deptName} 】吗？`,
        "提示",
        { type: "warning" }
      ).then(() => {
        this.$message.success({ message: "操作成功" });
        changeOpenStatus({ key: row.id }).then(() => this.getTableData());
      });
    },
    // 更新
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          setOpenTips(this.form).then(() => {
            this.switchModal("addOrEdit", false);
            this.$message.success("操作成功");
            this.getTableData();
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.enable {
  color: #67c23a;
}
.disable {
  color: #909399;
}
</style>
