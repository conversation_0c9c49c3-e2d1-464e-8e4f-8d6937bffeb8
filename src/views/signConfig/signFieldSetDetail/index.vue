<!-- 字段配置 - 基础信息 -->
<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button
          type="primary"
          plain
          icon="el-icon-back"
          size="small"
          @click="back"
          >返回</el-button
        >
        <span class="field-set-title">{{ nodePath }}</span>
      </div>
    </div>
    <div class="field-set-detail" v-loading="tableLoading">
      <el-collapse>
        <el-collapse-item
          v-for="(item, index) in tableData"
          :key="item.typeConfigId"
        >
          <template slot="title">
            <el-button
              type="primary"
              @click.stop="matchingPic(item.infoName, index)"
              size="small"
              >匹配照片</el-button
            >&emsp; {{ swithInfoName(item.infoName) }}&emsp;
            <el-radio-group v-model="item.isShow">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">不显示</el-radio>
            </el-radio-group>
          </template>
          <div class="table-box">
            <div class="selectAll-box">
              <el-button
                type="text"
                size="mini"
                @click="handleSelectAll(index, 1)"
                >全选</el-button
              >
              /
              <el-button
                type="text"
                size="mini"
                @click="handleSelectAll(index, 0)"
                style="margin-left: 0px"
                >取消全选</el-button
              >
            </div>
            <el-table
              :data="item.fieldLeafConfigInfoVos | filterData"
              border
              stripe
            >
              <el-table-column width="100" prop="isShow" align="center">
                <template slot-scope="{ row }">
                  <el-checkbox
                    v-model="row.isShow"
                    :true-label="1"
                    :false-label="0"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="fieldName" label="字段名称" align="center">
                <template slot-scope="scope">
                  <el-popover
                    placement="bottom"
                    width="240"
                    height="400"
                    trigger="click"
                  >
                    <div>
                      <el-input
                        v-model="fieldNameTemp.newFieldName"
                        placeholde="请输入名称"
                      ></el-input>
                      <div class="flex-center">
                        <div class="flex-center sd-m-t-15">
                          <el-button
                            type="plain"
                            @click="closeAllPopovers"
                            size="small"
                            >取 消</el-button
                          >
                          <el-button
                            type="primary"
                            @click="fieldNameSubmit(index, scope.$index)"
                            size="small"
                            >保 存</el-button
                          >
                        </div>
                      </div>
                    </div>
                    <span
                      @click="editFieldName(scope.row)"
                      style="cursor: pointer"
                      slot="reference"
                    >
                      {{ scope.row.fieldName }}
                      <el-link
                        type="info"
                        :underline="false"
                        icon="el-icon-edit"
                      >
                      </el-link>
                    </span>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column
                prop="fieldEnglish"
                label="数据库字段"
                align="center"
              ></el-table-column>
              <el-table-column prop="isNecessary" label="设置" align="center">
                <template slot-scope="{ row }">
                  <el-radio-group v-model="row.isNecessary">
                    <el-radio :label="1">必填</el-radio>
                    <el-radio :label="0">非必填</el-radio>
                  </el-radio-group>
                </template>
              </el-table-column>
              <el-table-column
                prop="inputItemString"
                label="输入方式"
                align="center"
              ></el-table-column>
              <el-table-column prop="sort" label="排序" align="center">
                <template slot-scope="scope">
                  <el-popover
                    placement="bottom"
                    width="140"
                    height="400"
                    trigger="click"
                  >
                    <div>
                      <el-input
                        v-model="fieldSortTemp.newSort"
                        placeholde="请输入序号"
                      ></el-input>
                      <div class="flex-center">
                        <div class="flex-center sd-m-t-15">
                          <el-button
                            type="plain"
                            @click="closeAllPopovers"
                            size="small"
                            >取 消</el-button
                          >
                          <el-button
                            type="primary"
                            @click="fieldSortTempSave(index, scope.$index)"
                            size="small"
                            >保 存</el-button
                          >
                        </div>
                      </div>
                    </div>
                    <span
                      @click="editFieldSort(scope.row)"
                      style="cursor: pointer"
                      slot="reference"
                    >
                      {{ scope.row.sort }}
                      <el-link
                        type="info"
                        :underline="false"
                        icon="el-icon-edit"
                      >
                      </el-link>
                    </span>
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
      <div class="flex-center sd-m-t-15">
        <el-button type="default" @click="back" size="small">取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          size="small"
          :loading="submitLoading"
          >保 存</el-button
        >
      </div>
    </div>
    <el-dialog
      :title="infoName + ' - 配置照片'"
      :visible.sync="modal.detail"
      center
      :close-on-click-modal="false"
      class="field-pic-set"
      width="1200px"
    >
      <el-table :data="picTableDataTemp" border stripe>
        <el-table-column prop="fieldName" label="照片名称" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="bottom"
              width="240"
              height="400"
              trigger="click"
              :ref="scope.$index"
            >
              <div>
                <el-input
                  v-model="fieldNameTemp.newFieldName"
                  placeholde="请输入名称"
                ></el-input>
                <div class="flex-center">
                  <div class="flex-center sd-m-t-15">
                    <el-button
                      type="plain"
                      @click="scope._self.$refs[scope.$index].doClose()"
                      size="small"
                      >取 消</el-button
                    >
                    <el-button
                      type="primary"
                      @click="
                        picFieldNameSubmit(scope.$index);
                        scope._self.$refs[scope.$index].doClose();
                      "
                      size="small"
                      >保 存</el-button
                    >
                  </div>
                </div>
              </div>
              <span
                @click="editPicFieldName(scope.row)"
                style="cursor: pointer"
                slot="reference"
              >
                {{ scope.row.fieldName }}
                <el-link type="info" :underline="false" icon="el-icon-edit">
                </el-link>
              </span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          prop="fieldEnglish"
          label="数据库字段"
          align="center"
        ></el-table-column>
        <el-table-column label="是否显示" align="center">
          <template slot-scope="{ row }">
            <el-radio-group v-model="row.isShow">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">不显示</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="设置" align="center">
          <template slot-scope="{ row }">
            <el-radio-group v-model="row.isNecessary">
              <el-radio :label="1">必填</el-radio>
              <el-radio :label="0">非必填</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="bottom"
              width="140"
              height="400"
              trigger="click"
              :ref="'sort' + scope.$index"
            >
              <div>
                <el-input
                  v-model="fieldSortTemp.newSort"
                  placeholde="请输入序号"
                ></el-input>
                <div class="flex-center">
                  <div class="flex-center sd-m-t-15">
                    <el-button
                      type="plain"
                      @click="
                        scope._self.$refs['sort' + scope.$index].doClose()
                      "
                      size="small"
                      >取 消</el-button
                    >
                    <el-button
                      type="primary"
                      @click="
                        picFieldSortTempSave(scope.$index);
                        scope._self.$refs['sort' + scope.$index].doClose();
                      "
                      size="small"
                      >保 存</el-button
                    >
                  </div>
                </div>
              </div>
              <span
                @click="editPicFieldSort(scope.row)"
                style="cursor: pointer"
                slot="reference"
              >
                {{ scope.row.sort }}
                <el-link type="info" :underline="false" icon="el-icon-edit">
                </el-link>
              </span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="上传示例图" align="center">
          <template slot-scope="scope">
            <div class="flex-center">
              <el-upload
                action
                :http-request="
                  (file) => {
                    return uploadRequest(file, scope.$index);
                  }
                "
                list-type="text"
                :before-upload="uploadBefore"
                :show-file-list="false"
              >
                <el-button size="mini" type="primary">上传</el-button>
              </el-upload>
              <el-button
                size="mini"
                type="primary"
                class="sd-m-l-10"
                :disabled="!scope.row.sampleImgUrl"
                @click="preview(scope.row.sampleImgUrl)"
                >预览</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-center sd-m-t-15">
        <el-button type="default" @click="matchingPicCancel()" size="small"
          >取 消</el-button
        >
        <el-button type="primary" @click="matchingPicSave" size="small"
          >保 存</el-button
        >
      </div>
    </el-dialog>
    <el-dialog :visible.sync="uploadVisible">
      <img width="100%" :src="switchPicUrl(picUrl)" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import ModalMixin from "@/mixins/ModalMixin";
import {
  getFieldConfigInfo,
  setFieldName,
  setFieldConfigInfo,
} from "@/api/signConfig";
import { uploaDing, uploadPicture } from "@/utils/upload.js";
import { pref } from "@/utils/common";

export default {
  name: "signFieldSetDetail",
  mixins: [ModalMixin],
  components: {},
  data() {
    return {
      baseApi: process.env.VUE_APP_BASE_API,
      queryParams: {
        setUpSaveIds: "",
        configId: "",
      },
      nodePath: "",
      tableData: [],
      tableLoading: false,
      submitLoading: false,

      modal: {
        detail: false,
      },
      infoIndex: null,
      infoName: "",
      picTableDataTemp: [],
      fieldNameTemp: {
        fieldId: "",
        newFieldName: "",
      },
      fieldSortTemp: {
        newSort: "",
      },
      picFieldNameIsEdit: false,
      uploadVisible: false,
      picUrl: "",
      prefixDeptCode: "",
    };
  },
  created() {
    this.queryParams.setUpSaveIds = this.$route.query.setUpSaveIds;
    this.queryParams.configId = this.$route.query.configId;
    this.nodePath = this.$route.query.nodePath;
    this.prefixDeptCode = this.$route.query.code;
    this.getFieldConfigInfo();
    console.log("this.prefixDeptCode",this.prefixDeptCode)
    console.log("this.queryParams.setUpSaveIds",this.queryParams.setUpSaveIds)
  },
  computed: {
    swithInfoName() {
      if(this.prefixDeptCode==='130582'){
        return (name) => {
          // 先处理 name
          const processedName1 = this.processFieldName1(name);
          const processedName2 = this.processFieldName2(name);

          if (this.queryParams.configId == 1) {
            return `基础信息 - ${name}`;
          } else if (this.queryParams.configId == 2) {
            if ([17, 25, 73, 81].includes(Number(this.queryParams.setUpSaveIds))) {
              return `房产信息 - ${processedName1}`;
            } else if([20, 28, 76, 84].includes(Number(this.queryParams.setUpSaveIds))){
              return `房产信息 - ${processedName2}`;
            }
            else {
              return `房产信息 - ${name}`;
            }
          } else if (this.queryParams.configId == 3) {
            return `图片管理 - ${name}`;
          }
          return "";
        };
      }else {
        return (name) => {
          return this.queryParams.configId == 1
              ? `基础信息 - ${name}`
              : this.queryParams.configId == 2
                  ? `房产信息 - ${name}`
                  : this.queryParams.configId == 3
                      ? `图片管理 - ${name}`
                      : "";
        };
      }
    },
    switchPicUrl() {
      return (url) => this.baseApi + pref + this.prefixDeptCode + url;
    },
  },
  filters: {
    filterData: (data) => {
      let filteredData = [...data];
      filteredData = filteredData.filter((item) => item.type == 1);
      return filteredData;
    },
  },
  methods: {
    processFieldName1(name) {
      // 在这里添加你想要替换的字段名称规则
      const fieldNameMap = {
        "房屋所有权证": "父母房屋所有权证",
        "不动产权证书": "父母不动产权证",
        "土地使用证": "祖(外祖)父母房屋购买手续",
        "购房合同": "父母购房合同",
        "抵押合同": "祖(外祖)父母房屋所有权证",
        "集资房": "祖(外祖)父母不动产权证",
        "小产权房": "祖(外祖)父母购房合同",
        "廉租房/经济保障房": "公租房/经济适用房",
        "宅基证": "宅基证",
        "自然村": "小产权房"
      };
      return fieldNameMap[name] || name;
    },
    processFieldName2(name) {
      // 在这里添加你想要替换的字段名称规则
      const fieldNameMap = {
        "房屋所有权证": "所租房房主房屋所有权证",
        "不动产权证书": "所租房房主不动产权证",
        "购房合同": "所租房房主购房合同",
        "自然村": "房屋购买手续"
      };
      return fieldNameMap[name] || name;
    },
    // 获取列表
    getFieldConfigInfo() {
      this.tableLoading = true;
      getFieldConfigInfo(this.queryParams, this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 编辑字段名称气泡
    editFieldName(row) {
      this.fieldNameTemp.fieldId = row.fieldId;
      this.fieldNameTemp.newFieldName = row.fieldName;
    },
    // 编辑字段名称提交
    fieldNameSubmit(index, idx) {
      setFieldName(this.fieldNameTemp, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.tableData[index].fieldLeafConfigInfoVos[idx].fieldName =
          this.fieldNameTemp.newFieldName;
        this.closeAllPopovers();
      });
    },
    // 编辑排序气泡
    editFieldSort(row) {
      this.fieldSortTemp.newSort = row.sort;
    },
    // 编辑排序保存
    fieldSortTempSave(index, idx) {
      this.tableData[index].fieldLeafConfigInfoVos[idx].sort =
        this.fieldSortTemp.newSort;
      this.closeAllPopovers();
    },
    // 关闭所有气泡
    closeAllPopovers() {
      // 遍历所有的Popover实例
      const popovers = document.querySelectorAll(".el-popover");
      popovers.forEach((popover) => {
        popover.style.display = "none";
      });
    },
    // 全选，取消全选
    handleSelectAll(index, flag) {
      this.tableData[index].fieldLeafConfigInfoVos.forEach((item) => {
        item.isShow = flag;
      });
    },
    // 匹配照片 - 弹框
    matchingPic(infoName, index) {
      this.switchModal("detail", true);
      this.picFieldNameIsEdit = false;
      this.infoName = infoName;
      this.infoIndex = index;
      this.picTableDataTemp = [];
      // console.log(this.tableData[index].fieldLeafConfigInfoVos);
      let filterData = this.tableData[index].fieldLeafConfigInfoVos.filter(
        (item) => item.type == 2
      );
      this.picTableDataTemp = JSON.parse(JSON.stringify(filterData));
      // console.log(this.picTableDataTemp);
    },
    // 匹配照片 - 编辑名称气泡
    editPicFieldName(row) {
      this.fieldNameTemp.fieldId = row.fieldId;
      this.fieldNameTemp.newFieldName = row.fieldName;
    },
    // 匹配照片 - 编辑名称提交
    picFieldNameSubmit(index) {
      setFieldName(this.fieldNameTemp, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.picTableDataTemp[index].fieldName =
          this.fieldNameTemp.newFieldName;
        this.picFieldNameIsEdit = true;
      });
    },
    // 匹配照片 - 编辑排序气泡
    editPicFieldSort(row) {
      this.fieldSortTemp.newSort = row.sort;
    },
    // 匹配照片 - 编辑排序保存
    picFieldSortTempSave(index) {
      this.picTableDataTemp[index].sort = this.fieldSortTemp.newSort;
    },
    // 匹配照片 - 上传图片
    uploadBefore(file) {
      return uploaDing(file);
    },
    // 上传照片
    uploadRequest(file, index) {
      uploadPicture(file, this.prefixDeptCode).then((res) => {
        this.picTableDataTemp[index].sampleImgUrl = res;
      });
    },
    // 预览照片
    preview(url) {
      this.uploadVisible = true;
      this.picUrl = url;
    },
    // uploadPreview(file) {
    //   this.uploadVisible = true;
    //   this.uploadUrl = file.url;
    // },
    // uploadRemove() {
    //   this.uploadUrl = "";
    //   setTimeout(() => {
    //     this.isUpload = false;
    //   }, 700);
    // },
    // 匹配照片- 取消
    matchingPicCancel() {
      // 改过字段名称
      if (this.picFieldNameIsEdit) {
        // console.log(this.picTableDataTemp);
        for (
          let i = 0;
          i < this.tableData[this.infoIndex].fieldLeafConfigInfoVos.length;
          i++
        ) {
          for (let j = 0; j < this.picTableDataTemp.length; j++) {
            if (
              this.tableData[this.infoIndex].fieldLeafConfigInfoVos[i]
                .fieldId == this.picTableDataTemp[j].fieldId
            ) {
              this.tableData[this.infoIndex].fieldLeafConfigInfoVos[
                i
              ].fieldName = this.picTableDataTemp[j].fieldName;
            }
          }
        }
      }
      this.switchModal("detail", false);
    },
    // 匹配照片 - 保存
    matchingPicSave() {
      // console.log(this.picTableDataTemp);
      for (
        let i = 0;
        i < this.tableData[this.infoIndex].fieldLeafConfigInfoVos.length;
        i++
      ) {
        for (let j = 0; j < this.picTableDataTemp.length; j++) {
          if (
            this.tableData[this.infoIndex].fieldLeafConfigInfoVos[i].fieldId ==
            this.picTableDataTemp[j].fieldId
          ) {
            this.tableData[this.infoIndex].fieldLeafConfigInfoVos[i] =
              this.picTableDataTemp[j];
          }
        }
      }
      // console.log(this.tableData[this.infoIndex]);
      this.switchModal("detail", false);
    },
    // 全部提交
    handleSubmit() {
      let params = {
        configId: this.queryParams.configId,
        setUpSaveIds: this.queryParams.setUpSaveIds,
        fieldMiddleConfigInfoVos: [],
      };
      // 只解析出标记显示的字段
      let fieldMiddleConfigInfoVos = JSON.parse(
        JSON.stringify(this.tableData.filter((item) => item.isShow == 1))
      );
      fieldMiddleConfigInfoVos.forEach((item) => {
        item.fieldLeafConfigInfoVos = item.fieldLeafConfigInfoVos.filter(
          (i) => i.isShow == 1
        );
      });
      if (fieldMiddleConfigInfoVos.length > 0) {
        this.submitLoading = true;
        params.fieldMiddleConfigInfoVos = fieldMiddleConfigInfoVos;
        setFieldConfigInfo(params, this.prefixDeptCode)
          .then((res) => {
            this.$message.success("操作成功");
            this.getFieldConfigInfo();
          })
          .finally(() => {
            this.submitLoading = false;
          });
      } else {
        this.$message.warning("请配置显示项");
      }
    },
    // 返回
    back() {
      this.$router.back();
    },
  },
};
</script>

<style lang="scss" scoped>
.field-set-title {
  line-height: 40px;
  margin-left: 15px;
}
.field-set-detail {
  .table-box {
    position: relative;
    .selectAll-box {
      position: absolute;
      top: 12px;
      left: 5px;
      z-index: 10;
    }
  }
}
</style>