<!-- 报名填写字段 - 房产信息 -->
<template>
  <div>
    <el-tabs tab-position="left" :stretch="true">
      <el-tab-pane label="房屋所有权证">
        <el-table :data="tableDataFwsyqz" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="不动产权证书">
        <el-table :data="tableDataBdcqzs" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="土地使用证">
        <el-table :data="tableDataTdsyz" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="购房合同">
        <el-table :data="tableDataGfht" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="抵押合同">
        <el-table :data="tableDataDyht" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="集资房">
        <el-table :data="tableDataJzf" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="小产权房">
        <el-table :data="tableDataXcqf" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="廉租房/经济保障房">
        <el-table :data="tableDataLzf" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="宅基证">
        <el-table :data="tableDataZjz" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="自然村">
        <el-table :data="tableDataZrc" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableDataFwsyqz: [
        {
          field: "房屋所有权人姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "所有权发证时间",
          inputItem: "下拉选择",
          limit: "时间选择器",
        },
        {
          field: "房产证号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "房屋坐落地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
      ],
      tableDataBdcqzs: [
        {
          field: "权利人姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "所有权发证时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
        {
          field: "不动产单元号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "房屋坐落地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
      ],
      tableDataTdsyz: [
        {
          field: "土地使用者",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "所有权发证时间",
          inputItem: "下拉选择",
          limit: "时间选择器年月日",
        },
        {
          field: "地号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "土地坐落地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "终止日期",
          inputItem: "下拉选择",
          limit: "时间选择器年月日",
        },
      ],
      tableDataGfht: [
        {
          field: "出卖人姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "买受人姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "买受人身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "买受人联系电话",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "买受人与该生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "合同编号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "房屋详细地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "房屋交付时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
      ],
      tableDataDyht: [
        {
          field: "抵押权人姓名/单位",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "抵押房主姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "抵押房主身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "抵押房主联系电话",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "抵押人与该生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "抵押房屋详细地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "抵押时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
      ],
      tableDataJzf: [
        {
          field: "集资房编号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "建设单位",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "房主姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "房主身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "房主与该生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "房屋详细地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "房屋签约时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
      ],
      tableDataXcqf: [
        {
          field: "建设单位",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "房主姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "房主身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "房主与该生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "房屋详细地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "房屋建设时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
      ],
      tableDataLzf: [
        {
          field: "承租人姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "承租人身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "承租人与该生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "房屋详细地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "承租时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
      ],
      tableDataZjz: [
        {
          field: "宅基证户主姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "承租人身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "承租人与该生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "房屋详细地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "使用时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
      ],
      tableDataZrc: [
        {
          field: "姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "身份证号",
          inputItem: "手动输入",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "与该生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
        {
          field: "房屋详细地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值学校招生范围",
        },
        {
          field: "入住时间",
          inputItem: "下拉选择",
          limit: "时间选择器 年月日",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style>
</style>