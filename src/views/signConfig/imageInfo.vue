<!-- 报名填写字段 - 图片信息 -->
<template>
  <div>
    <el-tabs tab-position="left" :stretch="true">
      <el-tab-pane label="基础信息照片">
        <el-table :data="tableDataJc" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="监护人照片">
        <el-table :data="tableDataJhr" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="双胞胎照片">
        <el-table :data="tableDataSbt" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="户口照片">
        <el-table :data="tableDataHk" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="房产照片">
        <el-table
          :data="tableDataFc"
          border
          stripe
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column label="照片名称" prop="field" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="经商信息">
        <el-table :data="tableDataJs" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="务工照片">
        <el-table :data="tableDataWg" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="居住证信息">
        <el-table :data="tableDataJzz" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="其他材料照片">
        <el-table :data="tableDataQtcl" border stripe>
          <el-table-column label="照片名称" prop="field" align="center" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableDataJc: [
        {
          field: "学籍表",
        },
        {
          field: "幼小衔接卡",
        },
        {
          field: "儿童预防接种证",
        },
        {
          field: "医学出生证明",
        },
      ],
      tableDataJhr: [
        {
          field: "父母结婚证",
        },
        {
          field: "监护人1身份证",
        },
        {
          field: "监护人2身份证",
        },
      ],
      tableDataSbt: [
        {
          field: "医学出生证明",
        },
        {
          field: "学生本人户口页",
        },
        {
          field: "学生户主页",
        },
        {
          field: "儿童接种证明",
        },
      ],
      tableDataHk: [
        {
          field: "学生所属户口首页",
        },
        {
          field: "学生户主页",
        },
        {
          field: "监护人1户口本首页",
        },
        {
          field: "监护人1户口页",
        },
        {
          field: "监护人2户口本首页",
        },
        {
          field: "监护人2户口页",
        },
      ],
      tableDataJs: [
        {
          field: "营业执照",
        },
        {
          field: "经营门市照片",
        },
        {
          field: "租房合同",
        },
        {
          field: "上一年度营业证年检年度报告",
        },
        {
          field: "经营场所室内照片",
        },
        {
          field: "经营场所室外沿街门面照片",
        },
        {
          field: "工商户或日常进出货相关票据",
        },
        {
          field: "经营场所水电费票据",
        },
        {
          field: "其他辅助证明",
        },
      ],
      tableDataWg: [
        {
          field: "劳动合同",
        },
        {
          field: "用工单位大门照片",
        },
        {
          field: "社会养老保险证明",
        },
        {
          field: "租房合同",
        },
        {
          field: "营业证原件复印件",
        },
        {
          field: "工资表",
        },
        {
          field: "单位证明",
        },
        {
          field: "社保证明",
        },
        {
          field: "其他辅助证明",
        },
      ],
      tableDataJzz: [
        {
          field: "居住证",
        },
        {
          field: "外出就读证明",
        },
        {
          field: "其他辅助证明",
        },
      ],
      tableDataQtcl: [
        {
          field: "其他辅助证明",
        },
        {
          field: "其他辅助证明",
        },
        {
          field: "其他辅助证明",
        },
      ],
      tableDataFc: [
        {
          id: 1,
          field: "房屋所有权证",
          children: [
            { id: 101, field: "房屋所有权证封面" },
            { id: 102, field: "房屋所有权证信息页" },
            { id: 103, field: "物业费" },
            { id: 104, field: "水费" },
            { id: 105, field: "电费" },
            { id: 106, field: "燃气费" },
            { id: 107, field: "取暖费" },
            { id: 108, field: "购房发票" },
            { id: 109, field: "补充材料1" },
          ],
        },
        {
          id: 2,
          field: "不动产权证",
          children: [
            { id: 201, field: "不动产权证封面" },
            { id: 202, field: "不动产权证信息页" },
            { id: 203, field: "物业费" },
            { id: 204, field: "水费" },
            { id: 205, field: "电费" },
            { id: 206, field: "燃气费" },
            { id: 207, field: "取暖费" },
            { id: 208, field: "购房发票" },
            { id: 209, field: "补充材料1" },
          ],
        },
        {
          id: 3,
          field: "土地使用证",
          children: [
            { id: 301, field: "土地使用证信息页" },
            { id: 302, field: "物业费" },
            { id: 303, field: "水费" },
            { id: 304, field: "电费" },
            { id: 305, field: "燃气费" },
            { id: 306, field: "取暖费" },
            { id: 307, field: "购房发票" },
            { id: 308, field: "补充材料1" },
            { id: 309, field: "补充材料2" },
          ],
        },
        {
          id: 4,
          field: "购房合同",
          children: [
            { id: 401, field: "购房合同封面" },
            { id: 402, field: "买受人信息所在页" },
            { id: 403, field: "房屋门牌号所在页" },
            { id: 404, field: "买受人签字盖章页" },
            { id: 405, field: "电费" },
            { id: 406, field: "水费" },
            { id: 407, field: "燃气费" },
            { id: 408, field: "取暖费" },
            { id: 409, field: "购房发票" },
            { id: 410, field: "补充材料1" },
          ],
        },
        {
          id: 5,
          field: "抵押合同",
          children: [
            { id: 501, field: "抵押合同封面" },
            { id: 502, field: "抵押合同信息所在页" },
            { id: 503, field: "抵押合同签字盖章页" },
            { id: 504, field: "电费" },
            { id: 505, field: "水费" },
            { id: 506, field: "燃气费" },
            { id: 507, field: "取暖费" },
            { id: 508, field: "购房发票" },
            { id: 509, field: "补充材料1" },
            { id: 510, field: "补充材料2" },
          ],
        },
        {
          id: 6,
          field: "集资房",
          children: [
            { id: 601, field: "集资房信息所在页" },
            { id: 602, field: "集资房签字盖章页" },
            { id: 603, field: "电费" },
            { id: 604, field: "水费" },
            { id: 605, field: "燃气费" },
            { id: 606, field: "取暖费" },
            { id: 607, field: "购房发票" },
            { id: 608, field: "补充材料1" },
            { id: 609, field: "补充材料2" },
          ],
        },
        {
          id: 7,
          field: "小产权房",
          children: [
            { id: 701, field: "小产权房信息所在页" },
            { id: 702, field: "电费" },
            { id: 703, field: "水费" },
            { id: 704, field: "燃气费" },
            { id: 705, field: "取暖费" },
            { id: 706, field: "购房发票" },
            { id: 707, field: "补充材料1" },
            { id: 708, field: "补充材料2" },
            { id: 709, field: "补充材料3" },
          ],
        },
        {
          id: 8,
          field: "廉租房/经济保障房",
          children: [
            { id: 801, field: "房屋租赁凭证" },
            { id: 802, field: "电费" },
            { id: 803, field: "水费" },
            { id: 804, field: "燃气费" },
            { id: 805, field: "取暖费" },
            { id: 806, field: "补充材料1" },
            { id: 807, field: "补充材料2" },
            { id: 808, field: "补充材料3" },
          ],
        },
        {
          id: 9,
          field: "宅基证",
          children: [
            { id: 901, field: "宅基证" },
            { id: 902, field: "电费" },
            { id: 903, field: "水费" },
            { id: 904, field: "燃气费" },
            { id: 905, field: "取暖费" },
            { id: 906, field: "补充材料1" },
            { id: 907, field: "补充材料2" },
            { id: 908, field: "补充材料3" },
          ],
        },
        {
          id: 10,
          field: "自然村",
          children: [
            { id: 1001, field: "自然村证明" },
            { id: 1002, field: "电费" },
            { id: 1003, field: "水费" },
            { id: 1004, field: "燃气费" },
            { id: 1005, field: "取暖费" },
            { id: 1006, field: "补充材料1" },
            { id: 1007, field: "补充材料2" },
            { id: 1008, field: "补充材料3" },
          ],
        },
      ],
    };
  },
  methods: {},
};
</script>

<style>
</style>