<template>
  <div>
    <div>
      <el-input style="width: 200px;margin:0 20px  20px 0" v-model="search.keywords" clearable placeholder="姓名/身份证号"></el-input>
      <el-button type="primary" @click="getTable">搜索</el-button>
    </div>
    <el-table
        :data="tableData.records"
        border
        stripe
        v-loading="tableLoading"
    >
      <el-table-column
          align="center"
          label="序号"
          prop="index"
          width="80"
      >
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
          v-if="role=='CITY_ADMIN'"
          align="center"
          label="操作账号"
          prop="creatorName"
          width="140"
      ></el-table-column>
      <el-table-column
          v-if="role=='CITY_ADMIN'"
          align="center"
          label="操作人绑定微信昵称"
          prop="nickName"
          width="160"
      ></el-table-column>
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="绑定微信号"-->
<!--          prop=""-->
<!--          width="140"-->
<!--      ></el-table-column>-->
      <el-table-column
          v-if="role=='CITY_ADMIN'"
          align="center"
          label="操作时间"
          prop="createTime"
          width="140"
      ></el-table-column>
      <el-table-column
          align="center"
          label="调剂学生姓名"
          prop="studentName"
          width="120"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="调剂学生身份证号"
          prop="idCard"
          width="120"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="调剂原区县"
          prop="fromDeptName"

      ></el-table-column>
      <el-table-column
          align="center"
          label="调剂原学校"
          prop="fromSchoolName"

      >
      </el-table-column>
      <el-table-column
          align="center"
          label="调剂区县"
          prop="toDeptName"

      ></el-table-column>
      <el-table-column
          align="center"
          label="调剂学校"
          prop="toSchoolName"

      ></el-table-column>
      <el-table-column
          align="center"
          label="调剂原因"
          prop="content"

      ></el-table-column>
      <el-table-column
          align="center"
          label="操作"
          prop=""
          width="180"
      >
        <template slot-scope="scope">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="liuZhuan(scope.row)"
          >流转记录</el-link
          >
        </template>
      </el-table-column>

    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="total"
      >
      </el-pagination>
    </div>
<!--    流转记录-->
    <el-dialog
        title="流转记录"
        :visible.sync="liuZhuanShow"
        width="60%"
    >
      <el-table
          :data="liuZhuanData"
          style="width: 100%"
      >
        <el-table-column
            v-if="role=='CITY_ADMIN'"
            align="center"
            label="操作账号"
            prop="creatorName"
            width="140"
        ></el-table-column>
        <el-table-column
            v-if="role=='CITY_ADMIN'"
            align="center"
            label="操作人绑定微信昵称"
            prop="nickName"
            width="160"
        ></el-table-column>
        <!--      <el-table-column-->
        <!--          align="center"-->
        <!--          label="绑定微信号"-->
        <!--          prop=""-->
        <!--          width="140"-->
        <!--      ></el-table-column>-->
        <el-table-column
            v-if="role=='CITY_ADMIN'"
            align="center"
            label="操作时间"
            prop="createTime"
            width="140"
        ></el-table-column>
        <el-table-column
            align="center"
            label="报名ID"
            prop="signId"
            width="140"
        ></el-table-column>
        <el-table-column
            align="center"
            label="学生姓名"
            prop="studentName"
            width="120"
        >
        </el-table-column>
        <el-table-column
            align="center"
            label="身份证号"
            prop="idCard"
            width="170"
        ></el-table-column>
        <el-table-column
            align="center"
            label="多胞胎数量"
            prop="tiwnsNum"
            width="85"
        ></el-table-column>
        <el-table-column
            align="center"
            label="原报名学校"
            prop="fromSchoolName"
            width="180"
        >
        </el-table-column>

        <el-table-column
            align="center"
            label="原报名区县"
            prop="fromDeptName"
            width="180"
        ></el-table-column>
        <!--          <el-table-column-->
        <!--            align="center"-->
        <!--            label="调剂状态"-->
        <!--            prop=""-->
        <!--            width="180"-->
        <!--        ></el-table-column>-->
        <!--          <el-table-column-->
        <!--              align="center"-->
        <!--              label="调剂区县"-->
        <!--              prop=""-->
        <!--              width="180"-->
        <!--          ></el-table-column>-->
        <el-table-column
            align="center"
            label="调入学校"
            prop="toSchoolName"
            width="180"
        ></el-table-column>
        <el-table-column
            align="center"
            label="调剂原因"
            prop="content"
            width="180"
        ></el-table-column>
      </el-table>
      <div class="page-container" v-if="total > 0">
        <el-pagination
            background
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page.sync="liuZhuanForm.pageNumber"
            layout="total, prev, pager, next, sizes"
            :page-sizes="$pageSizes"
            :total="liuZhuanTotal"
        >
        </el-pagination>
      </div>
      <span
          slot="footer"
          class="dialog-footer"
      >
            <el-button @click="liuZhuanShow = false">取 消</el-button>
          </span>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import {kuaQuJiLu,getKuaQuList} from "@/api/tiaoChu";
export default {
  name: "index",
  mixins: [TableMixin],
  data() {
    return {
      role:this.$store.getters.role,
      liuZhuanForm:{
        pageSize:10,
        pageNumber:1,
        deptId:''
      },
      liuZhuanData:[],
      liuZhuanTotal:0,
      liuZhuanShow:false,
      search:{
        pageNumber:1,
        pageSize:10,
        keywords:"",
        type:1,
        fromSchoolId:this.$store.getters.role=='SCHOOL'?this.$store.getters.deptId:null,
        fromDeptId:this.$store.getters.role=='CITY_ADMIN'||this.$store.getters.role=='SCHOOL'?null:this.$store.getters.deptId
      },
     tableData:{
       records:[]
     }
  }
  },
  methods:{
    getTable(){
      this.search.pageNumber=1
      this.getTableData()
    },
    getTableData(){
        getKuaQuList(this.search).then(res=>{
          this.tableData=res
          this.total=Number(res.total)
        })
    },
    liuZhuan(row){
      this.liuZhuanShow=true
      this.liuZhuanForm.studentId=row.studentId
      this.liuZhuanForm.deptId=row.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        console.log(res,"res");
        this.liuZhuanData=res.records
        this.liuZhuanTotal=Number(res.total)
      })
    },
    liuZhuan1(row){
      // this.liuZhuanShow=true
      // this.liuZhuanForm.studentId=row.id
      // this.liuZhuanForm.deptId=this.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        console.log(res,"res");
        this.liuZhuanData=res.records
        this.liuZhuanTotal=Number(res.total)
      })
    },
    handleSizeChange1(size) {
      this.liuZhuanForm.pageSize = size
      this.liuZhuanForm.pageNumber = 1
      this.liuZhuan1()
    },
    handleCurrentChange1(page) {
      this.liuZhuanForm.pageNumber = page
      this.liuZhuan1()
    }
  }
}
</script>

<style scoped>

</style>
