<template>
  <div class="sd-wechat-container" v-loading="showLoading"></div>
</template>

<script>
import { loginConfrimApi } from "@/api/login";
export default {
  data() {
    return {
      showLoading: true,
    };
  },
  async created() {
    const token = this.$store.getters.token;
    if (!token) {
      this.$router.push("/login");
    } else {
      const query = this.$route.query;
      if (query && query.state && query.code) {
        try {
          const res = await loginConfrimApi({
            code: query.code,
            state: query.state,
          });
          if (res) {
            this.$store.commit("user/SET_SECURITYKEY", res);
            this.$store.commit("user/SET_ISLOGINCONFIRM", "1");
            this.$router.push("/");
          }
        } catch (e) {
          this.$router.push("/login");
        }
      } else {
        this.$router.push("/login");
      }
    }
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.sd-wechat-container {
  height: 100vh;
  width: 100%;
  background-color: #8c939d;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>