import { sysStateApi } from "@/api/login";

const showLoginForm = (elementId, returnUrl, style = undefined) => {
  sysStateApi().then(res => {
    if (res.appId) {
      const cssBase64 = style
      new WxLogin({
        self_redirect: false,
        id: elementId,
        appid: res.appId, // 公众号appid wx*******
        scope: "snsapi_login", // 网页默认即可
        redirect_uri: encodeURIComponent(res.redirectHost + returnUrl), // 授权成功后回调的url
        state: res.state, // 可设置为简单的随机数加session用来校验
        href: 'data:text/css;base64,' + cssBase64
      });
    }
  });
}
export { showLoginForm }
