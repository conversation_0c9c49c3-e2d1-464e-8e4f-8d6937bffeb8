<template>
  <div class="sd-wechat-container" v-loading="showLoading">
    <div class="sd-qrcode-container" v-show="showBindSuccess">
      <div class="sd-title">绑定微信</div>
      <div>
        <div style="font-size: 140px; color: #008a00;">
          <i class="el-icon-circle-check"></i>
        </div>
      </div>
      <div class="sd-warning-text-success sd-color-error">
        <div>绑定成功！</div>
        <div>返回登录页面，重新登录....</div>
      </div>
    </div>
  </div>
</template>

<script>
import { wechatBind } from "@/api/login";

export default {
  data() {
    return {
      showLoading: false,
      showBindSuccess: false,
    };
  },
  async created() {
    setTimeout(() => {
      this.showLoading = false;
    }, 1000);
    const token = this.$store.getters.token;
    if (!token) {
      this.$router.push("/login");
    } else {
      const query = this.$route.query;
      if (query && query.state && query.code) {
        try {
          const res = await wechatBind({
            code: query.code,
            state: query.state,
          });
          if (res) {
            this.showBindSuccess = true;
            this.$store.commit("SET_ISBIND", "1");
            setTimeout(() => {
              this.$router.push("/editPwd");
            }, 2500);
          }
        } catch (e) {
          this.$router.push("/login");
        }
      } else {
        this.$router.push("/login");
      }
    }
  },
  methods: {
    back() {
      this.$store.dispatch("user/logout").then(() => {
        this.$router.push("/login");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.sd-wechat-container {
  height: 100vh;
  width: 100%;
  background-color: #8c939d;
  display: flex;
  justify-content: center;
  align-items: center;

  .sd-qrcode-container {
    width: 500px;
    height: 500px;
    background-color: #eeeeee;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    border-radius: 10px;

    .sd-title {
      height: 100px;
      line-height: 100px;
      font-size: 24px;
      font-weight: bold;
    }

    .sd-warning-text {
      position: absolute;
      padding: 30px;
      top: 450px;
    }

    .sd-warning-text-success {
      text-align: center;
      line-height: 40px;
    }
  }
}
</style>
