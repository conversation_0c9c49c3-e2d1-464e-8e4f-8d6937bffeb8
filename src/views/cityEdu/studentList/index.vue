<template>
  <div>
    <el-tabs
        tab-position="left"
        :stretch="true"
        type="border-card"
        v-model="prefixDeptCode"

    >
<!--      @tab-click="getList"-->
      <el-tab-pane
          v-for="item in deptOptions"
          :label="item.deptName"
          :name="item.deptCode"
          :key="item.id"
          style="overflow: scroll;height:800px"
      >
        <biaoZhunBanXiaoXueStudentList :deptId="item.deptId" :deptCode="item.deptCode" v-if="item.deptCode==prefixDeptCode&&item.isFive=='0'"></biaoZhunBanXiaoXueStudentList>
        <shi-wu-qu-student-list :deptId="item.id" :deptCode="item.deptCode" v-if="item.deptCode==prefixDeptCode&&item.isFive=='1'"></shi-wu-qu-student-list>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import biaoZhunBanXiaoXueStudentList from "@/components/biaoZhunBanXiaoXueStudentList/index";
import shiWuQuStudentList  from "@/components/shiWuQuStudentList/list";
import { getDepts } from "@/api/common";
export default {
  name: "index",
  mixins: [TableMixin,ModalMixin],
  components: {biaoZhunBanXiaoXueStudentList,shiWuQuStudentList},
  data() {
    return {
      prefixDeptCode: "0", // 当前选项卡区县
      search: {
        keywords: "",
      },
      modal: {
        addOrEdit: false,
        import: false,
      },
      tableLoading: false,
      dialogTitle: "",
      form: {
        idNumber: "",
        studentId: "",
        studentName: "",
        deptName: "",
        enrollSchoolName: "",
      },
      deptOptions: [],
      rules: {
        idNumber: [
          { required: true, message: "请输入学生身份证号", trigger: "change" },
        ],
      },
      submitLoading: false,
      submitBatchLoading: false,
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
    };
  },
  created() {
    this.getTableData()
  },
  methods: {
    // 初次查询
    async getTableData() {
      // if (this.prefixDeptCode != "0") {
      //   // this.getList();
      // } else {
        let depts = await getDepts({ level: 2 });
     this.deptOptions =depts
        this.prefixDeptCode = this.deptOptions[0].deptCode;
        // this.getList();
      // }
    },
    getList(){}
  },
};
</script>

<style scoped lang="scss">
.el-tabs {
  height: calc(100vh - var(--header-height) - 35px);
  .category {
    float: left;
    text-align: left;
    padding-left: 10px;
  }
}
.el-form {
  padding-bottom: 40px;
  .el-form-item {
    margin-bottom: 5px;
  }
  .stu-search {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-bottom: 30px;
  }
}
</style>
