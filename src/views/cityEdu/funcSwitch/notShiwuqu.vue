<template>
  <div>
    <div class="switch-title">{{ deptName }}公办中小学教育局功能开关设置</div>
    <div class="switch-container">
      <ul class="switch-box">
        <li class="switch-item" v-for="item in switchList" :key="item.id">
          <span class="switch-item-label">{{ item.permissionName }}</span>
          <el-switch
            v-model="item.statusTemp"
            active-color="#13ce66"
            inactive-color="#dcdfe6"
            @change="handleSwitch(item)"
          >
          </el-switch>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { permissionList, permissionSave } from "@/api/qianYiCity";
export default {
  name: "notShiwuqu",
  data() {
    return {
      prefixDeptCode: "",
      switchList: [],
    };
  },
  props: {
    deptCode: {
      type: String,
      default: "",
    },
    deptName: {
      type: String,
      default: "",
    },
  },
  created() {
    this.prefixDeptCode = this.deptCode;
    this.getSwitchList();
  },
  methods: {
    getSwitchList() {
      permissionList({}, this.prefixDeptCode).then((res) => {
        this.switchList = res;
        this.switchList.forEach((item) => {
          item.status == "1"
            ? (item.statusTemp = false)
            : (item.statusTemp = true);
        });
      });
    },
    handleSwitch(item) {
      let params = {
        id: item.id,
        status: item.statusTemp ? "2" : "1",
      };
      permissionSave(params, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getSwitchList();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.switch-title {
  font-size: 20px;
  margin-bottom: 30px;
}
.switch-container {
  width: 800px;
  display: flex;
  justify-content: space-between;
  .switch-box {
    .el-switch {
      margin-left: 20px;
    }
    .switch-item {
      margin-bottom: 15px;
      .switch-item-label {
        display: inline-block;
        width: 260px;
      }
    }
  }
}
</style>
