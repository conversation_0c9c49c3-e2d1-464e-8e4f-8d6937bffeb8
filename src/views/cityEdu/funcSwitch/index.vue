<template>
  <div>
<!--    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/welcome' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>功能开关设置</el-breadcrumb-item>
    </el-breadcrumb>-->
    <el-tabs
      tab-position="left"
      :stretch="true"
      type="border-card"
      v-model="deptCode"
    >
      <el-tab-pane
        v-for="item in deptOptions"
        :label="item.deptName"
        :name="item.deptCode"
        :key="item.id"
      >
        <NotShiwuqu
          :deptCode="item.deptCode"
          :deptName="item.deptName"
          v-if=" deptCode == item.deptCode"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getDepts } from "@/api/qianYiCity";
import NotShiwuqu from "@/views/cityEdu/funcSwitch/notShiwuqu";
export default {
  name: "signExportSet",
  components: { NotShiwuqu },
  data() {
    return {
      deptCode: "",
      deptOptions: [],
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
    async getTableData() {
      let depts = await getDepts({ level: 2 });
      this.deptOptions = depts
      this.deptCode = this.deptOptions[0].deptCode;
    },
  },
};
</script>

<style scoped lang="scss">
.el-tabs {
  height: calc(100vh - 160px);
  margin-top: 10px;
}
</style>
