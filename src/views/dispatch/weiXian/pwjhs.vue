<template>
  <div>
    <div>
      <el-button type="primary" @click="showSchool()">添加摇号学校</el-button>
      <p></p>
    </div>
    <el-table
        :data="tableData"
        style="width: 100%"
        border
    >
      <el-table-column
          type="index"
          label="序号"
          align="center"
          width="80">
      </el-table-column>
      <el-table-column
          prop="schoolName"
          label="摇号学校"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="planNum"
          label="空余学位数"
          align="center"
      >
      </el-table-column>
      <el-table-column
          label="操作"
          align="center"
          width="180">
        <template slot-scope="scope">
          <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)">编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        title="设置招生计划数"
        :visible.sync="jiHuaShow"
        align="center"
    >
      <el-form :model="form" style="width:300px">
        <el-form-item
            label="计划数"
        >
          <el-input
              v-model="form.planNum"
              placeholder="请输入计划数"
              clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span style="color: red;font-size:25px;font-weight: bold">
            注: 请确认计划总数是否符合学校学位数，如不符合请调整计划数
          </span>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="jiHuaShow = false">取 消</el-button>
        <el-button
            type="primary"
            @click="add"
        >确 定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
        title="添加摇号学校"
        :visible.sync="addSchoolFlag"
        width="30%"
        center
    >
      <el-form
          :model="form"
          label-width="120px"
          class="dialog-form"
      >
        <el-form-item label="摇号学校：">
          <el-select
              v-model="form.schoolId"
              placeholder="请选择摇号学校"
              clearable
              style="width: 100%"
              @change="handleSchoolChange"
          >
            <el-option
                v-for="item in schoolList"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="空余学位数：">
          <el-input
              v-model.number="form.planNum"
              placeholder="请输入空余学位数"
              clearable
              style="width: 100%"
              type="number"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addSchoolFlag = false">取 消</el-button>
        <el-button
            type="primary"
            @click="addSchool"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getPWSchoolList, updateSchoolaJiHuaShu} from "@/api/quXianPW";
import {getDepts} from "@/api/common";

export default {
  name: "pwjhs",
  data(){
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      tableData: [],
      schoolList:[],
      jiHuaShow: false,
      addSchoolFlag: false,
      form: {
        id:'',
        planNum: 0,
        schoolName: '',
        schoolId: ''
      }
    }
  },
  created() {
    this.getTable()
  },
  methods:{
    handleSchoolChange(schoolId) {
      if (schoolId) {
        const selectedSchool = this.schoolList.find(item => item.id === schoolId);
        this.form.schoolName = selectedSchool ? selectedSchool.deptName : '';
      } else {
        this.form.schoolName = '';
      }
    },
    getTable(){
      getPWSchoolList({},this.prefixDeptCode).then(res=>{
        console.log(JSON.stringify(res,null, 2))
        this.tableData=res
      })
    },
    // 查询学校
    getDepts() {
      let params = {
        level: 3,
        period: "3",
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    handleEdit(row) {
      this.getDepts()
      this.form.id = row.id
      this.form.planNum = row.planNum
      this.form.schoolName = row.schoolName
      this.form.schoolId = row.schoolId
      // this.jiHuaShow = true;
      this.addSchoolFlag = true;
    },
    async add() {
      // this.jiHuaShow = false;
      this.addSchoolFlag = false;
      try {
        await updateSchoolaJiHuaShu(this.form, this.prefixDeptCode);
        this.getTable(); // 确保在更新成功后再获取最新数据
        this.$message.success('计划数更新成功');
      } catch (error) {
        this.$message.error('计划数更新失败');
        console.error(error);
      }
    },
    showSchool(){
      this.form= {}
      this.getDepts()
      this.addSchoolFlag = true;
    },
    async addSchool(){
      // 添加摇号学校
      if (!this.form.schoolId) {
        this.$message.error('请选择摇号学校');
        return;
      }
      if (!this.form.planNum || this.form.planNum <= 0) {
        this.$message.error('请输入有效的空余学位数');
        return;
      }
      //添加摇号学校
      console.log(JSON.stringify(this.form, null, 2))

      await updateSchoolaJiHuaShu(this.form, this.prefixDeptCode);
      this.getTable(); // 确保在更新成功后再获取最新数据
      this.addSchoolFlag = false;
    }
  }
}
</script>

<style scoped>
.dialog-form {
  margin: 0 auto;
  max-width: 400px;
}
.dialog-footer {
  text-align: center;
}
</style>