<template>
  <div>
    <div>
      <el-button type="primary" @click="openImport()">批量导入</el-button>
      <p></p>
    </div>
    <el-table
        :data="tableData"
        style="width: 100%"
        border
    >
      <el-table-column
          type="index"
          label="序号"
          align="center"
          width="80">
      </el-table-column>
      <el-table-column
          prop="studentName"
          label="学生姓名"
          align="center"
      >
      </el-table-column>
      <el-table-column
          :formatter="formatterGender"
          prop="studentGender"
          label="性别"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="studentIdCardNumber"
          label="学籍号"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="graduatePrimarySchool"
          label="毕业学校"
          align="center"
      >
      </el-table-column>
    </el-table>
    <div class="sd-pagination-container">
      <el-pagination
          background
          layout="total, prev, pager, next,sizes"
          :total="Number(this.tableDataTotal || 0)"
          :current-page.sync="search.pageNumber"
          :page-size.sync="search.pageSize"
          :page-sizes="[5, 10, 30, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog :visible.sync="modal.import" center title="导入信息" :close-on-click-modal="false" width="500px">
      <el-upload action="#" :limit="upload.limit" :auto-upload="upload.auto" :file-list="upload.list"
                 :on-change="changeFile" :on-remove="removeFile">
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>
      <el-form ref="form" label-width="80px">
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button size="small" @click="switchModal('import', false)">取消</el-button>
        <el-button size="small" type="primary" :disabled="upload.list.length === 0" @click="confirmUpload">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {daoRupaiWei} from "@/api/linZhangPaiWei";
import ModalMixin from "@/mixins/ModalMixin";
import {daiPaiWeiStudent, getStudentByStepAndSchoolList} from "@/api/quXianPW";
import TableMixin from "@/mixins/TableMixin";

export default {
  mixins: [ModalMixin,TableMixin],
  data(){
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      tableData: [],
      tableDataTotal: 0,
      modal: {
        import: false,
        reject: false,
      },
      upload: {
        limit: 1,
        auto: false,
        list: [],
      },
      errorMessages: [],
      e: "3"
    }
  },
  async created() {
    await this.getTableData();
  },
  methods:{
    formatterGender(row) {
      switch (row.studentGender) {
        case 1:
          return "男";
        case 0:
          return "女";
        default:
          return "未知";
      }
    },
    openImport() {
      this.switchModal("import", true);
      this.removeFile();
      this.errorMessages = [];
    },
    // 上传
    confirmUpload() {
      this.$message.info('正在导入，数据量较大，请耐心等待')
      let fd = new FormData();
      fd.append("file", this.upload.list[0]);
      fd.append("status", this.e);
      daoRupaiWei(fd, this.prefixDeptCode).then(() => {
        this.switchModal("import", false);
        this.$message.success("操作成功");
        this.loadStudentList();
      });
    },
    // 上传
    changeFile(file) {
      this.upload.list = [file.raw];
    },
    // 删除上传
    removeFile() {
      this.upload.list = [];
    },
    loadStudentList() {
      this.loading1 = true
      this.$message.info('数据量较大，请耐心等待')
      let fd = new FormData();
      fd.append("status", this.e);
      daiPaiWeiStudent(fd, this.prefixDeptCode).then(res => {
        this.studentList = res;
        this.loading1 = false
      });
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
    async getTableData() {
      console.log(this.search);
      this.tableLoading = true;
      this.$message.info('数据量较大，请耐心等待')
      getStudentByStepAndSchoolList(this.search,this.prefixDeptCode)
          .then((res) => {
            this.tableData = res.records;
            this.tableDataTotal = res.total;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
  }
}
</script>

<style scoped>
</style>