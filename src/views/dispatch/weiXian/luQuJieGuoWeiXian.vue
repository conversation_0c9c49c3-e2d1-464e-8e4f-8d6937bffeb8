<template>
  <div class="admission-grid">
    <!-- 溢阳中学卡片 -->
    <div class="school-card" v-for="(item, index) in schoolList" :key="index">
      <div class="compact-header">
        <h2 class="school-name">{{item.schoolName}}</h2>
      </div>
      <div class="compact-data">
        <div>
          <span class="data-label">学校招生人数: </span>
          <span class="data-value">{{ item.planNum }}人</span>
        </div>
      </div>
      <div class="compact-data">
        <div>
          <span class="data-label">学校录取人数: </span>
          <span class="data-value highlight">{{ item.actualEnrolled }}人</span>
        </div>
      </div>

      <div class="button-container">
        <button @click="nextPage(index)" class="compact-result-button">
          录取结果
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import {getPwSchoolStudentList} from "@/api/quXianPW";
export default {
  name: "luQuJieGuoWeiXian",
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      schoolList:[],
    }
  },
  created() {
    this.getTableData()
  },
  methods:{
    getTableData(){
      getPwSchoolStudentList(this.prefixDeptCode).then(res=>{
        this.schoolList = res
        console.log(JSON.stringify(res, null, 2))
        // 2. 跳转代码

      })
    },
    nextPage(index){
      // console.log(JSON.stringify(this.schoolList[index].schoolId));
      this.$router.push({
        path: 'luQUJieGuoSchoolWeiXian',
        query: { schoolId: this.schoolList[index].schoolId}  // 必须用 name，不能用 path
      });
    }
  }
}
</script>


<style scoped lang="scss">
.admission-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 固定5列 */
  gap: 12px;
  width: 100%;
  margin: 0 auto;
  padding: 15px;
  font-family: 'Arial', sans-serif;
  box-sizing: border-box;
}

.school-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  height: 140px; /* 稍微降低高度 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* 防止内容溢出 */
}

.compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.school-name {
  margin: 0;
  color: #333;
  font-size: 1rem; /* 缩小字体 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.admission-status {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem; /* 缩小字体 */
}

.admitted {
  background-color: #e6f7e6;
  color: #2e7d32;
}

.compact-data {
  display: flex;
  gap: 10px; /* 缩小间隙 */
  margin: 5px 0;
  flex-wrap: wrap;
}

.compact-data div {
  display: flex;
  align-items: center;
  min-width: 0;
}

.data-label {
  color: #666;
  font-size: 0.75rem; /* 缩小字体 */
  margin-right: 3px;
  white-space: nowrap;
}

.data-value {
  font-size: 0.85rem; /* 缩小字体 */
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.highlight {
  color: #2e7d32;
}

.button-container {
  display: flex;
  justify-content: flex-end; /* 按钮容器右对齐 */
  width: 100%;
}

.compact-result-button {
  padding: 5px 12px; /* 调整左右内边距 */
  background-color: #f0f7ff;
  border: 1px solid #d0e3ff;
  border-radius: 5px;
  color: #1a73e8;
  font-size: 0.8rem; /* 缩小字体 */
  cursor: pointer;
  transition: background 0.2s;
  white-space: nowrap;
  width: auto; /* 移除固定宽度 */
}

.compact-result-button:hover {
  background-color: #e1ecff;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .admission-grid {
    grid-template-columns: repeat(3, 1fr); /* 中等屏幕显示3列 */
  }
}

@media (max-width: 768px) {
  .admission-grid {
    grid-template-columns: repeat(2, 1fr); /* 小屏幕显示2列 */
  }
}

@media (max-width: 480px) {
  .admission-grid {
    grid-template-columns: 1fr; /* 超小屏幕显示1列 */
  }
}
</style>
