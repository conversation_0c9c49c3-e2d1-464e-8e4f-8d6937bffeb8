<template>
  <div>
    <el-form :model="search" :inline="true">
      <el-form-item>
        <el-input v-model="search.keywords" size="small" clearable placeholder="学生姓名/身份证号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
            @change="change"
            size="small"
            v-model="search.sex"
            placeholder="选择性别"
            class="sd-w-130"
            clearable
        >
          <el-option label="男" :value="1"></el-option>
          <el-option label="女" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
            size="small"
            type="primary"
            icon="el-icon-search"
            @click="getTableData1"
        ></el-button>
        <el-button
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="exportData"
          :loading="exportLoading"
      >导出报名信息</el-button
      >
      </el-form-item>

    </el-form>
    <el-table :data="tableData" border stripe >
			<el-table-column
				type="index"
				align="center"
				label="序号"
				width="100"
			></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
      ></el-table-column>
      <el-table-column
          align="center"
          label="性别"
          prop="studentIdCardNumber"
      >
        <template slot-scope="scope">
          {{ scope.row.studentGender == 1 ? "男" :scope.row.studentGender == 0 ? "女" :''}}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="录取学校"
          prop="volunteerSchoolName"
      ></el-table-column>

    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="pageSizes"
      >
        <!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getPWSchoolList,huoQuChengGongPaiWei} from "@/api/quXianPW";
import { getDepts } from "@/api/common"
import {pref} from "@/utils/common";
export default {
  name: "luQuJieGuo",
  data(){
    return{
      exportLoading:false,
      prefixDeptCode: this.$store.getters.deptCode,
      tableData:[],
      schoolList:[],
      pageSizes:[10,300,500,800],
      total:0,
      search:{
        pageNumber:1,
        pageSize:10,
				isTwins: '',
				schoolId: this.$store.getters.deptId
      }
    }
  },
  created() {
		this.getTableData()
  },
  methods:{
    change(){
      this.$forceUpdate()
    },
    getTableData1(){
     this.search.pageNumber=1
     this.getTableData()
    },
    getTableData(){
      huoQuChengGongPaiWei(this.search,this.prefixDeptCode).then(res=>{
        this.tableData = res.records
        this.total =Number(res.total)
      })
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
    exportData(){
      this.$message.info('正在导出，数据量较大，请耐心等待')
      this.exportLoading = true;
      let params = this.search;
      this.$download(
          `${pref+this.prefixDeptCode}/biz/TbPaiWeiSchoolSettingController/exportPwData`,
          params,
          "xls",
          "派位成功学生列表.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("下载成功");
      });
    },
  }
}
</script>

<style scoped>

</style>
