<template>
  <div>
    <el-breadcrumb separator="/" style="font-size: 16px">
      <el-breadcrumb-item>首页</el-breadcrumb-item>
      <el-breadcrumb-item>电脑派位管理</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="selct" style="margin: 10px 0;">
      <el-button type="primary" @click="back" icon="el-icon-caret-left">返回</el-button>
<!--      <el-button type="primary" @click="button1Click()">重置</el-button>-->
    </div>
    <div class="central" style="margin: 10px 0">
      <span class="central_center">{{ assingnmentSchoolName }}电脑派位录取工作</span>
    </div>
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="grid-content bg-purple">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>{{stateName}}（{{ studentList.length }}）人</span>
            </div>
            <el-table :data="studentList" border style="width: 100%" max-height="500" height="500">
              <!--<el-table-column prop="id" label="报名ID" width="120" align="center"></el-table-column>-->
              <el-table-column prop="stuName" label="学生姓名" width="120" align="center"></el-table-column>
              <el-table-column prop="gender" :formatter="formatterGender" label="性别" width="50"
                align="center"></el-table-column>
              <el-table-column prop="idCard" label="身份证" align="center"></el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="grid-content bg-purple-light">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>派位成功（{{ endStudentList.length }}）人</span>
            </div>
            <el-table :data="endStudentList" border style="width: 100%" max-height="500" height="500"
              header-align="center" ref="endStudentList">
              <!--<el-table-column prop="assignmentNum" label="报名号" width="120"-->
              <!--align="center"></el-table-column>-->
              <el-table-column prop="stuName" label="学生姓名" width="120" align="center"></el-table-column>
              <el-table-column prop="gender" :formatter="formatterGender" label="性别" width="50"
                align="center"></el-table-column>
              <el-table-column prop="idCard" label="身份证" align="center"></el-table-column>
              <el-table-column prop="status" label="状态" width="120" align="center"></el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>
    <div class="rowbtn" v-show="!theEnd">
      <el-button @click="begin()" :disabled="(generateNumDisabled || studentList.length == 0) && paiweiState">随机打乱顺序</el-button>
      <el-button @click="stop()" :disabled="sortListDisabled">停止打乱顺序</el-button>
      <el-button type="primary" @click="stop1()" :disabled="doAssignmentDisabled">开始派位</el-button>
      <!--      <el-button type="danger" @click="stop1()" :disabled="endAssignmentDisabled">结束派位</el-button>-->
      <el-button type="danger" @click="confirmEnd()" v-show="confirmEndVisable">结束派位</el-button>
    </div>
  </div>
</template>
<script>
import {
  assignmentStudentListApi,
  schoolGetApi,
  assignmentInitAssignmentApi,
  assignmentConfirmationApi,
  assignmentPrimaryInfoApi, assignmentResetApi
} from "@/api/qingHePaiWei";

export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      generateNumDisabled: false,
      sortListDisabled: true,
      generateStartDisabled: false,
      generateIntervalDisabled: false,
      doAssignmentDisabled: true,
      endAssignmentDisabled: true,
      paiweiState:true,
      assingnmentSchoolId: "",
      assingnmentSchoolName: "",
      assignmentInfo: "",
      startNum: "",
      interval: "",
      confirmEndVisable: false,
      theEnd: false,
      stateName:"等待派位",
      studentList: [],
      endStudentList: [],
      doneStudentList: [],
      successStudentList: [],
      houBuStudentList: [],
      localStudentList: [],
      failStudentList: [],
      agmtSchool: '',
      sourceType: '',
      resData:'',
      planQuantity: 0,
      currentIndex: 0, // 当前索引
      intervalId: null, // 定时器ID
      form: {
        key: ''
      }

    };
  },
  async created() {
    this.assingnmentSchoolId = localStorage.getItem("assingnmentSchoolId");
    this.assingnmentSchoolName = localStorage.getItem("assingnmentSchoolName");
    this.sourceType = this.$route.query.sourceType;
    this.agmtSchool = this.$route.query.agmtSchool;
    await this.initAssignment();
    await this.loadStudentList();
  },
  methods: {
    button1Click(){
      assignmentResetApi(this.assingnmentSchoolId, this.prefixDeptCode).then(res => {
        if (res){
          this.$message.success("重置成功")
          this.loadStudentList();
        }
      })
    },
    getSchoolInfo() {
      this.form.key = this.assignmentInfo.schoolId;
      schoolGetApi(this.form, this.prefixDeptCode).then(res => {
        if (res.code == "200") {
          const result = res.result;
          this.planQuantity = result.planNum;
        }
      });
    },
    begin() {
      this.generateNumDisabled = true;
      this.sortListDisabled = false;
      let arr = this.studentList;
      this.interval = setInterval(() => {
        arr.sort(function () {
          return 0.5 - Math.random();
        });
      }, 1000);
    },
    beginAssignment() {
      this.generateNumDisabled = true;
      this.sortListDisabled = true;
      this.doAssignmentDisabled = true;
      this.endAssignmentDisabled = false;
      let arr = this.studentList;
      this.interval = setInterval(() => {
        arr.sort(function () {
          return 0.5 - Math.random();
        });
      }, 100);
    },
    stop() {
      this.generateNumDisabled = false;
      this.sortListDisabled = true;
      this.doAssignmentDisabled = false;
      clearInterval(this.interval);
    },
    async stop1() {
      this.generateNumDisabled = true;
      this.sortListDisabled = true;
      this.doAssignmentDisabled = true;
      this.endAssignmentDisabled = false;

      // 调用接口获取对口学生信息
      this.form.key = this.assingnmentSchoolId;
      let directStudentsIdCards = [];
      try {
        const res = await assignmentPrimaryInfoApi(this.form, this.prefixDeptCode);
       
        // 假设接口返回的结果中，对口学生的身份证号在 res.result.idCards 数组中
        res.result.forEach(item => {
            if (item.idCard) {
              directStudentsIdCards.push(item.idCard);
            }
          });
      } catch (error) {
        console.error('获取对口学生信息失败:', error);
        this.$message.error('获取对口学生信息失败，请检查网络');
        return;
      }
      console.log(directStudentsIdCards,"对口学生");

      // 区分直升学生和普通学生
      const directStudents = this.studentList.filter(student => 
        directStudentsIdCards.includes(String(student.stuId))
      );
      const normalStudents = this.studentList.filter(student => 
        !directStudentsIdCards.includes(String(student.stuId))
      );

      this.successStudentList = [];
      this.failStudentList = [];

       // 先处理直升学生
      directStudents.forEach(student => { 
        if (this.successStudentList.length < this.planQuantity) { 
          student.status = "成功";
          this.successStudentList.push(student);
        } else { 
          student.status = "派位失败";
          this.failStudentList.push(student);
        }
      });

      // 计算剩余学位
      const remainingQuota = this.planQuantity - this.successStudentList.length;

      // 打乱普通学生顺序
      const shuffledNormalStudents = normalStudents.sort(() => 0.5 - Math.random());

      // 处理普通学生
      if (remainingQuota > 0) {
        shuffledNormalStudents.slice(0, remainingQuota).forEach(student => {
          student.status = "成功";
          this.successStudentList.push(student);
        });
      }
      // 处理未被录取的普通学生
      shuffledNormalStudents.slice(remainingQuota).forEach(student => {
        student.status = "派位失败";
        this.failStudentList.push(student);
      });
      assignmentConfirmationApi(
          this.assingnmentSchoolId,
          this.assignmentInfo.id,
          this.agmtSchool,
          this.sourceType,
          this.successStudentList,
          this.failStudentList,
          this.prefixDeptCode
      ).then(res => {
        if (res.code == "200") {
          this.resData = res.result;
        } else {
          console.error('API 请求失败:', res);
          this.$message.error(res.msg);
          return false;
        }
      }).catch(error => {
        console.error('API 请求出错:', error);
        alert('API 请求出错，请检查控制台日志');
      });
      // 滚动效果实现
      let allStudents = [...directStudents, ...normalStudents];
      let index = 0;
      let isRolling = true;

      // 滚动定时器
      const rollingInterval = setInterval(() => {
        if (isRolling) {
          // 随机显示学生，模拟滚动效果
          const randomIndex = Math.floor(Math.random() * allStudents.length);
        }
      }, 100);

        clearInterval(rollingInterval);
        isRolling = false;

        let allResultList = this.successStudentList.concat(this.failStudentList);
        this.currentIndex = 0;

        if (this.intervalId) {
          clearInterval(this.intervalId);
        }

        this.intervalId = setInterval(() => {
          if (this.currentIndex < allResultList.length) {
            let item = allResultList[this.currentIndex];
            if(item.status === "派位失败"){
              this.$message.success("派位完成");
              this.stateName="派位失败"
              clearInterval(this.intervalId);
              this.currentIndex = 0;
              return
            }
            this.endStudentList.unshift(item);
            for (let i = 0; i < this.studentList.length; i++) {
              if (this.studentList[i].idCard === item.idCard) {
                this.studentList.splice(i, 1);
                break;
              }
            }
            this.currentIndex++;
          } else {
            clearInterval(this.intervalId);
            this.currentIndex = 0;
            console.log('所有数据都已添加');
          }

        }, 500);

        this.endAssignmentDisabled = true;
        this.confirmEndVisable = true;
    
    },
    // 数字位数不够，进行前补零
    PrefixZero(num, n) {
      return (Array(n).join(0) + num).slice(-n);
    },

    // 列表-对“性别”进行格式化
    formatterGender(row, column) {
      switch (row.gender) {
       
        case "1":
          return "男";
          break;
        case "2":
          return "女";
          break;
        default:
          return "未知";
      }
    },
    initAssignment() {
      this.form.key = this.assingnmentSchoolId;
      assignmentPrimaryInfoApi(this.form, this.prefixDeptCode).then(res=>{
        // console.log(res)
      })
      assignmentInitAssignmentApi(this.assingnmentSchoolId, this.prefixDeptCode).then(res => {
        if (res.code == "200") {
          this.assignmentInfo = res.result;
          // console.log(this.assignmentInfo)
          this.getSchoolInfo();
        } else {
          this.$message.error(res.msg);
          return false;
        }
      });
    },
    confirmEnd() {
      // console.log(this.assignmentInfo)
      this.theEnd = false;
      this.$confirm("确认派位完成？").then(_ => {
        this.theEnd = this.resData;
        this.$router.back()
      })
    },
    back() {
      // console.log(this.assignmentInfo
      this.theEnd = false;
      this.$confirm("确认返回？").then(_ => {
        this.theEnd = this.resData;
        this.$router.back()
      })
    },
    loadStudentList() {
      assignmentStudentListApi(this.assingnmentSchoolId, this.prefixDeptCode).then(res => {
        if (res.code == "200") {
          // 清空现有列表
          this.studentList = [];
          this.endStudentList = [];

          // 根据状态分配学生到不同列表
          res.result.forEach(student => {
            if (student.status === 'checking' || student.status === 'notPass') {
              // 状态为checking的学生进入等待派位列表
              this.studentList.push(student);
              if (student.status == 'notPass'){
                this.stateName="派位失败"
                this.paiweiState =false
              }
            } else if(student.status === 'passed'){
              // 其他状态的学生进入派位完成列表
              // 修改状态显示文本
              if (student.status == 'passed') {
                student.status = '派位成功';
              }
              this.endStudentList.push(student);
            }
          });

        } else {
          this.$message.error(res.msg);
          return false;
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.central {
  height: 80px;
  line-height: 80px;
  position: relative;

  .central_center {
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    color: #01aaed;
    font-size: 24px;
  }

  .central_right {
    float: right;
    display: flex;
    justify-content: space-evenly;
    width: 400px;

    .central_right_de {
      display: flex;
      align-items: center;

      .boxB {
        border: 1px solid #909399;
        height: 20px;
        color: #01aaed;
        text-align: center;
        width: 50px;
        line-height: 20px;
      }
    }
  }
}

.rowbtn {
  display: flex;
  justify-content: space-evenly;

  .el-button {
    width: 13%;
    border-radius: 8px;
  }
}
</style>
