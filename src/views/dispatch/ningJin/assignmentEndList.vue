<template>
  <div>
    <el-breadcrumb separator="/" style="font-size: 16px">
      <el-breadcrumb-item>首页</el-breadcrumb-item>
      <el-breadcrumb-item>电脑派位管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ assingnmentSchoolName }}</el-breadcrumb-item>
      <el-breadcrumb-item>电脑派位录取结果</el-breadcrumb-item>
    </el-breadcrumb>

    <div style="margin: 10px 0 10px 0">
      <el-button type="primary" @click="returnPage" icon="el-icon-caret-left">返回</el-button>
      <el-button type="primary" @click="button1Click()" icon="el-icon-download" :disabled="isAssign" :loading="exportLoading" >导出</el-button>
    </div>
    <div class="selct">

      <el-input class="inputc" v-model="searchForm.stuName" placeholder="学生姓名"></el-input>
      <el-input class="inputc" v-model="searchForm.idCard" placeholder="身份证号"></el-input>
      <el-button class="butto" @click="handleSearch()" type="success" icon="el-icon-search">查询</el-button>
    </div>

    <div class="tabs">
      <el-table :data="tableData" border stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="stuName" label="学生姓名" width="150"></el-table-column>
        <el-table-column prop="idCard" label="身份证号"></el-table-column>
        <el-table-column prop="gender" label="性别" :formatter="formatterGender" width="100"></el-table-column>
        <el-table-column prop="phoneOne" label="联系电话"></el-table-column>
        <el-table-column prop="schoolName" label="报名学校"></el-table-column>
        <el-table-column prop="createTime" label="报名时间" :formatter="formatDate"></el-table-column>
        <el-table-column prop="verifyStatus" label="派位状态" :formatter="formatterStatus"></el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <span>人数：{{ total }}人</span>
      <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-sizes="$pageSizes"
          :current-page.sync="currentPage"
          @current-change="handleCurrentPageChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { assignmentEndListApi } from "@/api/qingHePaiWei";
import { pref } from "@/utils/common";
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      exportLoading:false,
      prefixDeptCode: this.$store.getters.deptCode,
      assingnmentSchoolId: "",
      assingnmentSchoolName: "",
      pageSize: 10,
      currentPage: 1,
      isAssign: true,
      total: 0,
        agmtSchool:'',
       sourceType:'',
      searchForm: {
        school: "",
        idCard: "",
        stuName: "",
        pageNum: ""
      },
      tableData: [],
      status: [
        {
          value: "passed",
          desc: "派位成功"
        },
        {
          value: "notPass",
          desc: "派位失败"
        }
      ]
    };
  },
  async created() {
    this.assingnmentSchoolId = localStorage.getItem("assingnmentSchoolId");
    this.assingnmentSchoolName = localStorage.getItem("assingnmentSchoolName");
    console.log(this.assingnmentSchoolId);
    this.searchForm.pageNum = 1;
    this.sourceType=this.$route.query.sourceType;
    this.agmtSchool=this.$route.query.agmtSchool;
    this.searchForm.stuName="";
    this.searchForm.idCard="";
    await this.find();
  },
  methods: {
    // 列表-对“性别”进行格式化
    formatterGender(row, column) {
      console.log(row.gender)
      switch (row.gender) {
        case '1':
          return "男";
          break;
        case '2':
          return "女";
          break;
        default:
          return "未知";
      }
    },
    returnPage() {
      if (window.history.length <= 1) {
        this.$router.push({ path: "/zh-CN/home" });
        return false;
      } else {
        this.$router.go(-1);
      }
    },
    formatDate(row, column) {
      // 获取单元格数据
      let data = row[column.property];
      let dt = new Date(data);
      return (
        dt.getFullYear() +
        "-" +
        (dt.getMonth() + 1) +
        "-" +
        dt.getDate() +
        " " +
        dt.getHours() +
        ":" +
        dt.getMinutes() +
        ":" +
        dt.getSeconds()
      );
    },
    // 列表-对“状态”进行格式化
    formatterStatus(row, column) {
      
      switch (row.firstSchoolStatus) {
        case "checking":
          return "等待派位";
          break;
        case "passed":
          return "派位成功";
          break;
        case "notPass":
          return "派位失败";
          break;
        default: // 由于该结果恒定，顾与前端商量写死即可
          return "派位成功";
      }
    },
    // 导出报名信息
    button1Click() {
      this.$message.info('正在导出，数据量较大，请耐心等待')
      this.exportLoading = true;
  let params = {
    schoolId:this.assingnmentSchoolId,
    stuName:this.searchForm.stuName,
    idCard:this.searchForm.idCard,
    agmtSchool: this.agmtSchool,
    sourceType:this.sourceType,
  }

    this.$download(
        `${pref}${this.prefixDeptCode}/api/assignment/downloadEndList`,
        params,
        // "xls",
        // "初中统计.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
},
    handleCurrentPageChange(selectedPageNo) {
      this.searchForm.pageNum = selectedPageNo;
      this.find();
    },
    handleSearch() {
      this.searchForm.pageNum = 1;
      this.find();
      this.currentPage = 1;
    },
    // 列表-获取全部学校
    find() {
      assignmentEndListApi(
        this.assingnmentSchoolId,
        this.searchForm.status = '',
        this.searchForm.pageNum,
        this.agmtSchool,
        this.sourceType,
        this.searchForm.stuName,
        this.searchForm.idCard,
        this.prefixDeptCode
      ).then(res => {
        console.log(res)
        if (res.code == "200") {
          this.tableData = res.result.records;
          console.log(res.result)
          this.tableData.forEach(data =>{
            if(data.firstSchoolStatus === 'checking'){
              
                this.isAssign = true;
            }else{
                this.isAssign =false;
            }
          })
          this.total = res.result.total*1;
          this.pageSize = res.result.totalPage;
        }
      });
    }
  }
};
</script>

<style scoped>
.selct {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px; /* 控制元素之间的间距 */
}

.selct .el-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

.selct .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
}

.selct .el-button--success {
  background-color: #67C23A;
  border-color: #67C23A;
}

.selct .inputc {
  width: 200px;
  height: 38px;
}

.selct .inputc .el-input__inner {
  height: 100%;
  font-size: 14px;
  padding: 6px 12px;
}
</style>
