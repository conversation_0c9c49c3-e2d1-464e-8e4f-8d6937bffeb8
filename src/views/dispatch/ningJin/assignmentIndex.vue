<template>
  <div class="index flexd" style="display: flex;justify-content: space-between">
<!--    <div class="w100">-->
<!--      <img src="../../../assets/img/paiweibg.png"  style="width:1000px"/>-->
<!--    </div>-->
    <div class="w100 flexd center ">
      <div class="oprationBox">
        <div>
<!--          <el-button size="large" :disabled="!editaBle" type="danger" @click="jump(2)">民办小学电脑派位</el-button>-->
        </div>
        <div>
          <el-button size="large" :disabled="!editaBle" type="danger" @click="jump(3)">民办初中电脑派位</el-button>
        </div>
      </div>
    </div>
    <el-dialog
        title="电脑派位流程及规则说明"
        :visible.sync="dialogVisible"
        class="mimi"
        width="50%"
        :before-close="()=>{dialogVisible = false}">
      <div></div>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" size="mini" @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

// import {assignmentGetPaiweiStatusApi} from "../api/api";
import {getPrivateEnrollTime} from "@/api/qingHePaiWei"
export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      dialogVisible: false,
      editaBle: true,
      from:{
      key: '',
     },
    }
  },
  created() {

    // assignmentGetPaiweiStatusApi().then(res => {
    //   if (!res) {
    //     this.editaBle = false;
    //     return
    //   }
    //   if (res.code == "200") {
    //     // this.step1=res.result.localFirstCount;
    //     // this.step2=res.result.localSecCount;
    //     // this.step3=res.result.localThrCount;

    //   } else {

    //     // this.$message.error(res.msg);
    //     return false;
    //   }
    // }).catch((err) => {

    // });
  },
  methods: {
    jump(e) {
      this.from.key = 2;
      getPrivateEnrollTime(this.from,this.prefixDeptCode).then(res => {
        if (res) {
          this.$router.push('/dispatch/primaryPwResultNJ?type=' + e)
        }
      });
     
    }
  }
}
</script>
<style lang="less" scoped>
.index {
  padding: 48px;

  /deep/ .mimi {
    .el-dialog__body {
      padding: 10px 0;
    }

    .el-dialog__header {
      padding: 15px 20px 10px;
    }
  }

  .oprationBox {
    button {
      margin-bottom: 48px;
      width: 230px;
      height: 60px;
      font-size: 16px;
    }
  }
}
</style>