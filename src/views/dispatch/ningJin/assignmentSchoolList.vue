<template>
  <div class="school">
    <el-breadcrumb separator="/" style="font-size: 16px">
      <el-breadcrumb-item>首页</el-breadcrumb-item>
      <el-breadcrumb-item>{{ title }}</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="selct" style="margin: 10px 0;">
      <el-button type="primary" @click="returnPage" icon="el-icon-caret-left">返回</el-button>
    </div>

    <div class="paiw">
      <el-card class="box-card" v-for="(school, index) in schoolList" :key="index">
        <div slot="header" class="clearfix">
          <div class="flexd">
            <div class="w100"><span>{{ school.schoolName }}</span></div>
            <div class="w100 right">
              <el-tag type="info"
                      v-if="(school.currentQuantity <= school.planQuantity&&school.planQuantity!=0) || school.planQuantity <= 0">
                无需派位
              </el-tag>
              <el-tag type="warning"
                      v-if="(school.currentQuantity > school.planQuantity)&&school.currentQuantity!=0&&school.planQuantity!=0">
                可派位
              </el-tag>
              <!-- <el-tag type="danger"  v-if="school.planQuantity==0&&school.currentQuantity">本校不招生</el-tag> -->
            </div>
          </div>
        </div>
        <div class="flexd text item">
          <div class="w100">
            <div><span class="span" style="margin-bottom: 5px;">招生计划人数：{{ school.planQuantity }}人</span></div>
            <div><span class="span" style="margin-bottom: 5px;">学校报名人数：{{ school.currentQuantity }}人</span></div>
          </div>
          <div class="w100 flexd right" style="height: 42px;
    align-items: flex-end;">
            <el-button
                class="button"
                type="primary"
                size="mini"
                v-if="school.status == 'N'&&school.currentQuantity!=0&&school.planQuantity!=0"
                @click="doAssignment(school.schoolId, school.schoolName)"
            >进行派位
            </el-button>
<!--            <el-button-->
<!--                v-if="(school.currentQuantity <= school.planQuantity)&&(school.currentQuantity!=0&&school.planQuantity!=0)"-->
<!--                class="button"-->
<!--                size="mini"-->
<!--                type="primary"-->
<!--                @click="autoPass(school.schoolId,agmtSchool)"-->
<!--            >直接录取-->
<!--            </el-button>-->
            <el-button
                class="button"
                size="mini"
                type="success"

                @click="endList(school.schoolId, school.schoolName)"
            >录取结果
            </el-button>

          </div>
        </div>
      </el-card>
    </div>
  </div>

</template>
<script>
// import {assignmentAutoPassApi, assignmentEnrollTimeIsEndApi, assignmentSchoolListApi} from "../api/api";
import {getPrivatePawWeiSchoolList,assignmentAutoPassApi,getPrivateEnrollTime} from "@/api/qingHePaiWei"

export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      schoolList: [],
      isEnd: "",
      agmtSchool: '',
      sourceType: '',
      generateNumForm: {
        schoolId: '',
        agmtSchool: '',
        sourceType: ''
      },
     from:{
      key: '',
     },
      title: '电脑派位管理'
    };
  },
  async created() {
    let {type} = this.$route.query;
    this.sourceType = type;
    this.from.key = type;
    if (type == 1) {
      this.title = "幼儿园电脑派位管理"
    } else if (type == 2) {
      this.title = "民办小学电脑派位管理"
    } else if (type == 3) {
      this.title = "民办初中电脑派位管理"
    }
    await this.find(type);
    await this.enrollTimeLoad();
  },
  methods: {
    // 列表-获取全部
    find(type) {
      this.from.key = type;
      getPrivatePawWeiSchoolList(this.from,this.prefixDeptCode).then(res => {
        if (res) {
          this.schoolList = res.result;
          console.log(this.schoolList);
        }
      });
    },
    returnPage() {
      this.$router.push({path: "/dispatch/pwResultNJ"});
    },
    doAssignment(id, name) {
      if (this.isEnd) {
        localStorage.setItem("assingnmentSchoolId", id);
        localStorage.setItem("assingnmentSchoolName", name);
        this.$router.push({path: "/dispatch/PwAssignmentNJ", query: {agmtSchool: this.agmtSchool, sourceType: this.sourceType}});
      } else {
        this.$alert("报名时间未结束。");
      }
    },
    autoPass(schoolId) {
      this.$confirm('确认直接录取？').then((e) => {
        console.log("调用成功")
        if (this.isEnd) {
          console.log("调用成功----111")
          this.generateNumForm.schoolId = schoolId;
          this.generateNumForm.agmtSchool = this.agmtSchool;
          this.generateNumForm.sourceType = this.sourceType;
          assignmentAutoPassApi(this.generateNumForm, this.prefixDeptCode).then(res => {
            if (res.code == "200") {
              this.$alert("直接录取成功。");
              this.find(this.sourceType)
            } else {
              this.$message.error(res.msg);
              return false;
            }
          });
        }
      })

    },
    endList(id, name) {
      if (this.isEnd) {
        localStorage.setItem("assingnmentSchoolId", id);
        localStorage.setItem("assingnmentSchoolName", name);
        this.$router.push("/dispatch/primaryPwSchoolListNJ?agmtSchool=" + this.agmtSchool + '&sourceType=' + this.sourceType);
      } else {
        this.$alert("报名时间未结束。");
      }
    },
    enrollTimeLoad() {
      this.from.key = 2;
      getPrivateEnrollTime(this.from,this.prefixDeptCode).then(res => {
        if (res) {
          console.log(res.endTime);
          this.isEnd = res.endTime;
        }else {
          this.$router.push("/dispatch/assignmentIndex")
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.school {
  width: 100%;
}

.span {
  display: inline-block;
  padding-right: 24px;
}

.paiw {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .box-card {
    width: 49%;
  }
}

.text {
  margin-top: 10px;
}
</style>
