<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>首页</el-breadcrumb-item>
      <el-breadcrumb-item>电脑派位管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ assingnmentSchoolName }}</el-breadcrumb-item>
      <el-breadcrumb-item>电脑派位录取结果</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="selct">
      <el-button type="primary" @click="returnPage" icon="el-icon-caret-left">返回</el-button>
      <el-button type="primary" @click="button1Click()" icon="el-icon-plus" :loading="exportLoading">导出</el-button>
      <el-input class="inputc" v-model="search.keywords" clearable placeholder="学生姓名/身份证号"></el-input>
<!--      <el-input class="inputc" v-model="searchForm.idCard" placeholder="身份证号"></el-input>-->
      <el-button class="butto" @click="handleSearch" type="success" icon="el-icon-search">查询</el-button>
    </div>

    <div class="tabs">
      <el-table :data="tableData" border stripe >
        <el-table-column
            align="center"
            label="学生姓名"
            prop="studentName"
        ></el-table-column>
        <el-table-column
            align="center"
            label="身份证号"
            prop="studentIdCardNumber"
        ></el-table-column>
        <el-table-column
            align="center"
            label="性别"
            prop="studentIdCardNumber"
        >
          <template slot-scope="scope">
            {{ scope.row.studentGender == 1 ? "男" :scope.row.studentGender == 2? "女" :''}}
          </template>
        </el-table-column>
        <el-table-column
            align="center"
            label="录取成功志愿"
            prop="severalVolunteer"
        >
          <template slot-scope="scope">
            {{ scope.row.severalVolunteer == 1 ? "第一志愿" :
              scope.row.severalVolunteer == 2?'第二志愿':
                  scope.row.severalVolunteer == 3?'第三志愿':
                      scope.row.severalVolunteer == 4?'第四志愿':
                          scope.row.severalVolunteer == 5?'第五志愿': '' }}
          </template>
        </el-table-column>
        <el-table-column
            align="center"
            label="录取学校"
            prop="volunteerSchoolName"
        ></el-table-column>

      </el-table>
    </div>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="pageSizes"
      >
        <!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
  </div>
</template>

<script>
// import { assignmentEndListApi, assignmentDownloadEndListApi } from "../api/api";
import {huoQuChengGongPaiWei} from "@/api/quXianPW";
import {pref} from "@/utils/common";
export default {
  data() {
    return {
      exportLoading:false,
      prefixDeptCode: this.$store.getters.deptCode,
      assingnmentSchoolId: "",
      assingnmentSchoolName: "",
      pageSize: 10,
      currentPage: 1,
      total: 0,
        agmtSchool:'',
       sourceType:'',
      pageSizes:[10,300,500,800],
      search: {
        pageNumber:1,
        pageSize: 10,
        school: "",
        idCard: "",
        stuName: "",
        pageNum: ""
      },
      tableData: [],
      status: [
        {
          value: "success",
          desc: "派位成功"
        },
        {
          value: "fail",
          desc: "派位失败"
        }
      ]
    };
  },
  async created() {
    this.search.schoolId = localStorage.getItem("assingnmentSchoolId");
    this.assingnmentSchoolName = localStorage.getItem("assingnmentSchoolName");
    this.search.pageNumber = 1;
    this.sourceType=this.$route.query.sourceType;
    this.search.step= this.$route.query.agmtSchool=='first'?'1':
        this.$route.query.agmtSchool=='second'?'2':
            this.$route.query.agmtSchool=='third'?'3':
                this.$route.query.agmtSchool=='fourth'?'4':
                    this.$route.query.agmtSchool=='fifth'?'5':'';
    this.search.stuName="";
    this.search.idCard="";
    await this.find();
  },
  methods: {
    // 列表-对“性别”进行格式化
    formatterGender(row, column) {
      switch (row.gender) {
        case "M":
          return "男";
          break;
        case "F":
          return "女";
          break;
        default:
          return "未知";
      }
    },
    returnPage() {
      // if (window.history.length <= 1) {
      //   this.$router.push({ path: "/zh-CN/home" });
      //   return false;
      // } else {
        this.$router.go(-1);
      // }
    },
    formatDate(row, column) {
      // 获取单元格数据
      let data = row[column.property];
      let dt = new Date(data);
      return (
        dt.getFullYear() +
        "-" +
        (dt.getMonth() + 1) +
        "-" +
        dt.getDate() +
        " " +
        dt.getHours() +
        ":" +
        dt.getMinutes() +
        ":" +
        dt.getSeconds()
      );
    },
    // 列表-对“状态”进行格式化
    formatterStatus(row, column) {
      switch (row.status) {
        case "checking":
          return "等待派位";
          break;
        case "passed":
          return "派位成功";
          break;
        case "notPass":
          return "派位失败";
          break;
        default: // 由于该结果恒定，顾与前端商量写死即可
          return "派位成功";
      }
    },
    // 导出报名信息
    button1Click() {
      this.$message.info('正在导出，数据量较大，请耐心等待')
      this.exportLoading = true;
      let params = this.search;
      this.$download(
          `${pref+this.prefixDeptCode}/biz/TbPaiWeiSchoolSettingController/exportPwData`,
          params,
          "xls",
          "派位成功学生列表.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("下载成功");
      });
    },
    handleSearch(){
      this.search.pageNumber=1
    this.find()
    },
    // 列表-获取全部学校
    find() {
      huoQuChengGongPaiWei(this.search,this.prefixDeptCode).then(res=>{
        this.tableData = res.records
        this.total =Number(res.total)
      })
      // assignmentEndListApi(
      //   this.assingnmentSchoolId,
      //   this.searchForm.status = '',
      //   this.searchForm.pageNum,
      //   this.agmtSchool,
      //   this.sourceType,
      //   this.searchForm.stuName,
      //   this.searchForm.idCard
      // ).then(res => {
      //   if (res.code == "200") {
      //     this.tableData = res.result.content;
      //     this.total = res.result.totalSize*1;
      //     this.pageSize = res.result.pageSize;
      //   }
      // });
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.find()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.find()
    },
  }
};
</script>
<style>
@import "../../../../public/index.css";
</style>
<style lang="scss" scoped>
.selct {
  display: flex;
  padding: 10px 0 0 20px;
  box-sizing: border-box;

  .el-button {
    padding: 0 10px;
  }

  .inputc {
    width: 180px;
    margin-left: 10px;
  }

  .butto {
    margin-left: 10px;
  }
}

.pagination {
  display: flex;
  float: right;
  line-height: 32px;
  padding: 0 20px;
}
.tabs {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px;
}
</style>
