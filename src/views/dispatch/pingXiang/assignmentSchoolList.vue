<template>
  <div class="school">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>首页</el-breadcrumb-item>
      <el-breadcrumb-item>{{title}}</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="selct">
      <el-button type="primary" @click="returnPage" icon="el-icon-caret-left">返回</el-button>
    </div>

    <div class="paiw">
      <el-card class="box-card" v-for="(school, index) in schoolList" :key="index">
        <div slot="header" class="clearfix">
         <div class="flexd">
           <div class="w100"> <span>{{ school.lotterySchoolName }}</span></div>
           <div class="w100 right">
          <el-tag type="warning" v-if="school.eligiblePendingStudents >0">可派位</el-tag>
           <el-tag type="success"  v-if="school.eligiblePendingStudents<=0">派位完成</el-tag>
           </div>
         </div>
        </div>
        <div class="flexd text item">
          <div class="w100">
            <div>  <span class="span">学校剩余学位人数：{{ school.remainingQuota }}人</span></div>
            <div>  <span class="span">待派位人数：{{ school.eligiblePendingStudents }}人</span></div>
          </div>
          <div class="w100 flexd right" style="    height: 42px;
    align-items: flex-end;">
                <el-button v
            class="button"
            type="primary"
            size="mini"
                           v-if="school.remainingQuota> 0 && school.eligiblePendingStudents>0"
            @click="doAssignment(school.lotterySchoolId, school.lotterySchoolName)"
          >进行派位</el-button>
                <el-button
                      class="button"
                      size="mini"
                      type="success"

                      @click="endList(school.lotterySchoolId, school.lotterySchoolName)"
              >录取结果</el-button>

          </div>
        </div>
      </el-card>
    </div>
  </div>

</template>
<script>
import {getPWSchoolList} from '@/api/quXianPW'
export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      schoolList: [],
      isEnd: "",
      agmtSchool:'',
      step:'',
      sourceType:'',
        generateNumForm:{
          schoolId:'',
          agmtSchool:'',
          sourceType:''
      },
      title:'电脑派位管理'
    };
  },
  async created() {
    let {step,type}=this.$route.query;
    this.agmtSchool=['','first','second'][step];
    this.sourceType=type;
    this.step=step;
    this.title=this.title+`--第${step}志愿`
    await this.find();
  },
  methods: {
    // 列表-获取全部
    find() {
      getPWSchoolList({step:this.step},this.prefixDeptCode).then(res=>{
        this.schoolList = res
      })
    },
    returnPage() {
      if (window.history.length <= 1) {
    this.$router.push({ path: "/zh-CN/home" });
    return false;
  } else {
    this.$router.go(-1);
  }
    },
    doAssignment(id, name) {
        localStorage.setItem("assingnmentSchoolId", id);
        localStorage.setItem("assingnmentSchoolName", name);
        this.$router.push({path:"/dispatch/assignment_pingXiang",query:{agmtSchool:this.agmtSchool,sourceType:this.sourceType}});
    },
    endList(id, name) {
        localStorage.setItem("assingnmentSchoolId", id);
        localStorage.setItem("assingnmentSchoolName", name);
        this.$router.push("/dispatch/assignmentEndList?agmtSchool="+this.agmtSchool+'&sourceType='+this.sourceType);
    },
  }
};
</script>
<style>
@import "../../../../public/index.css";
</style>
<style lang="less" scoped>
.school {
  width: 100%;
}
.span {
  display: inline-block;
  padding-right: 24px;
}
.paiw {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .box-card {
    width: 49%;
  }
}
.text {
  margin-top: 10px;
}
</style>
