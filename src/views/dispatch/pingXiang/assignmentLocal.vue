<template>
    <div class="container flexd center">
        <div class="inners">
            <h1>初中电脑派位</h1>
            <div class="flexd selectBox">
                <div class="center">
                    <div class="cubes">
                        <div class="imgBox"></div>
                        <div>第一志愿电脑派位</div>
                    </div>
                    <div class="flexd center btns">
                        <el-button @click="paiwei(1)" type="danger" class="paiweiBtn">进行电脑派位
                        </el-button>
                    </div>
                </div>
                <div class="flexd center ar"><i class="el-icon-d-arrow-right"></i></div>
                <div class="center">
                    <div class="cubes">
                        <div class="imgBox"></div>
                        <div>第二志愿电脑派位</div>
                    </div>
                    <div class="flexd center btns">
                        <el-button @click="paiwei(2)" type="danger" class="paiweiBtn">进行电脑派位
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
              prefixDeptCode: this.$store.getters.deptCode,
                step1: false,
                step2: false,
                step3: false,
                step4: false,
                step5: false,
                step6: false,
                stepLevel: 0,
                loadingShow: true,
                isEnd: "",
                type: 'local'//outland
            }
        },
        async created() {
            let type = this.$route.query.type;
            if (type) {
                this.type = type
            }
        },
        methods: {
            // 接口 获取 step1 ，2 ，3 的开启状态字段。用于设置对应值
            // 复制一份用于外地派位
            paiwei(e) {
                this.$router.push('/dispatch/assignmentSchoolList_pingXiang?type=' + this.type + '&step=' + e)
            }
        }
    }
</script>
<style>
@import "../../../../public/index.css";
</style>
<style lang="less" scoped>

    .container {
      //.flexd {
      //  display: flex;
      //  align-items: center;
      //}
        .selectBox {
            padding-top: 36px;
            .btns {
                margin-top: 24px;
            }
            .ar {
                font-size: 45px;
                padding: 24px;
                margin-bottom: 72px;
                i {
                    font-weight: bold;
                }
            }
            .cubes {
                border-radius: 8px;
                padding: 12px 18px;
                background: rgb(0, 255, 255);

                .imgBox {
                    width: 180px;
                    height: 180px;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: cover;
                    margin-bottom: 12px;
                    background-image: url('../../../assets/img/buok.png');

                }
                &.Acts {
                    .imgBox {
                        background-image: url('../../../assets/img/ok.png');
                    }

                }
                &.finished {
                    .paiweiBtn {
                        background: rgba(0, 0, 0, .8);
                        border: none;
                    }

                }
            }
        }
    }
</style>
