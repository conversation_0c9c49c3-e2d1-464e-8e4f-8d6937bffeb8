<template>
	<div class="pw-result">
		<div class="search-wrap">
			<el-form :model="search" :inline="true">
			  <el-form-item>
			    <el-input v-model="search.keywords" size="small" clearable placeholder="学生姓名/身份证号"></el-input>
			  </el-form-item>
			  <el-form-item>
			    <el-select
						size="small"
						v-model="search.gender"
						placeholder="选择性别"
						class="sd-w-130"
						clearable
			    >
			      <el-option label="男" :value="1"></el-option>
			      <el-option label="女" :value="2"></el-option>
			    </el-select>
			  </el-form-item>
			  <el-form-item>
			    <el-button
			        size="small"
			        type="primary"
			        icon="el-icon-search"
			        @click="getTableData"
			    ></el-button>
					<el-button
						size="small"
						type="success"
						@click="exportRes"
            :loading="exportLoading"
					>导出</el-button>
					<el-button
						size="small"
						@click="goBack"
					>返回</el-button>
			  </el-form-item>
			</el-form>
			<p class="school-txt">{{ operateSchool.schoolName }}</p>
		</div>
		
		<el-table :data="tableData" border stripe >
			<el-table-column
				type="index"
				align="center"
				label="序号"
				width="80"
			></el-table-column>
		  <el-table-column
		      align="center"
		      label="学生姓名"
		      prop="studentName"
		  ></el-table-column>
		  <el-table-column
		      align="center"
		      label="身份证号"
		      prop="idCard"
		  ></el-table-column>
		  <el-table-column
		      align="center"
		      label="性别"
		      prop="gender"
		  >
		    <template slot-scope="scope">
		      {{ scope.row.gender == 1 ? "男" :scope.row.gender == 2 ? "女" :''}}
		    </template>
		  </el-table-column>
			<el-table-column prop="status" label="状态" align="center">
				<template slot-scope="{ row }">
					<span>{{ [ '', '待派位', '派位成功', '派位失败' ][ row.status ] }}</span>
				</template>
			</el-table-column>
		</el-table>
		<div class="page-container" v-if="total > 0">
		  <el-pagination
		      background
		      @size-change="handleSizeChange"
		      @current-change="handleCurrentChange"
		      :current-page.sync="search.pageNumber"
		      layout="total, prev, pager, next, sizes"
		      :total="total"
		      :page-sizes="pageSizes"
		  >
		  </el-pagination>
		</div>
	</div>
</template>

<script>
import { pref } from "@/utils/common"
import { pwResultBySchool } from '@/api/yongNianPw.js'
export default {
	data() {
		return {
			prefixDeptCode: this.$store.getters.deptCode,
			tableData:[],
			pageSizes:[10,300,500,800],
			total:0,
			operateSchool: JSON.parse(sessionStorage.getItem('ynPWSchool')),
      exportLoading: false,
			search: {
			  pageNumber: 1,
				pageSize: 10,
				keywords: '',
				assignId: '',
				gender: ''
			}
		}
	},
	created() {
		this.search.assignId = this.operateSchool.id
		this.getTableData()
	},
	methods: {
		// 列表
		getTableData() {
		  pwResultBySchool(this.search, this.prefixDeptCode).then(res=>{
		    this.tableData = res.records
		    this.total = Number(res.total)
		  })
		},
		// 页面个数修改
		handleSizeChange(size) {
		  this.search.pageSize = size
		  this.search.pageNumber = 1
		  this.getTableData()
		},
		// 当前页
		handleCurrentChange(page) {
		  this.search.pageNumber = page
		  this.getTableData()
		},
		// 导出数据
		exportRes() {
        this.$message.info('正在导出，数据量较大，请耐心等待')
      this.exportLoading = true;
			let params = this.search
			this.$download(
			    `${pref + this.prefixDeptCode}/biz/assign/exportAssignResult`,
			    params,
			    "xls",
			    `${ this.operateSchool.schoolName } - 派位成功学生列表.xls`
			).then((res) => {
        this.exportLoading = false;
			  this.$message.success("下载成功")
			})
		},
		// 返回
		goBack() {
			this.$router.go(-1)
		}
	}
}
</script>

<style scoped>
.search-wrap {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
</style>