<template>
	<div>
		<el-breadcrumb separator="/">
			<el-breadcrumb-item>首页</el-breadcrumb-item>
			<el-breadcrumb-item>电脑派位管理</el-breadcrumb-item>
		</el-breadcrumb>
		<div class="central">
			<span class="central_center">{{ operateSchool.schoolName }} - 电脑派位录取工作</span>
		</div>
		<el-row :gutter="10">
			<el-col :span="12">
				<div class="grid-content bg-purple">

					<el-card class="box-card">
						<div slot="header" class="clearfix">
							<span>等待派位（{{studentList.length}}）人</span>
						</div>
						<el-table :data="studentList" border style="width: 100%" max-height="500" height="500" v-loading="loading1">
							<el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
							<el-table-column prop="studentName" label="学生姓名" width="120" align="center"></el-table-column>
							<el-table-column prop="gender" :formatter="formatterGender" label="性别" width="50" align="center"></el-table-column>
							<el-table-column prop="idCard" label="身份证" align="center"></el-table-column>
						</el-table>
					</el-card>
				</div>
			</el-col>
			<el-col :span="12">
				<div class="grid-content bg-purple-light">
					<el-card class="box-card">
						<div slot="header" class="clearfix">
							<span>派位完成（{{endStudentList.length}}）</span>
						</div>
						<el-table v-loading="loading2" :data="endStudentList" border style="width: 100%" max-height="500"
							height="500" header-align="center" ref="endStudentList">
							<el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
							<el-table-column prop="studentName" label="学生姓名" width="120" align="center"></el-table-column>
							<el-table-column prop="gender" :formatter="formatterGender" label="性别" width="50" align="center"></el-table-column>
							<el-table-column prop="idCard" label="身份证" align="center"></el-table-column>
							<el-table-column prop="relatedNum" label="多胞胎" align="center"></el-table-column>
							<el-table-column prop="status" label="状态" width="120" align="center">
								<template slot-scope="{ row }">
									<span>{{ [ '', '待派位', '派位成功', '派位失败' ][ row.status ] }}</span>
								</template>
							</el-table-column>
						</el-table>
					</el-card>
				</div>
			</el-col>
		</el-row>
		<div class="rowbtn" v-show="!theEnd">
			<el-button @click="begin()" :disabled="generateNumDisabled">随机打乱顺序</el-button>
			<el-button @click="stop()" :disabled="sortListDisabled">停止打乱顺序</el-button>
			<el-button type="primary" @click="beginAssignment()" :disabled="doAssignmentDisabled">开始派位</el-button>
			<el-button type="danger" @click="stop1()" :disabled="endAssignmentDisabled">结束派位</el-button>
			<el-button type="danger" @click="confirmEnd()" v-show="confirmEndVisable">确认派位完成</el-button>
		</div>
	</div>
</template>
<script>
	import {
		getPwStuBySchool,
		endPw,
		confirmEndPw
	} from "@/api/yongNianPw"
	import ModalMixin from "@/mixins/ModalMixin";
	export default {
		mixins: [ModalMixin],
		data() {
			return {
				reject: false,
				loading2: false,
				loading1: false,
				modal: {
					reject: false,
				},
				errorMessages: [],
				prefixDeptCode: this.$store.getters.deptCode,
				generateNumDisabled: false,
				sortListDisabled: true,
				generateStartDisabled: false,
				generateIntervalDisabled: false,
				doAssignmentDisabled: true,
				endAssignmentDisabled: true,
				assingnmentSchoolId: "",
				assingnmentSchoolName: "",
				assignmentInfo: "",
				startNum: "",
				interval: "",
				confirmEndVisable: false,
				theEnd: false,
				studentList: [],
				endStudentList: [],
				doneStudentList: [],
				successStudentList: [],
				houBuStudentList: [],
				localStudentList: [],
				failStudentList: [],
				planQuantity: 0,
				operateSchool: JSON.parse(sessionStorage.getItem('ynPWSchool'))
			};
		},
		async created() {
			this.assingnmentSchoolId = ''
			this.assingnmentSchoolName = localStorage.getItem("assingnmentSchoolName")
			await this.loadStudentList()
		},
		methods: {
			begin() {
				this.generateNumDisabled = true;
				this.sortListDisabled = false;
				let arr = this.studentList;
				this.interval = setInterval(() => {
					arr.sort(function() {
						return 0.5 - Math.random();
					});
				}, 100);
			},
			beginAssignment() {
				this.$confirm('是否确认派位开始', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.generateNumDisabled = true;
						this.sortListDisabled = true;
						this.doAssignmentDisabled = true;
						this.endAssignmentDisabled = false;
						let arr = this.studentList;
						this.interval = setInterval(() => {
							arr.sort(function() {
								return 0.5 - Math.random();
							});
						}, 100);
					})
					.catch(() => {

					})
			},
			stop() {
				this.generateNumDisabled = false;
				this.sortListDisabled = true;
				this.doAssignmentDisabled = false;
				clearInterval(this.interval);
			},
			stop1() {
				this.$confirm('是否确认派位结束', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.$message.info('数据量较大时，请耐心等待')
						this.loading2 = true;
						endPw({
							key: this.operateSchool.id
						}, this.prefixDeptCode).then(res => {
							this.loading2 = false;
							this.endAssignmentDisabled = true;
							this.confirmEndVisable = true;
							this.endStudentList = res
							this.studentList = []
						})
					})
					.catch(() => {})
			},
			// 列表-对“性别”进行格式化
			formatterGender(row, column) {
				switch (row.gender) {
					case 1:
						return "男";
						break;
					case 2:
						return "女";
						break;
					default:
						return "未知";
				}
			},
			doAssignment() {
				this.$confirm('是否确认派位结束', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.confirmEndVisable = false;
						this.theEnd = false;
					})
					.catch(() => {});
			},
			confirmEnd() {
				this.theEnd = false;
				this.$confirm('是否确认派位完成', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$router.go(-1)
				}).catch(() => {})
			},
			loadStudentList() {
				this.loading1 = true
				this.$message.info('数据量较大时，请耐心等待')
				getPwStuBySchool({
					key: this.operateSchool.id
				}, this.prefixDeptCode).then(res => {
					this.studentList = res;
					this.loading1 = false
				});
			}
		}
	};
</script>
<style>
	@import "../../../../public/index.css";
</style>
<style lang="less" scoped>
	.central {
		height: 80px;
		line-height: 80px;
		position: relative;

		.central_center {
			position: absolute;
			display: block;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);

			color: #01aaed;
			font-size: 24px;
		}

		.central_right {
			float: right;
			display: flex;
			justify-content: space-evenly;
			width: 400px;

			.central_right_de {
				display: flex;
				align-items: center;

				.boxB {
					border: 1px solid #909399;
					height: 20px;
					color: #01aaed;
					text-align: center;
					width: 50px;
					line-height: 20px;
				}
			}
		}
	}

	.rowbtn {
		display: flex;
		justify-content: space-evenly;
		margin-top: 20px;
		.el-button {
			width: 13%;
			border-radius: 8px;
		}
	}
</style>