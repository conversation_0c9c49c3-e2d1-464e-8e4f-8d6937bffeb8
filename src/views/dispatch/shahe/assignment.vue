<template>
	<div>
		<el-breadcrumb separator="/">
			<el-breadcrumb-item>首页</el-breadcrumb-item>
			<el-breadcrumb-item>电脑派位管理</el-breadcrumb-item>
		</el-breadcrumb>

		<div class="central">
			<!--          {{ assingnmentSchoolName }}-->
			<span class="central_center">电脑派位录取工作</span>
		</div>
		<div>
			<el-button type="primary" @click="openImport">导入</el-button>
		</div>
		<el-row :gutter="10">
			<el-col :span="12">
				<div class="grid-content bg-purple">

					<el-card class="box-card">
						<div slot="header" class="clearfix">
							<span>等待派位（{{studentList.length}}）人</span>
						</div>
						<u-table :data="studentList" style="width: 100%;" height="600px"  use-virtual
                     showBodyOverflow="title" showHeaderOverflow="title"  :header-cell-style="{ textAlign: 'center' }"
                     :cell-style="{ textAlign: 'center' }" border row-height="33"  v-loading="loading1">
							<!--<u-table-column prop="id" label="报名ID" width="120" align="center"></u-table-column>-->
							<u-table-column prop="studentName" label="学生姓名" width="120" align="center"></u-table-column>
							<u-table-column prop="studentGender" :formatter="formatterGender" label="性别" width="50"
								align="center"></u-table-column>
							<u-table-column prop="studentIdCardNumber" label="身份证" align="center"></u-table-column>
						</u-table>
					</el-card>
				</div>
			</el-col>
			<el-col :span="12">
				<div class="grid-content bg-purple-light">
					<el-card class="box-card">
						<div slot="header" class="clearfix">
							<span>派位完成（{{endStudentList.length}}）</span>
						</div>
						<u-table v-loading="loading2" :data="endStudentList" style="width: 100%;" height="600px"  use-virtual
                      showBodyOverflow="title" showHeaderOverflow="title"  :header-cell-style="{ textAlign: 'center' }"
                      :cell-style="{ textAlign: 'center' }"  row-height="33"  border>
							<!--<u-table-column prop="assignmentNum" label="报名号" width="120"-->
							<!--align="center"></u-table-column>-->
							<u-table-column prop="studentName" label="学生姓名" width="120" align="center"></u-table-column>
							<u-table-column prop="studentGender" label="性别" width="50" align="center"></u-table-column>
							<u-table-column prop="studentIdCardNumber" label="身份证" align="center"></u-table-column>
							<u-table-column prop="locationEnrollSchoolName" label="派位学校" align="center"></u-table-column>
							<u-table-column label="状态" width="120" align="center">
								<span>派位成功</span>
							</u-table-column>
						</u-table>
					</el-card>
				</div>
			</el-col>
		</el-row>
		<div class="rowbtn" v-show="!theEnd">
			<el-button @click="begin()" :disabled="generateNumDisabled">随机打乱顺序</el-button>
			<el-button @click="stop()" :disabled="sortListDisabled">停止打乱顺序</el-button>
			<el-button type="primary" @click="beginAssignment()" :disabled="doAssignmentDisabled">开始派位</el-button>
			<el-button type="danger" @click="stop1()" :disabled="endAssignmentDisabled">结束派位</el-button>
			<el-button type="danger" @click="confirmEnd()" v-show="confirmEndVisable">确认派位完成</el-button>
		</div>
		<!-- 导入 -->
		<el-dialog :visible.sync="modal.import" center title="导入信息" :close-on-click-modal="false" width="500px">
			<el-upload action="#" :limit="upload.limit" :auto-upload="upload.auto" :file-list="upload.list"
				:on-change="changeFile" :on-remove="removeFile">
				<el-button size="small" type="primary">点击上传</el-button>
			</el-upload>
			<el-form ref="form" label-width="80px">
				<el-form-item label="错误信息" v-if="errorMessages.length > 0">
					<div style="max-height: 300px; overflow-y: auto">
						<div v-for="(item, index) in errorMessages" :key="index">
							<div class="error-desc-text">
								{{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
							</div>
						</div>
					</div>
				</el-form-item>
			</el-form>

			<div slot="footer">
				<el-button size="small" @click="switchModal('import', false)">取消</el-button>
				<el-button size="small" type="primary" :disabled="upload.list.length == 0" @click="confirmUpload">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
	// import {
	//     assignmentStudentListApi,
	//     assignmentInitAssignmentApi,
	//     assignmentGenerateNumApi,
	//     assignmentSortListApi,
	//     assignmentGenerateStartApi,
	//     assignmentGenerateIntervalApi,
	//     assignmentDoApi,
	//     assignmentConfirmationApi,
	//     schoolGetApi
	// } from "../api/api";
	import {
		daiPaiWeiStudent,
		paiWeiChengGongStudent,
		queRenPaiWei
	} from "@/api/quXianPW"
	import ModalMixin from "@/mixins/ModalMixin";
	import {
		daoRupaiWei
	} from "@/api/linZhangPaiWei";
	export default {
		mixins: [ModalMixin],
		data() {
			return {
        e:this.$route.query.e,
				// import1: true,
				reject: false,
				loading2: false,
				loading1: false,
				modal: {
					import: false,
					reject: false,
				},
				upload: {
					limit: 1,
					auto: false,
					list: [],
				},
				errorMessages: [],
				prefixDeptCode: this.$store.getters.deptCode,
				generateNumDisabled: false,
				sortListDisabled: true,
				generateStartDisabled: false,
				generateIntervalDisabled: false,
				doAssignmentDisabled: true,
				endAssignmentDisabled: true,
				assingnmentSchoolId: "",
				assingnmentSchoolName: "",
				assignmentInfo: "",
				startNum: "",
				interval: "",
				confirmEndVisable: false,
				theEnd: false,
				studentList: [],
				endStudentList: [],
				doneStudentList: [],
				successStudentList: [],
				houBuStudentList: [],
				localStudentList: [],
				failStudentList: [],
				agmtSchool: '',
				sourceType: '',
				planQuantity: 0

			};
		},
		async created() {
			this.assingnmentSchoolId = ''
			this.assingnmentSchoolName = localStorage.getItem("assingnmentSchoolName");
			this.sourceType = this.$route.query.sourceType;
			this.agmtSchool = '1';
			await this.initAssignment();
			await this.loadStudentList();
		},
		methods: {
			// 打开导入
			openImport() {
				this.switchModal("import", true);
				this.removeFile();
				this.errorMessages = [];
			},
			// 上传
			changeFile(file, fileList) {
				this.upload.list = [file.raw];
			},
			// 删除上传
			removeFile() {
				this.upload.list = [];
			},
			// 上传
			confirmUpload() {
				this.$message.info('正在导入，数据量较大，请耐心等待')
				let fd = new FormData();
				fd.append("file", this.upload.list[0]);
        fd.append("status", this.e);
				daoRupaiWei(fd, this.prefixDeptCode).then((res) => {
					// if(res.length>0){
					//   this.errorMessages =res;
					// }else {

					this.switchModal("import", false);
					this.$message.success("操作成功");
					this.loadStudentList();
					// }



				});
			},
			getSchoolInfo() {
				// schoolGetApi(this.assignmentInfo.schoolId).then(res => {
				//     if (res.code == "200") {
				//         const result = res.result;
				//         this.planQuantity = result.actualQuantity;
				//     }
				// });
			},
			begin() {
				this.generateNumDisabled = true;
				this.sortListDisabled = false;
				let arr = this.studentList;
				this.interval = setInterval(() => {
					arr.sort(function() {
						return 0.5 - Math.random();
					});
				}, 100);
			},
			beginAssignment() {
				this.$confirm('是否确认派位开始', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.generateNumDisabled = true;
						this.sortListDisabled = true;
						this.doAssignmentDisabled = true;
						this.endAssignmentDisabled = false;
						let arr = this.studentList;
						this.interval = setInterval(() => {
							arr.sort(function() {
								return 0.5 - Math.random();
							});
						}, 100);
					})
					.catch(() => {

					})
			},
			stop() {
				this.generateNumDisabled = false;
				this.sortListDisabled = true;
				this.doAssignmentDisabled = false;
				clearInterval(this.interval);
			},
			stop1() {
				this.$confirm('是否确认派位结束', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.$message.info('数据量较大，请耐心等待')
						this.loading2 = true;
          let fd = new FormData();
          fd.append("status", this.e);
						paiWeiChengGongStudent(fd, this.prefixDeptCode).then(res => {
							this.loading2 = false;
							this.endAssignmentDisabled = true;
							this.confirmEndVisable = true;
							this.endStudentList = res
							this.studentList = [];
						})

					})
					.catch(() => {})
				// clearInterval(this.interval);
				// this.successStudentList = [];
				// this.failStudentList = [];
				// this.houBuStudentList= [],
				// this.localStudentList= [],
				// this.failStudentList= [],
				// //过滤本地生源和外地生源
				// this.localStudentList = this.studentList.filter(student => {
				//     if(student.sourceType == 'local'){
				//         return student;
				//     }else{
				//         this.houBuStudentList.push(student);
				//     }
				// });
				//
				// let eachData = JSON.parse(JSON.stringify(this.houBuStudentList));
				//
				// //判断本地生源  多退少补。
				// if(this.localStudentList.length < this.planQuantity){
				//     eachData.forEach(outland =>{
				//         if(this.localStudentList.length < this.planQuantity){
				//             this.localStudentList.push(outland);
				//             this.houBuStudentList = this.houBuStudentList.filter(houBu => {
				//                 if(houBu.id !== outland.id){
				//                     return houBu;
				//                 }
				//             });
				//             // this.houBuStudentList.splice(this.houBuStudentList.indexOf(outland) , 1);
				//         }
				//     });
				// }else if(this.localStudentList.length > this.planQuantity){
				//     for (let i = 0; i < this.localStudentList.length - this.planQuantity; i++) {
				//         this.houBuStudentList.push(this.localStudentList[i]);
				//     }
				//     this.localStudentList.splice(0 , this.localStudentList.length - this.planQuantity);
				// }
				//
				// //将数组重新打乱顺序
				// this.localStudentList.sort(function () {
				//     return 0.5 - Math.random();
				// });
				// this.houBuStudentList.sort(function () {
				//     return 0.5 - Math.random();
				// });
				//
				// if(this.houBuStudentList.length > 0){
				//     this.houBuStudentList.filter(fail =>{
				//         fail.status = "派位失败";
				//     });
				// }
				// if(this.localStudentList.length > 0){
				//     this.localStudentList.filter(success =>{
				//         success.status = "成功";
				//     });
				// }
				//
				//
				//
				// //合并数据，前端展示
				// this.endStudentList = this.localStudentList.concat(this.houBuStudentList);
				// this.studentList = [];
			},
			// 数字位数不够，进行前补零
			PrefixZero(num, n) {
				return (Array(n).join(0) + num).slice(-n);
			},

			// 列表-对“性别”进行格式化
			formatterGender(row, column) {
				switch (row.studentGender) {
					case "1":
						return "男";
						break;
					case "0":
						return "女";
						break;
					default:
						return "未知";
				}
			},
			initAssignment() {
				// assignmentInitAssignmentApi(this.assingnmentSchoolId, this.agmtSchool, this.sourceType).then(res => {
				//     if (res.code == "200") {
				//         this.assignmentInfo = res.result;
				//         this.getSchoolInfo();
				//     } else {
				//         this.$message.error(res.msg);
				//         return false;
				//     }
				// });
			},


			doAssignment() {
				this.$confirm('是否确认派位结束', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.confirmEndVisable = false;
						this.theEnd = false;
						//     assignmentDoApi(
						//         this.assingnmentSchoolId,
						//         this.assignmentInfo.id,
						//         this.startNum,
						//         this.interval,
						//         this.agmtSchool,
						//         this.sourceType
						//     ).then(res => {
						//         if (res.code == "200") {
						//             this.generateNumDisabled = true;
						//             this.sortListDisabled = true;
						//             this.generateStartDisabled = true;
						//             this.generateIntervalDisabled = true;
						//             this.doAssignmentDisabled = true;
						//             res.result.forEach(element => {
						//                 this.studentList.forEach(sElement => {
						//                     if (sElement.assignmentNum == element.assignmentNum) {
						//                         var index = this.studentList.indexOf(sElement);
						//                         this.studentList.splice(index, 1);
						//                         return false;
						//                     }
						//                 });
						//                 this.doneStudentList.push(element);
						//             });
						//             this.confirmEndVisable = true;
						//         } else {
						//             this.$message.error(res.msg);
						//             return false;
						//         }
						//     });
					})
					.catch(() => {});
			},
			confirmEnd() {
				this.theEnd = false;
				this.$confirm('是否确认派位完成', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$message.info('数据量较大，请耐心等待')
          let fd = new FormData();
          fd.append("status", this.e);
					queRenPaiWei(fd, this.prefixDeptCode).then(res => {
						this.$message.success('派位完成')
						this.$router.back()
					})

				}).catch(() => {})
			},
			loadStudentList() {
				this.loading1 = true
				this.$message.info('数据量较大，请耐心等待')
        let fd = new FormData();
        fd.append("status", this.e);
				daiPaiWeiStudent(fd, this.prefixDeptCode).then(res => {
					// if (res.code == "200") {
					this.studentList = res;
					this.loading1 = false
					// } else {
					//     this.$message.error(res.msg);
					//     return false;
					// }
				});
			}
		}
	};
</script>
<style>
	@import "../../../../public/index.css";
</style>
<style lang="less" scoped>
	.central {
		height: 80px;
		line-height: 80px;
		position: relative;

		.central_center {
			position: absolute;
			display: block;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);

			color: #01aaed;
			font-size: 24px;
		}

		.central_right {
			float: right;
			display: flex;
			justify-content: space-evenly;
			width: 400px;

			.central_right_de {
				display: flex;
				align-items: center;

				.boxB {
					border: 1px solid #909399;
					height: 20px;
					color: #01aaed;
					text-align: center;
					width: 50px;
					line-height: 20px;
				}
			}
		}
	}

	.rowbtn {
		display: flex;
		justify-content: space-evenly;

		.el-button {
			width: 13%;
			border-radius: 8px;
		}
	}
</style>
