<template>
  <div class="school">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>首页</el-breadcrumb-item>
      <el-breadcrumb-item>{{title}}</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="selct">
      <el-button type="primary" @click="returnPage" icon="el-icon-caret-left">返回</el-button>
    </div>

    <div class="paiw">
      <el-card class="box-card" v-for="(school, index) in schoolList" :key="index">
        <div slot="header" class="clearfix">
         <div class="flexd">
           <div class="w100"> <span>{{ school.schoolName }}</span></div>
           <div class="w100 right">
<!--             <el-tag  type="info"   v-if="school.isShow==0">无需派位</el-tag>-->
          <el-tag type="warning" v-if="school.isShow==1">可派位</el-tag>
          <el-tag type="info" v-if="school.isShow==3">无需派位</el-tag>
           <el-tag type="success"  v-if="school.isShow==0">派位完成</el-tag>
            <!-- <el-tag type="danger"  v-if="school.planQuantity==0&&school.currentQuantity">本校不招生</el-tag> -->
           </div>
         </div>
        </div>
        <div class="flexd text item">
          <div class="w100">
            <div>  <span class="span">学位剩余人数：{{ school.planNumOne }}人</span></div>
<!--            <div>  <span class="span">女生学位剩余人数：{{ school.planNumTwo }}人</span></div>-->
            <div>  <span class="span">待派位人数：{{ school.noPlanNumOne }}人</span></div>
<!--            <div>  <span class="span">待派位女生人数：{{ school.noPlanNumTwo }}人</span></div>-->
          </div>
          <div class="w100 flexd right" style="    height: 42px;
    align-items: flex-end;">
                <el-button
            class="button"
            type="primary"
            size="mini"
            v-if="school.isShow==1"
            @click="doAssignment(school.schoolId, school.schoolName)"
          >进行派位</el-button>
<!--             <el-button-->
<!--             v-if="(school.currentQuantity <= school.planQuantity)&&(school.currentQuantity!=0&&school.planQuantity!=0)"-->
<!--                      class="button"-->
<!--                      size="mini"-->
<!--                     type="primary"-->
<!--                      @click="autoPass(school.schoolId,agmtSchool)"-->
<!--              >直接录取</el-button>-->
                <el-button
                      class="button"
                      size="mini"
                      type="success"

                      @click="endList(school.schoolId, school.schoolName)"
              >录取结果</el-button>

          </div>
        </div>
      </el-card>
    </div>
  </div>

</template>
<script>
import {getPWSchoolList} from '@/api/longYaoPW'
// import {
//   assignmentSchoolListApi,
//     assignmentAutoPassApi,
//   assignmentEnrollTimeIsEndApi
// } from "../api/api";
export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      schoolList: [],
      isEnd: "",
      agmtSchool:'',
      step:'',
      sourceType:'',
        generateNumForm:{
          schoolId:'',
          agmtSchool:'',
          sourceType:''
      },
      title:'电脑派位管理'
    };
  },
  async created() {
    let {step,type}=this.$route.query;
    this.agmtSchool=['','first','second','third','fourth','fifth','sixth'][step];
    this.sourceType=type;
    this.step=step;
    this.title=this.title+`--第${step}志愿`
    await this.find();
    await this.enrollTimeLoad();
  },
  methods: {
    // 列表-获取全部
    find() {
      getPWSchoolList({step:this.step},this.prefixDeptCode).then(res=>{
        this.schoolList = res
      })
      // assignmentSchoolListApi(this.agmtSchool,this.sourceType).then(res => {
      //   if (res.code == "200") {
      //     this.schoolList = res.result;
      //     console.log(this.schoolList);
      //   } else {
      //     this.$message.error(res.msg);
      //     return false;
      //   }
      // });
    },
    returnPage() {
      if (window.history.length <= 1) {
    this.$router.push({ path: "/zh-CN/home" });
    return false;
  } else {
    this.$router.go(-1);
  }
    },
    doAssignment(id, name) {
      // if (this.isEnd) {
        localStorage.setItem("assingnmentSchoolId", id);
        localStorage.setItem("assingnmentSchoolName", name);
        this.$router.push({path:"/dispatch/assignment_longYao",query:{agmtSchool:this.agmtSchool,sourceType:this.sourceType}});
      // } else {
      //   this.$alert("报名时间未结束。");
      // }
    },
      autoPass(schoolId) {
           this.$confirm('确认直接录取？').then((e)=>{
         // if (this.isEnd) {
              this.generateNumForm.schoolId = schoolId;
              this.generateNumForm.agmtSchool = this.agmtSchool;
              this.generateNumForm.sourceType = this.sourceType;
              console.info(this.generateNumForm)
              // assignmentAutoPassApi(this.generateNumForm).then(res => {
              //     if (res.code == "200") {
              //         this.$alert("直接录取成功。");
              //         this.find()
              //     } else {
              //         this.$message.error(res.msg);
              //         return false;
              //     }
              // });
          // }
           })

      },
    endList(id, name) {
      // if (this.isEnd) {
        localStorage.setItem("assingnmentSchoolId", id);
        localStorage.setItem("assingnmentSchoolName", name);
        this.$router.push("/dispatch/assignmentEndList_longYao?agmtSchool="+this.agmtSchool+'&sourceType='+this.sourceType);
      // } else {
      //   this.$alert("报名时间未结束。");
      // }
    },
    enrollTimeLoad() {
      // assignmentEnrollTimeIsEndApi().then(res => {
      //   if (res.code == "200") {
      //     this.isEnd = res.result;
      //   } else {
      //     this.$message.error(res.msg);
      //     return false;
      //   }
      // });
    }
  }
};
</script>
<style>
@import "../../../../public/index.css";
</style>
<style lang="less" scoped>
.school {
  width: 100%;
}
.span {
  display: inline-block;
  padding-right: 24px;
}
.paiw {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .box-card {
    width: 49%;
  }
}
.text {
  margin-top: 10px;
}
</style>
