<template>
  <div>
    <el-table
        :data="tableData"
        style="width: 100%"
        border
    >
      <el-table-column
          prop="schoolName"
          label="学校名称"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="numOne"
          label="计划数"
          align="center"
      >
      </el-table-column>
<!--      <el-table-column-->
<!--          prop="numTwo"-->
<!--          label="女生计划数"-->
<!--          align="center"-->
<!--      >-->
<!--      </el-table-column>-->
      <el-table-column
          prop="address"
          label="总计划数"
          align="center"
      >
        <template slot-scope="scope">
          <span>{{scope.row.numOne!=null&&scope.row.numOne!=null?Number(scope.row.numOne):'' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              @click="jiHua(scope.row)"
          >设置计划数</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        title="设置招生计划数"
        :visible.sync="jiHuaShow"
        align="center"
    >
      <el-form :model="form" style="width:300px">
        <el-form-item
            label="计划数"
        >
          <el-input
              v-model="form.numOne"
              placeholder="请输入计划数"
              clearable
          ></el-input>
        </el-form-item>
<!--        <el-form-item-->
<!--            label="女生计划数"-->
<!--        >-->
<!--          <el-input-->
<!--              v-model="form.numTwo"-->
<!--              placeholder="请输入女生计划数"-->
<!--              clearable-->
<!--          ></el-input>-->
<!--        </el-form-item>-->
        <el-form-item label="计划总数">
          <span style="color: red;font-size:25px;font-weight: bold">  {{Number(form.numOne) }}</span>
        </el-form-item>
      </el-form>
      <span style="color: red;font-size:25px;font-weight: bold">
            注: 请确认计划是否匹配学校学位数，不符请立即调整（派位以本次设置为准，开始后无法更改）
          </span>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="jiHuaShow = false">取 消</el-button>
        <el-button
            type="primary"
            @click="add"
        >确 定
        </el-button>
      </div>
    </el-dialog>
    <el-button  v-if="prefixDeptCode === '130531'"
        size="small"
        type="warning"
        @click="resetPw"
    >重置派位</el-button
    >
  </div>
</template>

<script>
import {getPWSchoolList, queRenPaiWei, resetPw, updateSchoolaJiHuaShu} from "@/api/longYaoPW";

export default {
  name: "pwjhs",
  data(){
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      jiHuaShow:false,
      form: {
        numOne: '',
        numTwo: '',
      },
      tableData:[]
    }
  },
  created() {
    this.getTable()
  },
  methods:{
    resetPw(){
      //重置派位结果
      resetPw({schoolId: this.assingnmentSchoolId, step:this.agmtSchool},this.prefixDeptCode).then(res=>{
        this.$message.success('重置成功')
      })
    },
    getTable(){
      getPWSchoolList({},this.prefixDeptCode).then(res=>{
        this.tableData=res
      })
    },
    jiHua(row){
      this.form.schoolId=row.schoolId
      this.form.numOne=row.numOne
      this.form.numTwo=row.numTwo
      this.form.id=row.id
      this.jiHuaShow=true
    },
    add(){
      updateSchoolaJiHuaShu(this.form,this.prefixDeptCode).then(res=>{

          this.$message.success('计划数设置成功')
          this.jiHuaShow=false
          this.getTable()
      })
      // this.jiHuaShow=false
    }
  }
}
</script>

<style scoped>

</style>
