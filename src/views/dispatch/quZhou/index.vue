<template>
	<div class="index flexd" style="display: flex;justify-content: space-between">
		<div class="w100">
			<img src="../../../assets/img/linzhangpaiweibg.png" style="width:1000px" />
		</div>
		<div class="w100 flexd center ">
			<div class="oprationBox">
				<div><el-button size="large" type="success" @click="jump">初中电脑派位</el-button></div>
			</div>
		</div>
	</div>
</template>
<script>
	export default {
		methods: {
			jump() {
				this.$router.push('/dispatch/pwSchoolListQZ')
			}
		}
	}
</script>
<style>
	@import "../../../../public/index.css";
</style>
<style lang="less" scoped>
	.index {
		padding: 48px;

		/deep/.mimi {
			.el-dialog__body {
				padding: 10px 0;
			}

			.el-dialog__header {
				padding: 15px 20px 10px;
			}
		}

		.oprationBox {
			button {
				margin-bottom: 48px;
				width: 230px;
				height: 60px;
				font-size: 16px;
			}
		}
	}
</style>