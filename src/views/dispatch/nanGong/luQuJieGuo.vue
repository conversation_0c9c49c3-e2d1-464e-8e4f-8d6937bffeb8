<template>
  <div>
    <el-form :model="search" :inline="true">
      <el-form-item>
        <el-input v-model="search.keywords" size="small" clearable placeholder="学生姓名/身份证号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select clearable size="small" v-model="search.schoolName" placeholder="派位成功学校"  @change="change" :filterable="true">
          <el-option
              v-for="item in schoolList"
              :key="item.deptName"
              :label="item.deptName"
              :value="item.deptName"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
            @change="change"
            size="small"
            v-model="search.sex"
            placeholder="选择性别"
            class="sd-w-130"
            clearable
        >
          <el-option label="男" :value="1"></el-option>
          <el-option label="女" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
            size="small"
            v-model="search.isPwStatus"
            placeholder="选择状态"
            class="sd-w-130"
            clearable
        >
          <el-option label="派位成功" :value="2"></el-option>
          <el-option label="派位失败" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
            size="small"
            type="primary"
            icon="el-icon-search"
            @click="getTableData1"
        ></el-button>
        <el-button
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="exportData"
      >导出派位信息</el-button
      >
        <el-button  v-if="prefixDeptCode === '130526'"
                    size="small"
                    type="warning"
                    @click="resetPw"
        >重置派位</el-button
        >
      </el-form-item>

    </el-form>
    <el-table :data="tableData" border stripe
              v-loading="isLoad" >
			<el-table-column
				type="index"
				align="center"
				label="序号"
				width="100"
			></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
      ></el-table-column>
      <el-table-column
          align="center"
          label="性别"
          prop="studentIdCardNumber"
      >
        <template slot-scope="scope">
          {{ scope.row.studentGender == 1 ? "男" :scope.row.studentGender == 2 ? "女" :''}}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="录取学校"
          prop="volunteerSchoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="状态"
          prop="locationStatus"
      >
        <template slot-scope="scope">
          {{ scope.row.locationStatus == 1 ? "待派位" :scope.row.locationStatus == 2 ? "已派位" :scope.row.locationStatus == 3 ? "派位失败" :''}}
        </template>
      </el-table-column>

    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="pageSizes"
      >
        <!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getPWSchoolList, huoQuChengGongPaiWei, resetPw} from "@/api/quXianPW";
// import { getTableData } from "@/api/common"
import {pref} from "@/utils/common";
export default {
  name: "luQuJieGuo",
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      tableData:[],
      schoolList:[{id: '1',deptName: '第一中学附属学校'},{id: '2',deptName: '第二中学'},{id: '3',deptName: '第三中学'},{id: '4',deptName: '第四中学'},{id: '5',deptName: '第五中学'}],
      pageSizes:[10,300,500,800],
      total:0,
      isLoad:true,
      search:{
        pageNumber:1,
        pageSize:10,
				isTwins: '',
        isPwStatus:''
      }
    }
  },
  created() {
    // this.getSchool()
		this.getTableData()
  },
  methods:{
    resetPw(){
      //重置派位结果
      resetPw(this.search,this.prefixDeptCode).then(res=>{
        this.$message.success('重置成功')
        this.isLoad=true
        this.getTableData1()
      })
    },
    change(){
      this.$forceUpdate()
    },
    getTableData1(){
      this.isLoad=true
     this.search.pageNumber=1
     this.getTableData()
    },
    getTableData(){
      huoQuChengGongPaiWei(this.search,this.prefixDeptCode).then(res=>{
        this.tableData = res.records
        this.total =Number(res.total)
        this.isLoad=false
      })
    },
    // getSchool(){
    //   getDepts({
		// 		keywords: '',
		// 		nature: '',
		// 		period: 3,
		// 		parentId: 176,
		// 		type: '',
		// 		rejectSchoolId: '',
		// 		level: 3,
		// 		status: 1
		// 	}, this.prefixDeptCode).then(res=>{
    //     this.schoolList = res
    //   })
    // },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
    exportData(){
			this.$message.info('正在导出，请耐心等待')
      let params = this.search;
      this.$download(
          `${pref+this.prefixDeptCode}/biz/TbPaiWeiSchoolSettingController/exportPwData`,
          params,
          "xls",
          "派位成功学生列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
  }
}
</script>

<style scoped>

</style>
