<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-form :model="search" :inline="true">
<!--          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.enrollId"
                placeholder="报名ID"
                clearable
            ></el-input>
          </el-form-item>-->
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.studentName"
                placeholder="姓名或身份证"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.isPwStatus"
                placeholder="派位状态"
                clearable
            >
              <el-option label="待派位" :value="1"></el-option>
              <el-option label="派位成功" :value="2"></el-option>
              <el-option label="派位失败" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item>
            <el-date-picker
              size="small"
              v-model="search.houseDate"
              type="date"
              placeholder="购房合同时间"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-date-picker
                size="small"
                v-model="search.createdTime"
                type="datetime"
                placeholder="报名开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-date-picker
                size="small"
                v-model="search.endTime"
                type="datetime"
                placeholder="报名结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item v-if="role == 'COUNTY_ADMIN' || role == 'AUDITOR'">
            <el-select
                size="small"
                v-model="search.enrollSchoolId"
                placeholder="报名学校"
                filterable
                clearable
            >
              <el-option
                  v-for="item in schoolList"
                  :label="item.deptName"
                  :value="item.id"
                  :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="(role == 'COUNTY_ADMIN' || role == 'AUDITOR') && prefixDeptCode !=='130582'">
            <el-select
                size="small"
                v-model="search.adjustSchoolId"
                placeholder="调剂学校"
                filterable
                clearable
            >
              <el-option
                  v-for="item in schoolList"
                  :label="item.deptName"
                  :value="item.id"
                  :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.type"
                placeholder="类别"
                clearable
            >
              <el-option
                  v-for="(item, index) in typeList"
                  :label="item"
                  :value="item"
                  :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="role == 'COUNTY_ADMIN' || role == 'AUDITOR'">
            <el-select
                size="small"
                v-model="search.nature"
                placeholder="学校性质"
                clearable
            >
              <el-option label="乡镇学校" :value="1"></el-option>
              <el-option label="城区学校" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="prefixDeptCode !== '130532'&& prefixDeptCode !== '130534'">
            <el-select
                size="small"
                v-model="search.schoolReviewStatus"
                placeholder="学校审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="驳回-修改信息" :value="3"></el-option>
              <el-option label="驳回-不可再报" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="prefixDeptCode === '130532'">
            <el-select
                size="small"
                v-model="search.schoolReviewStatus"
                placeholder="学校复审状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="驳回-修改信息" :value="3"></el-option>
              <el-option label="驳回-不可再报" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="role === 'SCHOOL' && prefixDeptCode === '130532'">
            <el-select
                size="small"
                v-model="search.schoolAuditorReviewStatus"
                placeholder="学校初审状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="驳回-修改信息" :value="3"></el-option>
              <el-option label="驳回-不可再报" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                v-if="prefixDeptCode === '130582'&& role === 'SCHOOL'"
                size="small"
                v-model="search.auditorReviewStatus"
                placeholder="学校审核员状态"
                clearable
            >
              <el-option label="通过" :value="1"></el-option>
              <el-option label="不通过" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="prefixDeptCode !== '130532'&& prefixDeptCode !== '130534'">
            <el-select
                size="small"
                v-model="search.educationReviewStatus"
                placeholder="教育局审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="驳回-修改信息" :value="3"></el-option>
              <el-option label="驳回-不可再报" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.estateReviewStatus"
                placeholder="房管局审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.publicSecurityRegistrationReviewStatus"
                placeholder="公安户口审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.publicSecurityResidenceReviewStatus"
                placeholder="公安居住证审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>-->
          <el-form-item>
            <el-select
                size="small"
                v-model="search.isRelated"
                placeholder="是否多胞胎"
                clearable
            >
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item>
            <el-select
                size="small"
                v-model="search.publicityStatus"
                placeholder="是否公示"
                clearable
            >
              <el-option label="已公示" :value="1"></el-option>
              <el-option label="未公示" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.admissionLetterInStatus"
                placeholder="是否发送录取通知书"
                clearable
            >
              <el-option label="已发送" :value="1"></el-option>
              <el-option label="未发送" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.checkInStatus"
                placeholder="是否报到"
                clearable
            >
              <el-option label="已报到" :value="1"></el-option>
              <el-option label="未报到" :value="0"></el-option>
            </el-select>
          </el-form-item>-->
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData1"
            ></el-button>
          </el-form-item>
            <!--<el-row>
              <el-form-item>
                <el-button
                    size="small"
                    type="primary"
                    icon="el-icon-plus"
                    v-if=" prefixDeptCode !== '130582' && (role === 'COUNTY_ADMIN' || role === 'SCHOOL') && prefixDeptCode !== '130502'"
                    @click="go2AddAd"
                >添加报名信息
                </el-button
                >
                <el-button
                    size="small"
                    type="primary"
                    icon="el-icon-plus"
                    v-if="role === 'SCHOOL' && prefixDeptCode == '130502'"
                    @click="go2AddAd"
                >添加报名信息
                </el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-plus"
                  v-else-if="prefixDeptCode === '130582' && role === 'SCHOOL'"
                  @click="go2AddAd"
              >添加报名信息</el-button
              >
                <el-button
                    size="small"
                    type="warning"
                    icon="el-icon-download"
                    @click="exportEnrollInfo"
                    v-if="
                    role === 'COUNTY_ADMIN' ||
                    role === 'SCHOOL'
                  "
                >导出报名信息
                </el-button
                >-->
<!--              role == 'AUDITOR' ||-->
<!--              <el-button
                  size="small"
                  type="warning"
                  icon="el-icon-download"
                  @click="exportHouseholdSurvey"
                  v-if="
                  role == 'COUNTY_ADMIN' ||

                  role == 'SCHOOL'
                "
              >下载入户调查单
              </el-button
              >-->
<!--              role == 'AUDITOR' ||-->
          <!--<el-button
              size="small"
              type="success"
              icon="el-icon-check"
              @click="batchAudit"
              v-if="(role === 'COUNTY_ADMIN'&&prefixDeptCode!=='130532' &&prefixDeptCode !== '130502'&& prefixDeptCode !== '130534')|| (role === 'SCHOOL'&& (prefixDeptCode==='130532' || prefixDeptCode == '130502')&& prefixDeptCode !== '130534')"
          >批量审核通过
          </el-button
          >
          <el-button
              size="small"
              type="primary"
              icon="el-icon-s-promotion"
              @click="publicReview"
              v-if="role === 'SCHOOL' && funcStatusInfo.publicStatus && (prefixDeptCode === '130503' || prefixDeptCode === '130502')"
          >线下审核通知</el-button
          >
          <el-button
              size="small"
              type="primary"
              icon="el-icon-s-promotion"
              @click="publicAdmission"
              v-if="role == 'COUNTY_ADMIN' && funcStatusInfo.publicStatus && prefixDeptCode!== '130532' && prefixDeptCode !== '130502'"
          >公示录取结果
          </el-button
          >
          <el-button
              size="small"
              type="primary"
              icon="el-icon-s-promotion"
              @click="publicAdmission"
              v-if="role === 'SCHOOL' && funcStatusInfo.publicStatus && (prefixDeptCode === '130532' || prefixDeptCode === '130502')"
          >公示录取结果</el-button
          >
          <el-button
              size="small"
              type="primary"
              icon="el-icon-s-promotion"
              @click="notice"
              v-if="role == 'SCHOOL' && funcStatusInfo.noticeStatus"
          >发送录取通知书
          </el-button
          >
          <el-button
              size="small"
              type="primary"
              icon="el-icon-plus"
              v-if="( prefixDeptCode === '130532') && role === 'SCHOOL'"
              @click="allocateAuditor"
          >分配审核员</el-button
          >
        </el-form-item>
      </el-row>-->
        </el-form>
      </div>
    </div>
    <el-table
        :data="tableData.records"
        border
        stripe
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="50" :fixed="true">
      </el-table-column>
      <el-table-column
          align="center"
          label="是否公示"
          prop="publicityStatus"
          width="85"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="是否发送录取通知书"
          prop="admissionLetterInStatus"
          width="95"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="报名ID"
          prop="enrollId"
          width="140"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
          width="120"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
          width="170"
      ></el-table-column>
      <!--<el-table-column
          align="center"
          label="类别"
          prop="type"
          width="300"
      ></el-table-column>
       <el-table-column
        align="center"
        label="购房时间"
        prop="purchaseTime"
        width="100"
      ></el-table-column> -->
      <el-table-column
          align="center"
          label="报名学校"
          prop="enrollSchoolName"
          width="180"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="派位状态"
          prop="locationStatus"
          width="180"
      >
        <template slot-scope="{ row }">
          <span v-if="row.locationStatus === '1'">待派位</span>
          <span v-else-if="row.locationStatus === '2'">派位成功</span>
          <span v-else-if="row.locationStatus === '3'">派位失败</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
<!--      <el-table-column
          v-if="(prefixDeptCode === '130502' || prefixDeptCode === '130503')"
          align="center"
          label="是否发送线下审核通知"
          prop="offlineAudit"
          width="180"
      >
        <template slot-scope="scope">
          {{ scope.row.offlineAudit === '1' ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column
          v-if="prefixDeptCode === '130502'"
          align="center"
          label="审核人"
          prop="auditUser"
          width="180"
      ></el-table-column>
      <el-table-column
          align="center"
          label="调剂学校"
          prop="adjustSchoolName"
          width="180"
          v-if="prefixDeptCode!=='130582'"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报名时间"
          prop="enrollTime"
          width="160"
      ></el-table-column>-->
      <el-table-column
          align="center"
          label="是否多胞胎"
          prop="isRelated"
          width="95"
      >
        <template slot-scope="scope">
          {{ scope.row.isRelated === 1 ? "是" : "否" }}
        </template>
      </el-table-column>
<!--      <el-table-column
          align="center"
          label="是否优抚对象"
          prop="isEntitledGroup"
          width="110"
      ></el-table-column>
      <el-table-column
          align="center"
          label="毕业小学"
          prop="entitledGroupType"
      ></el-table-column>
      <el-table-column
          align="center"
          label="房产审核"
          prop="estateReviewStatus"
          width="85"
      >
        <template slot-scope="scope">
          <el-tooltip
              v-if="scope.row.estateReviewStatus === '不通过' && scope.row.estateReviewReason"
              class="item"
              effect="dark"
              :content="scope.row.estateReviewReason"
              placement="top"
          >
            <span style="cursor: pointer; color: #F56C6C;">{{ scope.row.estateReviewStatus }}</span>
          </el-tooltip>
          <span v-else>{{ scope.row.estateReviewStatus }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="公安户口审核"
          prop="publicSecurityRegistrationReviewStatus"
          width="85"
      >
        <template slot-scope="scope">
          <el-tooltip
              v-if="scope.row.publicSecurityRegistrationReviewStatus === '不通过' && scope.row.publicSecurityReviewReason"
              class="item"
              effect="dark"
              :content="scope.row.publicSecurityReviewReason"
              placement="top"
          >
            <span style="cursor: pointer; color: #F56C6C;">{{ scope.row.publicSecurityRegistrationReviewStatus }}</span>
          </el-tooltip>
          <span v-else>{{ scope.row.publicSecurityRegistrationReviewStatus }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="公安居住证审核"
          prop="publicSecurityResidenceReviewStatus"
          width="85"
      >
        <template slot-scope="scope">
          <el-tooltip
              v-if="scope.row.publicSecurityResidenceReviewStatus === '不通过' && scope.row.publicSecurityReviewReason"
              class="item"
              effect="dark"
              :content="scope.row.publicSecurityReviewReason"
              placement="top"
          >
            <span style="cursor: pointer; color: #F56C6C;">{{ scope.row.publicSecurityResidenceReviewStatus }}</span>
          </el-tooltip>
          <span v-else>{{ scope.row.publicSecurityResidenceReviewStatus }}</span>
        </template>
      </el-table-column>
      <el-table-column
          v-if="role === 'SCHOOL'&& prefixDeptCode==='130532'"
          align="center"
          label="学校初审状态"
          prop="schoolAuditorReviewStatus"
          width="85"
      >
        <template slot-scope="{ row }">
          <span v-if="row.schoolAuditorReviewStatus === '1'">待审核</span>
          <span v-else-if="row.schoolAuditorReviewStatus === '2'">通过</span>
          <span v-else-if="row.schoolAuditorReviewStatus === '3'">驳回-修改信息</span>
          <span v-else-if="row.schoolAuditorReviewStatus === '4'">驳回-不可再报</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column
          v-if="prefixDeptCode!== '130532'"
          align="center"
          label="学校审核状态"
          prop="schoolReviewStatus"
          width="85"
      ></el-table-column>
      <el-table-column
          v-else-if="prefixDeptCode === '130532'"
          align="center"
          label="学校复审状态"
          prop="schoolReviewStatus"
          width="85"
      ></el-table-column>
      <el-table-column
          v-if="prefixDeptCode !== '130532' && prefixDeptCode !== '130502'"
          align="center"
          label="教育局审核状态"
          prop="educationReviewStatus"
          width="85"
      ></el-table-column>
      <el-table-column
          align="center"
          label="是否报到"
          prop="checkInStatus"
          width="85"
      ></el-table-column>-->
      <el-table-column
          align="center"
          label="备注"
          prop="remark"
          show-overflow-tooltip
      ></el-table-column>
      <el-table-column align="center" label="操作" width="360" fixed="right">
        <template slot-scope="{ row, $index }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail(row, $index)"
          >详情
          </el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus == '通过'"
              @click="pass(row)"
              v-if="(role === 'COUNTY_ADMIN' || role === 'AUDITOR')&& prefixDeptCode !== '130532'&& prefixDeptCode !== '130534'&& prefixDeptCode !== '130502'"
          >通过
          </el-link
          >
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus == '不通过'"
              @click="reject(row)"
              v-if="(role === 'COUNTY_ADMIN' || role === 'AUDITOR')&& prefixDeptCode !== '130532'&& prefixDeptCode !== '130534'&& prefixDeptCode !== '130502'"
          >不通过
          </el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus !== '待审核'"
              @click="pass(row)"
              v-if="role === 'SCHOOL'&& prefixDeptCode !== '130582'&& prefixDeptCode !== '130532'&& prefixDeptCode !== '130534'"
          >通过
          </el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus !== '待审核'"
              @click="pass(row)"
              v-else-if="role === 'SCHOOL'&& prefixDeptCode === '130582' "
          >通过</el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus !== '待审核'|| row.schoolReviewCount !== 1"
              @click="pass(row)"
              v-else-if="role === 'SCHOOL'&& prefixDeptCode === '130532'"
          >通过</el-link
          >
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus !== '待审核'"
              @click="reject(row)"
              v-if="role === 'SCHOOL'&& prefixDeptCode !== '130582'&& prefixDeptCode!== '130532'&& prefixDeptCode !== '130534'"
          >不通过
          </el-link
          >
          <el-link
              icon="el-icon-check"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus !== '待审核'"
              @click="reject(row)"
              v-else-if="role === 'SCHOOL'&& prefixDeptCode === '130582' "
          >不通过</el-link
          >
          <el-link
              icon="el-icon-check"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus !== '待审核'|| row.schoolReviewCount!== 1"
              @click="reject(row)"
              v-else-if="role === 'SCHOOL'&& prefixDeptCode === '130532' "
          >不通过</el-link
          >
          <el-link
              icon="el-icon-refresh"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              @click="adjust(row)"
              v-if="
              (role === 'COUNTY_ADMIN' || role === 'AUDITOR') &&
              row.educationReviewStatus === '通过' &&
              row.publicityStatus === '未公示' && prefixDeptCode!=='130582'
            "
          >调剂
          </el-link
          >
          <el-link
              icon="el-icon-s-flag"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              @click="report(row)"
              v-if="role == 'SCHOOL'"
              :disabled="
              !row.educationReviewStatus == '通过' ||
              row.publicityStatus == '未公示'
            "
          >报到
          </el-link
          >
          <el-link
              icon="el-icon-star-off"
              type="warning"
              :underline="false"
              @click="priority(row)"
              v-if="
              (role == 'COUNTY_ADMIN' || role == 'SCHOOL') &&
              row.isEntitledGroup == '是'
            "
          >优抚类型
          </el-link
          >
          <el-link
              icon="el-icon-remove-outline"
              type="danger"
              :underline="false"
              @click="revokePublic(row)"
              v-if="role == 'COUNTY_ADMIN' && row.publicityStatus == '已公示' && prefixDeptCode !== '130502'"
          >撤销公示结果
          </el-link
          >
          <el-link
              icon="el-icon-remove-outline"
              type="danger"
              :underline="false"
              @click="revokePublic(row)"
              v-if="role == 'SCHOOL' && row.publicityStatus == '已公示' && prefixDeptCode == '130502'"
          >撤销公示结果
          </el-link
          >
          <el-link
              icon="el-icon-delete"
              type="danger"
              :underline="false"
              @click="deleteSign(row)"
              v-if="role == 'COUNTY_ADMIN'&& funcStatusInfo.deleteStatus "
          >删除报名信息
          </el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <span class="signNum">报名总人数：{{ tableData.signNum }}</span
      >&emsp;
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="total"
      >
      </el-pagination>
    </div>

    <!-- 不通过 -->
    <el-dialog
        title="不通过"
         :visible.sync="modal.reject"
        append-to-body
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="rejectForm"
          ref="rejectForm"
          :rules="rejectRules"
          label-position="right"
          label-width="100px"
      >
        <el-form-item prop="reviewStatus" label="驳回类型：">
          <el-select v-model="rejectForm.reviewStatus">
            <el-option label="驳回-修改信息" :value="3"></el-option>
            <el-option label="驳回-不可再报" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="reviewReason" label="驳回原因：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="rejectForm.reviewReason"
              placeholder="请输入驳回原因，最多200个字符"
              style="width: 400px"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="tips">
        <div>注释：</div>
        <div>
          1、驳回-修改信息：选择修改信息，可以修改报名类别及基础信息，不可修改报名学校
        </div>
        <div>
          2、驳回-不可再报：选择不可再报，重新填写报名信息，不能选择原报名学校
        </div>
      </div>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('reject', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmReject"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 调剂 -->
    <el-dialog
        title="调剂"
      :visible.sync="modal.adjust"
        append-to-body
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="adjustForm"
          ref="adjustForm"
          :rules="adjustRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item label="报名ID：">{{ adjustInfo.enrollId }}</el-form-item>
        <el-form-item label="学生姓名：">{{
            adjustInfo.studentName
          }}
        </el-form-item>
        <el-form-item label="报名学校：">{{
            adjustInfo.enrollSchoolName
          }}
        </el-form-item>
        <el-form-item prop="adjustSchoolId" label="调剂学校选择：">
          <el-select
              v-model="adjustForm.adjustSchoolId"
              style="width: 300px"
              @change="adjustSchoolChange"
          >
            <el-option
                v-for="item in schoolListAvailable"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('adjust', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmAdjust"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 报到 -->
    <el-dialog
        title="报到"
        :visible.sync="modal.report"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="reportForm"
          ref="reportForm"
          :rules="reportRules"
          label-position="right"
          label-width="120px"
      >
        <el-form-item prop="reportStatus" label="报到类型：">
          <el-radio-group v-model="reportForm.reportStatus">
            <el-radio :label="1">报到</el-radio>
            <el-radio :label="2">未报到</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
            :prop="reportForm.reportStatus == 2 ? 'noShowCause' : 'empty'"
            label="未报到原因："
        >
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="reportForm.noShowCause"
              placeholder="请输入未报到原因，最多200个字符"
              style="width: 400px"
              maxlength="200"
              show-word-limit
              :disabled="reportForm.reportStatus != 2"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('report', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmReport"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 优抚类型 -->
    <el-dialog
        title="优抚类型"
        :visible.sync="modal.priority"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="priorityForm"
          ref="priorityForm"
          :rules="priorityRules"
          label-position="right"
          label-width="120px"
      >
        <el-form-item label="学生姓名：">{{
            priorityInfo.studentName
          }}
        </el-form-item>
        <el-form-item label="学生身份证号：">{{
            priorityInfo.studentIdCardNumber
          }}
        </el-form-item>
        <el-form-item prop="priorityType" label="优抚类型：">
          <el-select v-model="priorityForm.priorityType" style="width: 220px">
            <el-option
                v-for="(item, index) in priorityTypeList"
                :key="index"
                :label="item.value"
                :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
            prop="priorityTypeStr"
            label="其他类型："
            v-if="priorityForm.priorityType === '其他-填写其他类型'"
        >
          <el-input
              type="input"
              v-model.trim="priorityForm.priorityTypeStr"
              placeholder="请输入其他类型"
              style="width: 220px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('priority', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmPriority"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 撤销公示结果 -->
    <el-dialog
        title="撤销公示结果"
        :visible.sync="modal.revokePublic"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="revokePublicForm"
          ref="revokePublicForm"
          :rules="revokePublicRules"
          label-position="right"
          label-width="130px"
      >
        <el-form-item prop="content" label="撤销公示原因：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="revokePublicForm.content"
              placeholder="请输入撤销公示原因，最多200个字符"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('revokePublic', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmRevokePublic"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 删除报名信息 -->
    <el-dialog
        title="删除报名信息"
        :visible.sync="modal.deleteSign"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="deleteSignForm"
          ref="deleteSignForm"
          :rules="deleteSignRules"
          label-position="right"
          label-width="130px"
      >
        <el-form-item prop="content" label="删除报名原因：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="deleteSignForm.content"
              placeholder="请输入删除报名原因，最多200个字符"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('deleteSign', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmDeleteSign"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 发送录取通知书 -->
    <el-dialog
        title="发送录取通知书"
        :visible.sync="modal.notice"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <p class="notice-num">发送人数：{{ noticeForm.num }}</p>
      <el-form
          :model="noticeForm"
          ref="noticeForm"
          :rules="noticeRules"
          label-position="right"
      >
        <el-form-item prop="content">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="noticeForm.content"
              placeholder="请输入录取通知书内容，最多200个字符"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('notice', false)"
        >取消
        </el-button
        >
        <el-button
            size="small"
            type="primary"
            @click="confirmNotice"
            :disabled="noticeForm.num == '' || noticeForm.num == 0"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 详情 -->
    <el-dialog
        :visible.sync="modal.stuDetail"
        :close-on-click-modal="false"
        title="学生报名详情"
        center
        width="1240px"
        @close="stuDetailClose"
    >
      <enroll-detail
          :stu-detail="curStuDetail"
          :key="curStuDetail.studentBaseId"
      ></enroll-detail>
      <div>
        <el-skeleton :loading="loadingAuditRecord" animated>
          <p>审核情况</p>
          <el-table :data="auditRecordList" border stripe>
            <el-table-column
                align="center"
                label="操作时间"
                prop="createTime"
            ></el-table-column>
            <el-table-column
                align="center"
                label="角色"
                prop="roleName"
            ></el-table-column>
            <el-table-column
                align="center"
                label="姓名"
                prop="nickname"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作账号"
                prop="creatorName"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作记录"
                prop="content"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作"
            >
              <template slot-scope="{ row }">
                <el-button
                    type="primary"
                    plain
                    size="small"
                    v-if="row.type == 1 || row.type == 2 || row.type == 6 || row.type == 7 || row.type == 19 || row.type == 84"
                    @click="getEnrollRecords(row)"
                >报名详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-table :data="auditStatusList" border stripe>
            <el-table-column
              align="center"
              label="角色"
              prop="role"
            ></el-table-column>
            <el-table-column
              align="center"
              label="审核状态"
              prop="status"
            ></el-table-column>
            <el-table-column
              align="center"
              label="操作账号"
              prop="operationName"
            ></el-table-column>
            <el-table-column
              align="center"
              label="驳回原因"
              prop="causeOfRejection"
            ></el-table-column>
            <el-table-column
              align="center"
              label="调剂学校"
              prop="adjustSchoolName"
            ></el-table-column>
            <el-table-column
              align="center"
              label="操作时间"
              prop="operationTime"
            ></el-table-column>
          </el-table> -->
        </el-skeleton>
      </div>
      <div class="flex-center" style="margin-top: 20px">
        <el-button
            icon="el-icon-check"
            type="success"
            size="small"
            :disabled="curStuDetail.educationReviewStatus == '通过'"
            @click="pass(curStuDetail)"
            v-if="(role == 'COUNTY_ADMIN' || role == 'AUDITOR')&& prefixDeptCode !== '130532'&& prefixDeptCode !== '130534'&& prefixDeptCode !== '130502'"
        >通过
        </el-button
        >
        <el-button
            icon="el-icon-close"
            type="danger"
            size="small"
            :disabled="curStuDetail.educationReviewStatus == '不通过'"
            @click="reject(curStuDetail)"
            v-if="(role == 'COUNTY_ADMIN' || role == 'AUDITOR')&& prefixDeptCode !== '130532'&& prefixDeptCode !== '130534'&& prefixDeptCode !== '130502'"
        >不通过
        </el-button
        >
        <el-button
            icon="el-icon-check"
            type="success"
            size="small"
            :disabled="curStuDetail.educationReviewStatus != '待审核' "
            @click="pass(curStuDetail)"
            v-if="role == 'SCHOOL'&& prefixDeptCode !== '130582'&& prefixDeptCode !== '130534'"
        >通过
        </el-button
        >
        <el-button
            icon="el-icon-check"
            type="success"
            size="small"
            :disabled="curStuDetail.educationReviewStatus != '待审核' "
            @click="pass(curStuDetail)"
            v-else-if="role == 'SCHOOL'&& prefixDeptCode === '130582'"
        >通过
        </el-button
        >
        <el-button
            icon="el-icon-close"
            type="danger"
            size="small"
            :disabled="curStuDetail.educationReviewStatus != '待审核' "
            @click="reject(curStuDetail)"
            v-if="role == 'SCHOOL'&& prefixDeptCode !== '130582'&& prefixDeptCode !== '130534'"
        >不通过
        </el-button
        >
        <el-button
            icon="el-icon-close"
            type="danger"
            size="small"
            :disabled="curStuDetail.educationReviewStatus != '待审核' "
            @click="reject(curStuDetail)"
            v-else-if="role == 'SCHOOL'&& prefixDeptCode === '130582'"
        >不通过
        </el-button
        >
        <el-button type="primary" @click="prevEnrollDetail" size="small">上一条
        </el-button>
        <el-button type="primary" @click="nextEnrollDetail" size="small"
        >下一条
        </el-button
        >
        <el-button
            icon="el-icon-refresh"
            type="warning"
            size="small"
            @click="adjust(curStuDetail)"
            v-if="
            (role == 'COUNTY_ADMIN' || role == 'AUDITOR') &&
            curStuDetail.educationReviewStatus == '通过' &&
            curStuDetail.publicityStatus == '未公示' && prefixDeptCode!=='130582'
          "
        >调剂
        </el-button
        >
        <el-button size="small" type="info" @click="stuDetailClose"
        >关闭
        </el-button
        >
      </div>
    </el-dialog>
    <!-- 线下审核通知 -->
    <el-dialog
        title="线下审核通知"
        :visible.sync="modal.review"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="reviewForm"
          ref="reviewForm"
          :rules="offlineReview"
          label-position="right"
          label-width="100px"
      >
        <el-form-item prop="reviewReason" label="审核结果：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="reviewForm.schoolReviewResult"
              placeholder="请输入审核结果，最多200个字符"
              style="width: 400px"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('review', false)"
        >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmReview"
        >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 审核情况中指定的报名详情 -->
    <el-dialog
        :visible.sync="modal.enrollRecord"
        :close-on-click-modal="false"
        title="报名详情"
        center
        width="1240px"
    >
      <enroll-record-detail
          :key="curStuRecordId"
          :student-id="curStuRecordId"
      ></enroll-record-detail>
      <div slot="footer">
        <el-button size="small" type="info" @click='switchModal("enrollRecord", false)'>关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getEnrollList,
  getTypeList,
  passAudit,
  rejectAudit,
  adjust,
  report,
  batchAudit,
  publicity,
  publicAdmissionResults,
  sendRequisition,
  updateEntitledGroup,
  getAuditRecord,
  revokePublic,
  deleteEnroll, allocateSchoolAuditor, schoolReviewAudit,
} from "@/api/enrollment";
import {privateDeleteEnrollInfo} from '@/api/sysConfig';
import {getDepts} from "@/api/common";
import { pref, tangShanPriorityTypeList} from "@/utils/common";
import EnrollDetail from "@/components/EnrollDetail";
import EnrollRecordDetail from "@/components/EnrollRecordDetail";
import {getFuncStatus} from "@/api/funcSwitch";
import {getAddSignCheck, getPrivateAddSignCheck} from "@/api/setting"

export default {
  mixins: [TableMixin, ModalMixin],
  components: {EnrollDetail, EnrollRecordDetail},
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      role: this.$store.getters.role,
      search: {
        enrollStage: "2",
        enrollId: "",
        studentName: "",
        // houseDate: "",
        createdTime: "",
        endTime: "",
        enrollSchoolId: "",
        adjustSchoolId: "",
        schoolType:2,
        nature: "",
        schoolReviewStatus: "",
        educationReviewStatus: "",
        estateReviewStatus: "",
        checkInStatus: "",
        admissionLetterInStatus: "",
        publicSecurityRegistrationReviewStatus: "",
        publicSecurityResidenceReviewStatus: "",
        isRelated: "",
        publicityStatus: "",
        auditorReviewStatus:"",
        schoolAuditorReviewStatus: "", //学校审核员审核状态
      },
      // 审核
      reviewForm: {
        id: 0,
        idList:[],
        schoolReviewResult: "",
        reviewStatus: "",
      },
      offlineReview: {
        schoolReviewResult: [
          {
            required: true,
            message: "请输入审核结果",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      typeList: [],
      modal: {
        reject: false,
        adjust: false,
        report: false,
        notice: false,
        priority: false,
        review: false,
        stuDetail: false,
        revokePublic: false,
        deleteSign: false,
        enrollRecord: false
      },
      schoolList: [],
      multipleSelection: [],
      // 驳回
      rejectForm: {
        id: 0,
        reviewReason: "",
        reviewStatus: "",
      },
      rejectRules: {
        reviewStatus: [
          {
            required: true,
            message: "请选择驳回类型",
            trigger: "change",
          },
        ],
        reviewReason: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 调剂
      adjustForm: {
        id: "",
        adjustSchoolId: "",
        adjustSchoolName: "",
      },
      adjustRules: {
        adjustSchoolId: [
          {
            required: true,
            message: "请选择调剂学校",
            trigger: "change",
          },
        ],
      },
      adjustInfo: {
        enRollId: "",
        studentName: "",
        enrollSchoolName: "",
        adjustSchoolName: "",
      },
      // 报到
      reportForm: {
        id: "",
        noShowCause: "",
        reportStatus: "",
      },
      reportRules: {
        reportStatus: [
          {
            required: true,
            message: "请选择报到类型",
            trigger: "change",
          },
        ],
        noShowCause: [
          {
            required: true,
            message: "请输入未报到原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 发送录取通知书
      noticeForm: {
        content: "",
        num: "",
      },
      noticeRules: {
        content: [
          {
            required: true,
            message: "请输入录取通知书内容",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 优抚类型
      priorityForm: {
        id: "",
        priorityType: "",
        priorityTypeStr: "",
      },
      priorityRules: {
        priorityType: [
          {
            required: true,
            message: "选择优抚类型",
            trigger: "change",
          },
        ],
        priorityTypeStr: [
          {
            required: true,
            message: "输入其他类型",
            trigger: "blur",
          },
        ],
      },
      priorityInfo: {
        studentName: "",
        studentIdCardNumber: "",
      },
      priorityTypeList:  tangShanPriorityTypeList,
      // 撤销公示结果
      revokePublicForm: {
        id: "",
        content: "",
      },
      revokePublicRules: {
        content: [
          {
            required: true,
            message: "请输入撤销公示原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 删除报名信息
      deleteSignForm: {
        id: "",
        content: "",
      },
      deleteSignRules: {
        content: [
          {
            required: true,
            message: "请输入删除报名原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // auditStatusList: [],
      // loadingAuditStatus: true,
      auditRecordList: [],
      loadingAuditRecord: true,
      // 当前学生报名详情
      curStuDetail: {

      },
      // 下一条报名详情index
      index: 0,
      // 功能开关状态
      funcStatusInfo: {
        deleteStatus: false,
        publicStatus: false,
        noticeStatus: false,
      },
      // 审核情况中指定的报名详情
      curStuRecordId: ''
    };
  },
  computed: {
    schoolListAvailable() {
      return this.schoolList.filter(
          (item) =>
              item.deptName !=
              (this.adjustInfo.adjustSchoolName
                  ? this.adjustInfo.adjustSchoolName
                  : this.adjustInfo.enrollSchoolName)
      );
    },
  },
  async created() {
    if (this.role == "SCHOOL") {
      // 学校级 获取学校id
      this.search.enrollSchoolId = this.$store.getters.deptId;
    }
    if (this.role == "COUNTY_ADMIN" || this.role == "AUDITOR") {
      // 区县级 获取学校列表
      this.getDepts();
    }
    // 查询报名类别
    this.getTypeList();
    // 查询功能开关状态 小学
    this.getFuncStatusInfo("DELETE_STUDENT_PRIVATE");
    this.getFuncStatusInfo("PUBLIC_STUDENT_PRIVATE");
    this.getFuncStatusInfo("NOTICE_STUDENT_PRIVATE");
  },
  // methods: {
  //   // 列表
  //   getTableData11() {
  //     this.search.pageNumber = 1;
  //     this.getTableData();
  //   },
  //   getTableData() {
  //     this.tableLoading = true;
  //     getEnrollList(this.search, this.prefixDeptCode)
  //         .then((res) => {
  //           this.tableData = res;
  //         })
  //         .finally(() => {
  //           this.tableLoading = false;
  //         });
  //   },
  //   // 获取报名类别 2小学 3初中
  //   getTypeList() {
  //     getTypeList({key: 2}, this.prefixDeptCode).then((res) => {
  //       this.typeList = res;
  //     });
  //   },
  //   // 查询学校
  //   getDepts() {
  //     let params = {
  //       level: 3,
  //       period: "2",
  //       type:2,
  //       parentId: this.$store.getters.deptId,
  //     };
  //     getDepts(params).then((res) => {
  //       this.schoolList = res;
  //     });
  //   },
  //   //线下审核通知发送
  //   confirmReview(){
  //     this.$refs.reviewForm.validate((valid) => {
  //
  //       if (valid) {
  //         let params = {};
  //         params.idList = this.reviewForm.idList;
  //         params.id = this.reviewForm.id;
  //         params.schoolReviewStatus = this.reviewForm.schoolReviewResult;
  //         console.log(params);
  //         schoolReviewAudit(this.reviewForm, this.prefixDeptCode).then(() => {
  //           this.$message.success("操作成功");
  //           this.switchModal("review", false);
  //           this.getTableData();
  //         });
  //       }
  //     });
  //   },
  //   // 通过
  //   pass(row) {
  //     this.$confirm("确认该学生信息无误，审核通过？", "提示", {
  //       confirmButtonText: "确定",
  //       cancelButtonText: "取消",
  //       type: "warning",
  //     }).then(() => {
  //       passAudit(
  //           {id: row.studentBaseId, enrollStage: "2"},
  //           this.prefixDeptCode
  //       ).then(() => {
  //         this.$message.success("操作成功");
  //         this.getTableData();
  //       });
  //     });
  //   },
  //   // 驳回
  //   reject(row) {
  //     this.switchModal("reject", true);
  //     this.$nextTick(() => {
  //       this.$refs.rejectForm.resetFields();
  //       this.rejectForm.id = row.studentBaseId;
  //     });
  //   },
  //   //线下审核通知
  //   publicReview(){
  //     this.reviewForm.idList = [];
  //     this.reviewForm.idList = this.multipleSelection.map((item) => item.studentBaseId);
  //
  //     this.reviewForm.schoolReviewResult = "";
  //     if(this.reviewForm.idList.length==0){
  //       this.$message.error("请选择要审核的学生");
  //       return;
  //     }
  //     this.switchModal('review', true);
  //     console.log(this.reviewForm);
  //
  //   },
  //   // 驳回确定
  //   confirmReject() {
  //     this.$refs.rejectForm.validate((valid) => {
  //       if (valid) {
  //         let params = {};
  //         if (this.role == "COUNTY_ADMIN" || this.role == "AUDITOR") {
  //           params.id = this.rejectForm.id;
  //           params.educationReviewStatus = this.rejectForm.reviewStatus;
  //           params.educationReviewReason = this.rejectForm.reviewReason;
  //         } else if (this.role == "SCHOOL") {
  //           params.id = this.rejectForm.id;
  //           params.schoolReviewStatus = this.rejectForm.reviewStatus;
  //           params.schoolReviewReason = this.rejectForm.reviewReason;
  //         }
  //         rejectAudit(params, this.prefixDeptCode).then((res) => {
  //           this.$message.success("操作成功");
  //           this.switchModal("reject", false);
  //           this.getTableData();
  //         });
  //       }
  //     });
  //   },
  //   // 调剂
  //   adjust(row) {
  //     this.switchModal("adjust", true);
  //     this.$nextTick(() => {
  //       this.$refs.adjustForm.resetFields();
  //       this.adjustForm.id = row.studentBaseId;
  //       this.adjustForm.adjustSchoolId = "";
  //       this.adjustForm.adjustSchoolName = "";
  //
  //       this.adjustInfo.enrollId = row.enrollId;
  //       this.adjustInfo.studentName = row.studentName;
  //       this.adjustInfo.enrollSchoolName = row.enrollSchoolName;
  //       this.adjustInfo.adjustSchoolName = row.adjustSchoolName;
  //     });
  //   },
  //   // 调剂确定
  //   confirmAdjust() {
  //     this.$refs.adjustForm.validate((valid) => {
  //       if (valid) {
  //         adjust(this.adjustForm, this.prefixDeptCode).then((res) => {
  //           this.$message.success("操作成功");
  //           this.switchModal("adjust", false);
  //           this.getTableData();
  //         });
  //       }
  //     });
  //   },
  //   // 调剂学校选择change
  //   adjustSchoolChange() {
  //     this.schoolList.forEach((item) => {
  //       if (this.adjustForm.adjustSchoolId == item.id) {
  //         this.adjustForm.adjustSchoolName = item.deptName;
  //       }
  //     });
  //   },
  //   // 报到
  //   report(row) {
  //     this.switchModal("report", true);
  //     this.$nextTick(() => {
  //       this.$refs.reportForm.resetFields();
  //       this.reportForm.id = row.studentBaseId;
  //       this.reportForm.reportStatus = "";
  //       this.reportForm.noShowCause = "";
  //     });
  //   },
  //   // 报到确定
  //   confirmReport() {
  //     this.$refs.reportForm.validate((valid) => {
  //       if (valid) {
  //         report(this.reportForm, this.prefixDeptCode).then((res) => {
  //           this.$message.success("操作成功");
  //           this.switchModal("report", false);
  //           this.getTableData();
  //         });
  //       }
  //     });
  //   },
  //   // 优抚类型
  //   priority(row) {
  //     this.switchModal("priority", true);
  //     this.$nextTick(() => {
  //       this.$refs.priorityForm.resetFields();
  //       this.priorityForm.id = row.studentBaseId;
  //       this.priorityForm.entitledGroupType = "";
  //       this.priorityInfo.studentName = row.studentName;
  //       this.priorityInfo.studentIdCardNumber = row.studentIdCardNumber;
  //     });
  //   },
  //   // 优抚类型确定
  //   confirmPriority() {
  //     this.$refs.priorityForm.validate((valid) => {
  //       if (valid) {
  //         let params = {
  //           id: this.priorityForm.id,
  //           entitledGroupType:
  //               this.priorityForm.priorityType == "其他-填写其他类型"
  //                   ? this.priorityForm.priorityTypeStr
  //                   : this.priorityForm.priorityType,
  //         };
  //         updateEntitledGroup(params, this.prefixDeptCode).then((res) => {
  //           this.$message.success("操作成功");
  //           this.switchModal("priority", false);
  //           this.getTableData();
  //         });
  //       }
  //     });
  //   },
  //   // 前往添加报名信息
  //   go2AddAd() {
  //     if (this.role === "SCHOOL") {
  //       getPrivateAddSignCheck({key: '2:ADD'}, this.prefixDeptCode).then((res) => {
  //         if (res) {
  //             this.$router.push({path: "/enrollment/privateAddAd", query: {period: "primary"}});
  //         } else {
  //           this.$message.warning("添加学生报名信息时间已截止，不可添加学生报名信息，如有疑问请联系教育局");
  //         }
  //       });
  //     } else {
  //         this.$router.push({path: "/enrollment/privateAddAd", query: {period: "primary"},});
  //       }
  //   },
  //   //分配审核员
  //   allocateAuditor(){
  //     allocateSchoolAuditor(this.prefixDeptCode).then((res) => {
  //       this.$message.success("操作成功");
  //       this.getTableData();
  //     })
  //   },
  //   // 导出报名信息
  //   exportEnrollInfo() {
  //       this.$download(
  //           `${pref}${this.prefixDeptCode}/biz/excelConfig/exportExcelPriMiddle`,
  //           this.search,
  //           "xlsx",
  //           '民办小学报名列表',
  //
  //           // "导出报名信息.xlsx"
  //       ).then((res) => {
  //         this.$message.success("导出成功");
  //       });
  //   },
  //   // 多选
  //   handleSelectionChange(val) {
  //     this.multipleSelection = val;
  //   },
  //   // 下载入户调查单
  //   exportHouseholdSurvey() {
  //     let checkList = this.multipleSelection.map((item) => item.studentBaseId);
  //     let params = {search: this.search, list: checkList};
  //     this.$download(
  //         `${pref}${this.prefixDeptCode}/biz/recruitStudent/batchExportHouseholdSurvey`,
  //         params,
  //         "zip",
  //         "下载入户调查单.zip"
  //     ).then((res) => {
  //       this.$message.success("下载成功");
  //     });
  //   },
  //   // 批量审核通过
  //   batchAudit() {
  //     let checkList = this.multipleSelection.map((item) => item.studentBaseId);
  //     let params = {
  //       search: Object.assign({}, this.search, {
  //         pageSize: undefined,
  //         pageNumber: undefined,
  //       }),
  //       list: checkList,
  //     };
  //     let msg = "";
  //     if(this.prefixDeptCode==='130532') {
  //       msg =
  //           checkList.length == 0
  //               ? "确认完成审核？所有学校待审核的学生，学校审核状态都会改为已通过，请谨慎使用。"
  //               : "确认为已选学生完成审核吗";
  //     }else {
  //       msg =
  //           checkList.length == 0
  //               ? "确认完成审核？所有初审已通过的学生，教育局审核状态都会改为已通过，请谨慎使用。"
  //               : "确认为已选学生完成审核吗";
  //     }
  //     this.$confirm(msg, "提示", {
  //       confirmButtonText: "确定",
  //       cancelButtonText: "取消",
  //       type: "warning",
  //     }).then(() => {
  //       batchAudit(params, this.prefixDeptCode).then((res) => {
  //         this.$message.success("操作成功");
  //         this.getTableData();
  //       });
  //     });
  //   },
  //   // 公示录取结果
  //   publicAdmission() {
  //     let checkList = this.multipleSelection.map((item) => item.studentBaseId);
  //     let params = {
  //       search: Object.assign({}, this.search, {
  //         pageSize: undefined,
  //         pageNumber: undefined,
  //       }),
  //       list: checkList,
  //     };
  //     publicity(params, this.prefixDeptCode).then((res) => {
  //       this.$confirm(res, "提示", {
  //         confirmButtonText: "确定",
  //         cancelButtonText: "取消",
  //         type: "warning",
  //       }).then(() => {
  //         publicAdmissionResults(params, this.prefixDeptCode).then((res) => {
  //           this.$message.success("操作成功");
  //           this.getTableData();
  //         });
  //       });
  //     });
  //   },
  //   // 发送录取通知书
  //   notice() {
  //     this.switchModal("notice", true);
  //     this.$nextTick(() => {
  //       this.$refs.noticeForm.resetFields();
  //       this.noticeForm.content = "";
  //       this.noticeForm.num = "";
  //       let params = {
  //         search: Object.assign({}, this.search, {
  //           pageSize: undefined,
  //           pageNumber: undefined,
  //         }),
  //         type: 1,
  //         content: this.noticeForm.content,
  //       };
  //       sendRequisition(params, this.prefixDeptCode).then((res) => {
  //         this.noticeForm.num = res;
  //       });
  //     });
  //   },
  //   // 发送录取通知书确定
  //   confirmNotice() {
  //     this.$refs.noticeForm.validate((valid) => {
  //       if (valid) {
  //         let params = {
  //           search: Object.assign({}, this.search, {
  //             pageSize: undefined,
  //             pageNumber: undefined,
  //           }),
  //           type: 2,
  //           content: this.noticeForm.content,
  //         };
  //         sendRequisition(params, this.prefixDeptCode).then((res) => {
  //           this.$confirm("录取通知书成功发送 " + res + " 人", "提示", {
  //             confirmButtonText: "关闭",
  //             showCancelButton: false,
  //             type: "success",
  //             closeOnClickModal: false,
  //             showClose: false,
  //             closeOnPressEscape: false,
  //           }).then(() => {
  //             this.switchModal("notice", false);
  //             this.getTableData();
  //           });
  //         });
  //       }
  //     });
  //   },
  //   // 详情
  //   detail(row, index) {
  //     this.curStuDetail = row;
  //     this.index = index;
  //     this.modal.stuDetail = true;
  //     this.getAuditRecord();
  //     // this.getAuditStatus();
  //   },
  //   // 查询审核情况
  //   getAuditRecord() {
  //     this.auditRecordList = [];
  //     this.loadingAuditRecord = true;
  //     getAuditRecord(
  //         {key: this.curStuDetail.studentBaseId},
  //         this.prefixDeptCode
  //     )
  //         .then((res) => {
  //           this.auditRecordList = res;
  //         })
  //         .finally(() => {
  //           this.loadingAuditRecord = false;
  //         });
  //   },
  //   // 查询审核情况
  //   // getAuditStatus() {
  //   //   this.auditStatusList = [];
  //   //   this.loadingAuditStatus = true;
  //   //   getAuditStatus(
  //   //     { key: this.curStuDetail.studentBaseId },
  //   //     this.prefixDeptCode
  //   //   )
  //   //     .then((res) => {
  //   //       this.auditStatusList = res;
  //   //     })
  //   //     .finally(() => {
  //   //       this.loadingAuditStatus = false;
  //   //     });
  //   // },
  //   // 详情 - 下一条
  //   nextEnrollDetail() {
  //     // 当前是不可翻页的页码（最后一页）
  //     if (
  //         this.search.pageNumber * this.search.pageSize > this.total ||
  //         this.search.pageNumber * this.search.pageSize == this.total
  //     ) {
  //       if (this.index < this.tableData.records.length - 1) {
  //         this.index += 1;
  //         this.curStuDetail = this.tableData.records[this.index];
  //         this.getAuditRecord();
  //       } else {
  //         this.$message.error("已是最后一条数据");
  //       }
  //     }
  //     // 当前是可翻页的页码
  //     else {
  //       if (this.index < this.tableData.records.length - 1) {
  //         this.index += 1;
  //         this.curStuDetail = this.tableData.records[this.index];
  //         this.getAuditRecord();
  //       } else {
  //         this.search.pageNumber += 1;
  //         getEnrollList(this.search, this.prefixDeptCode).then((res) => {
  //           this.tableData = res;
  //           this.curStuDetail = this.tableData.records[0];
  //           this.index = 0;
  //           this.getAuditRecord();
  //         });
  //       }
  //     }
  //   },
  //   // 详情 - 上一条
  //   prevEnrollDetail() {
  //     // 当前是不可翻页的页码（最后一页）
  //     if (
  //         this.search.pageNumber * this.search.pageSize > this.total ||
  //         this.search.pageNumber * this.search.pageSize == this.total
  //     ) {
  //       if (this.index > 0) {
  //         this.index -= 1;
  //         this.curStuDetail = this.tableData.records[this.index];
  //         this.getAuditRecord();
  //       } else {
  //         this.$message.error("已是第一条数据");
  //       }
  //     }
  //     // 当前是可翻页的页码
  //     else {
  //       if (this.index > 0) {
  //         this.index -= 1;
  //         this.curStuDetail = this.tableData.records[this.index];
  //         this.getAuditRecord();
  //       } else {
  //         this.search.pageNumber -= 1;
  //         getEnrollList(this.search, this.prefixDeptCode).then((res) => {
  //           this.tableData = res;
  //           this.index = this.tableData.records.length - 1;
  //           this.curStuDetail = this.tableData.records[this.index];
  //           this.getAuditRecord();
  //         });
  //       }
  //     }
  //   },
  //   // 详情 - 关闭
  //   stuDetailClose() {
  //     this.switchModal("stuDetail", false);
  //     this.index = 0;
  //   },
  //   // 撤销公示结果
  //   revokePublic(row) {
  //     this.switchModal("revokePublic", true);
  //     this.$nextTick(() => {
  //       this.$refs.revokePublicForm.resetFields();
  //       this.revokePublicForm.id = row.studentBaseId;
  //     });
  //   },
  //   // 撤销公示结果确认
  //   confirmRevokePublic() {
  //     this.$refs.revokePublicForm.validate((valid) => {
  //       if (valid) {
  //         let params = this.revokePublicForm;
  //         revokePublic(params, this.prefixDeptCode).then((res) => {
  //           this.$message.success("操作成功");
  //           this.switchModal("revokePublic", false);
  //           this.getTableData();
  //         });
  //       }
  //     });
  //   },
  //   // 删除报名信息
  //   deleteSign(row) {
  //     this.switchModal("deleteSign", true);
  //     this.$nextTick(() => {
  //       this.$refs.deleteSignForm.resetFields();
  //       this.deleteSignForm.key = row.studentBaseId;
  //     });
  //   },
  //   // 删除报名信息确认
  //   confirmDeleteSign() {
  //     this.$refs.deleteSignForm.validate((valid) => {
  //       if (valid) {
  //         let params = this.deleteSignForm;
  //         privateDeleteEnrollInfo(params, this.prefixDeptCode).then((res) => {
  //           this.$message.success("操作成功");
  //           this.switchModal("deleteSign", false);
  //           this.getTableData();
  //         });
  //       }
  //     });
  //   },
  //   // 查询功能开关状态 小学
  //   getFuncStatusInfo(params) {
  //     getFuncStatus({key: params}, this.prefixDeptCode).then((res) => {
  //       if (params == "DELETE_STUDENT_PRIVATE") {
  //         this.funcStatusInfo.deleteStatus = res;
  //       } else if (params == "PUBLIC_STUDENT_PRIVATE") {
  //         this.funcStatusInfo.publicStatus = res;
  //       } else if (params == "NOTICE_STUDENT_PRIVATE") {
  //         this.funcStatusInfo.noticeStatus = res;
  //       }
  //     });
  //   },
  //   // 获取审核情况列表中指定的报名详情
  //   getEnrollRecords(row) {
  //     this.curStuRecordId = row.id
  //     this.switchModal("enrollRecord", true)
  //   }
  // },
};
</script>

<style lang="scss" scoped>
.tips {
  padding-left: 30px;
  padding-bottom: 30px;
  font-size: 12px;
}

.notice-num {
  font-size: 16px;
}

.signNum {
  color: #606266;
  font-size: 13px;
  height: 28px;
  line-height: 28px;
}
</style>
