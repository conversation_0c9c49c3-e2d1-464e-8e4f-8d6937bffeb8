<template>
  <div>
    <el-tabs
      tab-position="left"
      :stretch="true"
      type="border-card"
      v-model="deptCode"
    >
      <el-tab-pane
        v-for="item in deptOptions"
        :label="item.deptName"
        :name="item.deptCode"
        :key="item.id"
      >
        <ShiwuquExport
          :deptName="item.deptName"
          :deptId="item.deptId"
          :deptCode="item.deptCode"
          v-if="item.deptCode == deptCode && item.isFive == '1'"
        />
        <ShiwuquExport
            :deptName="item.deptName"
            :deptId="item.deptId"
            :deptCode="item.deptCode"
            v-else-if="item.deptCode == deptCode && deptCode=='130426'"
        />
        <CommonExport :deptId="item.deptId" :deptCode="item.deptCode" v-else-if="item.deptCode == deptCode && item.isFive != '1'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getDepts } from "@/api/common";
import ShiwuquExport from "@/views/shiWuQu/signExportSet";
import CommonExport from "@/views/signExportSet/CommonExport";
export default {
  name: "signExportSet",
  components: { ShiwuquExport, CommonExport },
  data() {
    return {
      deptCode: "",
      deptOptions: [],
    };
  },
  created() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      let depts = await getDepts({ level: 2 });
      this.deptOptions=depts
   /*   this.deptOptions = depts.filter((item) => item.deptCode != "130426"); // 过滤涉县*/
      this.deptCode = this.deptOptions[0].deptCode;
    },
  },
};
</script>

<style scoped lang="scss">
.el-tabs {
  height: calc(100vh - var(--header-height) - 35px);
}
</style>
