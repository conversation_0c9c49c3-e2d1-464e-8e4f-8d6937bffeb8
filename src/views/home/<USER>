<template>
  <div>
    <she-xian-home v-if="role == 'COUNTY_ADMIN' &&prefixDeptCode=='130426'&& !isFive " />
    <CommonHome v-else-if="role == 'COUNTY_ADMIN' && !isFive &&prefixDeptCode!='130426'" />

    <ShiWuQuHome v-else-if="role == 'COUNTY_ADMIN' && isFive" />
    <div v-else-if="role == 'SCHOOL'">{{schoolName}}</div>
    <div v-else>邢台区县招生</div>
  </div>
</template>

<script>
import CommonHome from "./components/CommonHome";
import ShiWuQuHome from "./components/ShiWuQuHome";
import sheXianHome from "@/views/home/<USER>/sheXianHome";
export default {
  name: "home",
  components: { CommonHome, ShiWuQuHome ,sheXianHome},
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      role: this.$store.getters.role,
      isFive: this.$store.getters.isFive,
      schoolName: ""
    };
  },
  created() {
    if(this.role == 'SCHOOL') {
      this.schoolName = this.$store.getters.userInfo.deptName
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
