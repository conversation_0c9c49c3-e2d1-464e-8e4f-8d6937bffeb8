<template>
  <div>
    <ul class="total-num">
      <li>
        <h2>报名人数</h2>
        <span>{{ tableData.studentNum }}人</span>
      </li>
      <li>
        <h2>学生跨区调入</h2>
        <span>{{ tableData.studentFromNum}}人</span>
      </li>
      <li>
        <h2>学生跨区调出</h2>
        <span>{{ tableData.studentToNum}}人</span>
      </li>
<!--      <li>-->
<!--        <h2>报到人数</h2>-->
<!--        <span>{{ tableData.signNum}}人</span>-->
<!--      </li>-->
    </ul>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-button
            size="small"
            type="primary"
            icon="el-icon-view"
            @click="detail"
        >查看详情</el-button
        >
      </div>

    </div>
    <el-table
        :data="tableData.dataList.records"
        border
        stripe
        v-loading="tableLoading"
    >
      <el-table-column
          align="center"
          label="序号"
          type="index"
          width="60"
      >
<!--        <template slot-scope="scope">-->
<!--          <span>{{-->
<!--              scope.$index + (search.pageNumber - 1) * search.pageSize + 1-->
<!--            }}</span>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column
          align="center"
          label="学校名称"
          prop="schoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="计划招生数"
          prop="planNum"
      ></el-table-column>
            <el-table-column
                align="center"
                label="选择拟学校报名人数"
                prop="schoolNum"
            ></el-table-column>
      <el-table-column
          align="center"
          label="不确定拟报名学校人数(含学校退回教育局未重新分配人数)"
          prop="distributeNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="调出人数"
          prop="adjustToNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="调入人数"
          prop="adjustFromNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="录取人数（不包括市级调入人数）"
          prop="publicNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="录取人数（包括市级调入人数）"
      >
        <template slot-scope="scope">
          <span>{{Number(scope.row.publicNum)  + Number(scope.row.spanToNum) }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="市级调出"
          prop="spanFormNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="市级调入"
          prop="spanToNum"
      ></el-table-column>
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="报名人数"-->
<!--          prop="enrollNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="审核通过数(以最终教育局数为准)"-->
<!--          prop="passNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="审核驳回数(以最终教育局数为准)"-->
<!--          prop="notPassNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="调剂数"-->
<!--          prop="adjustNum"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="学校报到数"-->
<!--          prop="signNum"-->
<!--      ></el-table-column>-->
    </el-table>
    <div class="page-container" v-if="pageTotal > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="pageTotal"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import { homeStatistics } from "@/api/statistics";
import {getShuWuQuTongJiList2} from "@/api/shiWuQuShuJuTongJi";
export default {
  name: 'CommonHome',
  mixins: [TableMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      search: {
        keywords: "",
        nature: "",
        period: "",
      },
    };
  },
  computed: {


  },
  created() {

  },
  methods: {

    getTableData() {
      this.tableLoading = true;
      getShuWuQuTongJiList2(this.search,this.prefixDeptCode).then(res=>{
        this.tableData = res
       this.pageTotal =Number(res.dataList.total)
        console.log(res,"res2")
      }).finally(() => {
        this.tableLoading = false;
      });
      // homeStatistics(this.search, this.prefixDeptCode)
      //     .then((res) => {
      //       this.tableData = res;
      //     })
      //     .finally(() => {
      //       this.tableLoading = false;
      //     });
    },
    // 查看详情 - 跳转到数据统计，小学统计
    detail() {
      this.$router.push("/shuJuTongJi/luqu");
    },
  },
};
</script>

<style lang='scss' scoped>
.total-num {
  display: flex;
  border: 1px solid #ccc;
  border-radius: 4px;
  list-style: none;
  margin-bottom: 30px;
  & > li {
    height: 200px;
    flex: 0 0 30%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    & > span {
      padding-top: 40px;
    }
  }
}
</style>
