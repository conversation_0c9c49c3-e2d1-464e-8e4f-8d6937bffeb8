<template>
  <el-menu
    class="sd-el-menu"
    :default-active="activePath"
    :collapse="isCollapse"
    :default-openeds="defaultOpeneds"
    background-color="var(--el-menu-bg-color)"
    text-color="var(--el-menu-text-color)"
    active-text-color="var(--el-menu-active-color)"
    router
    :unique-opened="true"
  >
    <side-bar-item
      v-for="item in routes"
      :key="item.path"
      :menu-item="item"
    ></side-bar-item>
  </el-menu>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "SideBar",
  data() {
    return {
      defaultOpeneds: [],
      role: this.$store.getters.role,
      isFive: this.$store.getters.isFive,
      deptCode: this.$store.getters.deptCode,
      period: this.$store.getters.period,
    };
  },
  computed: {
    ...mapGetters(["isCollapse"]),
    routes() {
      return this.$router.options.routes.filter(
        (item) =>
          item.meta?.hidden == false &&
          item.meta.roles.indexOf(this.role) >= 0 &&
          (item.meta.isFive == undefined || item.meta.isFive == this.isFive) &&
          (item.meta.depts == undefined ||
            item.meta.depts.indexOf(this.deptCode) >= 0) &&
          (item.meta.period == undefined ||
            item.meta.period.indexOf(this.period) >= 0)
      );
    },
    activePath() {
      return this.$route.path;
    },
  },
};
</script>

<style scoped lang="scss">
.sd-el-menu {
  --el-menu-text-color: #e0e0e0;
  --el-menu-hover-text-color: #e0e0e0;
  --el-menu-bg-color: rgb(47, 90, 174);
  --el-menu-hover-bg-color: #023aa9;
  --el-menu-active-color: #ffff00;
  --el-menu-active-bg-color: #3b70d9;
  --el-menu-level: 0;

  border-right: solid 0 #ffffff !important;

  background-color: var(--el-menu-bg-color);

  .el-menu-item.is-active {
    background-color: var(--el-menu-active-bg-color) !important;
  }

  .el-menu-item:hover {
    background-color: var(--el-menu-hover-bg-color) !important;
  }
}
</style>
