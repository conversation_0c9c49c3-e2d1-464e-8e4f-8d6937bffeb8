<template>
	<div class="normal-select">
		<!-- 日期选择 -->
		<el-date-picker 
			v-if="verCode == 2 || verCode == 14"
			v-model="modelData"
			type="date"
			value-format="yyyy-MM-dd"
			:placeholder="phTxt"
		></el-date-picker>
		<!-- 其它下拉选择 -->
		<el-select
			v-else
			clearable
			style="width: 220px;"
			v-model="modelData" 
			:placeholder="phTxt"
			:filterable="isFilterable"
		>
			<el-option v-for="item, idx in dataSource" :key="idx" :label="item.val" :value="item.id"></el-option>
		</el-select>
	</div>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import { getSchoolRange } from "@/api/setting.js"
import { getDepts } from "@/api/user.js"
export default {
	name: 'normal-select',
	data() {
		return {
			modelData: '',
			phTxt: '',
			dataSource: [],
			isFilterable: false,
			searchSchool: {
				deptCode: '',
				keywords: "",
				nature: "",
				period: "",
				status: "",
				level: 3,
				pageNumber: 1,
				pageSize: 9999
			},
			schoolId: ''
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		},
		// 额外参数
		extraConfig: {
			type: Object,
			required: true
		}
	},
	watch: {
		modelData(newV, oldV) {
			if (newV != oldV) {
				this.valChange(newV)
			}
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		}
	},
	created() {

    // console.log("额外参数组件",JSON.stringify(this.extraConfig,null,2))

    if(this.extraConfig){
      this.searchSchool.deptCode = this.extraConfig.deptCode
      // this.searchSchool.nature = this.extraConfig.nature
      this.schoolId = this.extraConfig.schoolId
    }

		this.phTxt = `请选择${ this.itemConfig.fieldName }`
		// inputItemCode为6时开启filterable属性
		this.isFilterable = this.itemConfig.inputItemCode == 6
		
		// infoVerificationCode为3，4，5，9，10，12，13时直接读取字典里的list
		let dicListIdx = [3, 4, 5, 9, 10, 12, 13]
		// infoVerificationCode为6，7，8时需要请求api获取数据
		let qryListIdx = [6, 7, 8]
		if (dicListIdx.indexOf(this.verCode) != -1) {
			this.dataSource = rulesList[this.verCode].list
		} else if (qryListIdx.indexOf(this.verCode) != -1) {
			if (this.verCode == 6) {
				this.primarySchoolList()
			} else if (this.verCode == 7) {
				this.preSchoolList()
			} else if (this.verCode == 8) {
				this.rangeList()
			}
		}
	},
	methods: {
		// emit
		valChange(data) {
			this.modelData = data
			this.$emit('value-change', {
				id: this.itemConfig.fieldId,
				val: data
			})
		},
		// infoVerificationCode == 6时，小学列表
		primarySchoolList() {
			let params = { ...this.searchSchool }
			params.period = 2
			getDepts(params).then(res => {
				res.forEach(v => {
					v.val = v.deptName
				})
				this.dataSource = res
			})
		},
		// infoVerificationCode == 7时，幼儿园列表
		preSchoolList() {
			let params = { ...this.searchSchool }
			params.period = 1
			getDepts(params).then(res => {
				res.forEach(v => {
					v.val = v.deptName
				})
				this.dataSource = res
			})
		},
		// infoVerificationCode == 8时，范围列表
		rangeList() {
			getSchoolRange({
				schoolId: this.schoolId,
				type: 2
			}).then(res => {
				res.forEach(v => {
					v.val = v.rangeName
				})
				this.dataSource = res
			})
		},
	}
}
</script>
