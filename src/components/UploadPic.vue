<template>
  <div>
    <el-upload
      :class="{  hide:isHide }"
      action
      :http-request="request"
      list-type="picture-card"
      :on-change="change"
      :on-preview="preview"
      :on-remove="remove"
      :before-upload="beforeUpload"
      :file-list="fileList"
    >
        <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog title="预览" :visible.sync="isUrl">
      <!-- <img :src="url" width="280" height="390" v-if="isHead"/> -->
      <img :src="url" width="100%" />
    </el-dialog>
    <div class="upload_text"><span v-if="required">*</span>{{ textPic }}</div>
  </div>
</template>

<script>
import { uploaDing, uploadPicture } from "@/utils/upload.js";
export default {
  name: "uploadPic",
  props: {
    textPic: String,
    value: {
      type: String,
      default: "",
    },
    // 是否必传验证
    required: {
      type: Boolean,
      default: false,
    },
    // 是否是上传头像，头像会验证宽高280*390px
    // isHead: {
    // 	type: Boolean,
    // 	default: true
    // },
  },
  data() {
    return {
      fileList: [],
      isHide: false,
      isUrl: false,
      url: "",
    };
  },
  computed: {},
  watch: {
    value(newVal) {
      if (!newVal) {
        this.fileList = [];
        setTimeout(() => {
          this.isHide = false;
        }, 200);
      }
    },
  },
  created() {
    console.log(this.value,"this.value")
  },
  mounted() {
    setTimeout(() => {
      this.getFileList();
    }, 1500);
  },
  methods: {
    getFileList() {
       console.log(this.value,"this.value");
      if (this.value) {
        console.log(this.value);
        this.isHide = true;
        this.fileList.push({
          url: this.value,
        });
      }
    },
    request(param) {
      uploadPicture(param,this.$store.getters.deptCode)
        .then((res) => {
          console.log(res,"res");
          this.$emit("input",  res);
        })
        .catch(() => {});
    },
    change() {
      this.isHide = true;
      this.$emit("onChange");
      // if(this.isHead) {
      // this.$emit("onChangeHead")
      // }
    },
    remove() {
      this.isHide = false;
      this.$emit("input", "");
      this.$emit("onRemove");
      // if(this.isHead) {
      // 	this.$emit("onRemoveHead")
      // }
    },
    preview(file) {
      this.url = file.url;
      this.isUrl = true;
    },
    beforeUpload(file) {
      return uploaDing(file);
    },
  },
};
</script>

<style lang="less"  scoped >
.upload_text {
  padding: 10px 0;
  text-align: center;
  font-size: 12px;
  width: 150px;
  line-height: 16px;
  span {
    color: #f56c6c;
    margin-right: 4px;
    font-size: 14px;
  }
}
/deep/ .el-upload-list--picture-card {
  .el-upload-list__item {
    margin-bottom: 0;
    display: block;
  }
}
  /deep/ .hide .el-upload--picture-card {
    display: none;
  }
</style>
