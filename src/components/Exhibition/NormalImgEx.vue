<template>
	<!-- 报名详情查看：图片 -->
	<div class="normal-img-ex">
		<el-image
			style="width: 148px; height: 148px"
			:src="modelData" 
			:preview-src-list="previewList"
		>
			<div slot="error" class="ph">
				暂无图片
			</div>
		</el-image>
		<div class="key-desc" :class="{ 'is-modify': isModify }">
		  {{ keyDesc }}
		</div>
	</div>
</template>

<script>
import { imgPrefix } from '@/utils/common'
export default {
	name: 'normal-img-ex',
	data() {
		return {
			modelData: '',
			keyDesc: '',
			previewList: [],
      imgPrefix: imgPrefix()
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		},
		// 是否已修改
		isModify() {
			return this.itemConfig.isModify == 1
		}
	},
	created() {
		this.keyDesc = this.itemConfig.fieldName
		if (this.itemConfig.fieldValue) {
			this.modelData = `${ this.imgPrefix }${ this.itemConfig.fieldValue }`
			this.previewList = [this.modelData]
		}
	}
}
</script>

<style>

</style>