<template>
		<!-- 报名详情查看：select字段 -->
	<div class="normal-select-ex">
		<span :class="{ 'is-modify': isModify }">{{ modelData }}</span>
	</div>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import { getSchoolRange } from "@/api/setting.js"
import { getDepts } from "@/api/user.js"
export default {
	name: 'normal-select-ex',
	data() {
		return {
			modelData: '',
			fieldVal: '',
			// stuEnrollNature: '',
			enrollSchoolId: ''
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		},
		listItemConfig: {
			type: Object,
			required: true
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		},
		// 是否已修改
		isModify() {
      console.log(this.itemConfig)
			return this.itemConfig.isModify == 1
		}
	},
	created() {
		// console.log()
		// this.stuEnrollNature = this.listItemConfig.nature == '乡镇' ? 1 : 2
		this.enrollSchoolId = this.listItemConfig.enrollSchoolId
		// this.enrollSchoolId = '1798651984557735937'
		this.fieldVal = this.itemConfig.fieldValue
		this.modelData = this.fieldVal
		// infoVerificationCode为3，4，5，9，10，12，13时直接读取字典里的list
		let dicListIdx = [3, 4, 5, 9, 10, 12, 13]
		// infoVerificationCode为6，7，8时需要请求api获取数据
		let qryListIdx = [6, 7, 8]
		if (dicListIdx.indexOf(this.verCode) != -1) {
			let matchItem = rulesList[this.verCode].list.filter(v => v.id == this.fieldVal)
			if (matchItem.length > 0) {
				this.modelData = matchItem[0].val
			} else {
				this.modelData = ''
			}
		} else if (qryListIdx.indexOf(this.verCode) != -1) {
			if (this.verCode == 6) {
				this.primarySchoolList()
			} else if (this.verCode == 7) {
				this.preSchoolList()
			} else if (this.verCode == 8) {
				this.rangeList()
			}
		}
	},
	methods: {
		// infoVerificationCode == 6时，小学列表
		primarySchoolList() {
			getDepts({
				keywords: "",
				nature: '',
				// 学段
				period: 2,
				deptCode: this.$store.getters.deptCode,
				type: 1,
				pageNumber: 1,
				pageSize: 9999
			}).then(res => {
				res.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.deptName
					}
				})
			})
		},
		// infoVerificationCode == 7时，幼儿园列表
		preSchoolList() {
			getDepts({
				keywords: "",
				nature: '',
				// 学段
				period: 1,
				deptCode: this.$store.getters.deptCode,
				type: 1,
				pageNumber: 1,
				pageSize: 9999
			}).then(res => {
				res.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.deptName
					}
				})
			})
		},
		// infoVerificationCode == 8时，范围列表
		rangeList() {
			getSchoolRange({
				schoolId: this.enrollSchoolId,
				type: 2
			}).then(res => {
				res.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.rangeName
					}
				})
			})
		},
	}
}
</script>
