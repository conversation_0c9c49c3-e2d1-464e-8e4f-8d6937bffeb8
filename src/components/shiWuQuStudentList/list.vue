<template>
  <div>
    <div class="sd-option-container">
<!--class="sd-search"-->
      <div class="sd-search">

        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.signId"
                placeholder="报名ID"
                class="sd-w-200"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.keywords"
                placeholder="姓名或身份证号"
                class="sd-w-200"
                clearable
            ></el-input>
          </el-form-item>
<!--          <el-form-item  >-->
<!--            <el-select clearable size="small" v-model="search.schoolId" placeholder="报名学校" >-->
<!--              <el-option-->
<!--                  v-for="item in schoolList"-->
<!--                  :key="item.id"-->
<!--                  :label="item.deptName-->
<!--"-->
<!--                  :value="item.id"-->
<!--              >-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item  >-->
<!--&lt;!&ndash;            || role == 'AUDITOR'&ndash;&gt;-->
<!--            <el-select clearable size="small" v-model="search.adjustSchoolId" placeholder="调剂学校">-->
<!--              <el-option-->
<!--                  v-for="item in schoolList"-->
<!--                  :key="item.id"-->
<!--                  :label="item.deptName-->
<!--"-->
<!--                  :value="item.id"-->
<!--              >-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.signType"-->
<!--                placeholder="类别"-->
<!--                class="sd-w-150"-->
<!--            >-->
<!--              <el-option label="房户一致" :value="1"></el-option>-->
<!--              <el-option label="户口" :value="2"></el-option>-->
<!--              <el-option label="房产" :value="3"></el-option>-->
<!--              <el-option label="外来经商务工" :value="4"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-select
                size="small"
                v-model="search.type"
                placeholder="报名类型"
                class="sd-w-150"
                clearable
            >
              <el-option label="选择拟报名学校" :value="1"></el-option>
              <el-option label="不确定拟报名学校" :value="2"></el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.schoolStatus"-->
<!--                placeholder="学校审核状态"-->
<!--                class="sd-w-150"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="审核中" :value="0"></el-option>-->
<!--              <el-option label="已通过" :value="1"></el-option>-->
<!--              <el-option label="已驳回-修改信息" :value="2"></el-option>-->
<!--              <el-option label="已驳回-重新报名" :value="3"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.eduStatus"-->
<!--                placeholder="教育局审核状态"-->
<!--                class="sd-w-150"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="审核中" :value="0"></el-option>-->
<!--              <el-option label="已通过" :value="1"></el-option>-->
<!--              <el-option label="已驳回-修改信息" :value="2"></el-option>-->
<!--              <el-option label="已驳回-重新报名" :value="3"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.houseStatus"-->
<!--                placeholder="房管局审核状态"-->
<!--                class="sd-w-130"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="0"></el-option>-->
<!--              <el-option label="通过" :value="1"></el-option>-->
<!--              <el-option label="不通过" :value="2"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.policeStatus"-->
<!--                placeholder="公安审核状态"-->
<!--                class="sd-w-130"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="0"></el-option>-->
<!--              <el-option label="通过" :value="1"></el-option>-->
<!--              <el-option label="不通过" :value="2"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--                size="small"-->
<!--                v-model="search.signStatus"-->
<!--                placeholder="是否报到"-->
<!--                class="sd-w-130"-->
<!--                clearable-->
<!--            >-->
<!--              <el-option label="未报到" :value="2"></el-option>-->
<!--              <el-option label="已报到" :value="1"></el-option>-->
<!--              <el-option label="待报到" :value="0"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-select
                size="small"
                v-model="search.spanStatus"
                placeholder="跨区调剂状态"
                class="sd-w-130"
                clearable
            >
              <el-option label="未调剂" :value="1"></el-option>
              <el-option label="已调剂" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData"
            ></el-button>
          </el-form-item>
<!--          <el-form-item>-->
<!--            <el-button-->
<!--                size="small"-->
<!--                type="primary"-->
<!--                icon="el-icon-plus"-->
<!--                v-if="role == 'COUNTY_ADMIN' || role == 'SCHOOL'"-->
<!--                @click="add"-->
<!--            >添加报名信息</el-button-->
<!--            >-->
<!--            <el-button-->
<!--                size="small"-->
<!--                type="warning"-->
<!--                icon="el-icon-download"-->
<!--                @click="exportData"-->
<!--                v-if="-->
<!--                role == 'COUNTY_ADMIN'  || role == 'SCHOOL'|| role == 'AUDITOR'-->
<!--              "-->
<!--            >导出报名信息</el-button-->
<!--            >-->
<!--&lt;!&ndash;            || role == 'AUDITOR'&ndash;&gt;-->
<!--&lt;!&ndash;            <el-button&ndash;&gt;-->
<!--&lt;!&ndash;                size="small"&ndash;&gt;-->
<!--&lt;!&ndash;                type="warning"&ndash;&gt;-->
<!--&lt;!&ndash;                icon="el-icon-download"&ndash;&gt;-->
<!--&lt;!&ndash;                v-if="&ndash;&gt;-->
<!--&lt;!&ndash;                role == 'COUNTY_ADMIN' || role == 'AUDITOR' || role == 'SCHOOL'&ndash;&gt;-->
<!--&lt;!&ndash;              "&ndash;&gt;-->
<!--&lt;!&ndash;            >下载入户调查单</el-button&ndash;&gt;-->
<!--&lt;!&ndash;            >&ndash;&gt;-->
<!--            <el-button-->
<!--                size="small"-->
<!--                type="success"-->
<!--                icon="el-icon-check"-->
<!--                v-if="role == 'COUNTY_ADMIN'"-->
<!--                @click="piLiangShenHeTongGuo"-->
<!--            >批量审核通过</el-button-->
<!--            >-->
<!--            &lt;!&ndash; <el-button-->
<!--                size="small"-->
<!--                type="primary"-->
<!--                icon="el-icon-s-promotion"-->
<!--                v-if="role == 'COUNTY_ADMIN'"-->
<!--                @click="gongShiLuQuJieGuo"-->
<!--            >公示录取结果</el-button-->
<!--            > &ndash;&gt;-->
<!--            <el-button-->
<!--                size="small"-->
<!--                type="primary"-->
<!--                icon="el-icon-s-promotion"-->
<!--                v-if="role == 'SCHOOL'"-->
<!--                @click="faSongLuQu"-->
<!--            >发送录取通知书</el-button-->
<!--            >-->
<!--          </el-form-item>-->
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe >
      <el-table-column
          align="center"
          label="报名ID"
          prop="signId"
          width="90"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="idCard"
          width="150"
      ></el-table-column>
<!--      <el-table-column align="center" label="报名类别" prop="signType">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.signType == 1 ? "房户一致" :scope.row.signType == 2?"户口":scope.row.signType == 3?'房产':scope.row.signType == 4?'':'外来务工经商' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="购房时间"-->
<!--          prop="aId"-->
<!--          width="150"-->
<!--      ></el-table-column>-->
      <el-table-column
          align="center"
          label="报名学校"
          prop="schoolName"
      ></el-table-column>
<!--      <el-table-column align="center" label="是否优抚对象" prop="careStatus">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.careStatus == 1 ? "是" :scope.row.careStatus == 0?"否":'' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="调剂学校"-->
<!--          prop="adjustSchoolName"-->
<!--      ></el-table-column>-->
      <el-table-column
          align="center"
          label="报名时间"
          prop="createTime"
          width="150"
      ></el-table-column>
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="房产审核"-->
<!--          prop="houseStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.houseStatus == 1 ? "通过" :scope.row.houseStatus == 0?"待审核":scope.row.houseStatus == 2?"未通过":'' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="公安审核"-->
<!--          prop="policeStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.policeStatus == 1 ? "通过" :scope.row.policeStatus == 0?"待审核":scope.row.policeStatus == 2?"未通过":'' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="学校审核状态"-->
<!--          prop="schoolStatus"-->
<!--          width="130"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{scope.row.schoolStatus == 1 ? "通过" :scope.row.schoolStatus == 0?"审核中":scope.row.schoolStatus == 2?"驳回修改信息":scope.row.schoolStatus == 3?'驳回重新报名':''}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="教育局审核状态"-->
<!--          prop="eduStatus"-->
<!--          width="130"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{scope.row.eduStatus == 1 ? "通过" :scope.row.eduStatus == 0?"审核中":scope.row.eduStatus == 2?"驳回修改信息":scope.row.eduStatus == 3?'驳回重新报名':''}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column
          align="center"
          label="跨区调剂状态"
          prop="eduStatus"
          width="130"
      >
        <template slot-scope="scope">
          <span>{{scope.row.spanStatus == 1 ? "未调剂" :scope.row.spanStatus==2?"已调剂":''}}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="跨区调剂原因"
          prop="content"
          width="130"
      >
      </el-table-column>
<!--      <el-table-column-->
<!--      align="center"-->
<!--      label="对口类型"-->
<!--      prop="signTypeStr"-->
<!--      >-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="是否公示"-->
<!--          prop="publicStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <template>{{scope.row.publicStatus==0?'未公布':scope.row.publicStatus==1?'公布':''}}</template>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          align="center"-->
<!--          label="是否报到"-->
<!--          prop="signStatus"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <template>{{scope.row.signStatus==0?'待报到':scope.row.signStatus==1?'报到':scope.row.signStatus==2?'未报到':''}}</template>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column align="center" label="备注" prop="mark"></el-table-column>-->
<!--      <el-table-column align="center" label="优抚类别" prop="careType"></el-table-column>-->
      <el-table-column align="center" label="操作" width="300" fixed="right">
        <template slot-scope="{ row }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail(row)"
          >详情</el-link
          >
<!--          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="buChongCaiLiao(row)"
          >补充材料</el-link
          >
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="liuZhuan(row)"
          >流转记录</el-link
          >
          <el-link
              icon="el-icon-close"
              type="warning"
              :underline="false"
              @click="adjust(row)"
          >调剂</el-link
          >-->
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="$pageSizes"
      >
<!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
    <el-dialog
        title="学生报名信息详情"
        width="800px"
        append-to-body
        :show-close="false"
        :visible.sync="modal.detail"
        :close-on-click-modal="false"
        center
    >
      <div>
        <component
            v-if="modal.detail"
            :ref="'detail'"
            :is="'detail'"
            :row="row"
            :data=null
            :mode=null
            :data-row="selectRow?selectRow:{}"
            :deptCode="deptCode"
            @searchs="searchs"

        />
<!--        @save-complete="dialogFormComponent.saveComplete"-->
      </div>
      <div class="flex-center" style="margin-top:20px ">
        <el-button
            type="default"
            @click="fanHui"
            size="small"
        >返回</el-button
        >
<!--        <el-button-->
<!--            type="primary"-->
<!--            @click="xiaYiTiao"-->
<!--            :loading="saveLoading"-->
<!--            size="small"-->
<!--        >下一条</el-button-->
<!--        >-->
<!--        <el-button-->
<!--            type="primary"-->
<!--            @click="tiaoJi"-->
<!--            :loading="saveLoading"-->
<!--            size="small"-->
<!--            v-if="role == 'COUNTY_ADMIN' "-->
<!--        >调剂</el-button-->
<!--        >-->
<!--        || role == 'AUDITOR'-->
      </div>

    </el-dialog>
    <!--补充材料-->
    <el-dialog
        title="补充材料"
        :visible.sync="buChongShow"
        width="60%"
    >
      <div v-if="imgList.length>0" style="display: flex;justify-content: space-between">
<!--        <span>{{imgQianZhui+item}}</span>-->
        <el-image v-for="(item, index) in imgList"
            style="width: 200px; height: 200px"
            :src="imgQianZhui+item"
            :preview-src-list="[imgQianZhui+item]"
            :key="index"
        ></el-image>
      </div>

    </el-dialog>
    <el-dialog
        title="调剂"
        :visible.sync="tiaoJiShow"
        width="500px"
    >
      <el-form
          :model="tiaoJiXinXi"
          ref="tiaoJiXinXi"
          :rules="tiaoJiXinXiRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item label="报名ID：">{{ tiaoJiXinXi.signId }}</el-form-item>
        <el-form-item label="学生姓名：">{{
            tiaoJiXinXi.studentName
          }}</el-form-item>
        <el-form-item label="身份证号：">{{
          tiaoJiXinXi.idCard
        }}</el-form-item>
        <el-form-item label="报名学校：">{{
            tiaoJiXinXi.schoolName
          }}</el-form-item>

        <el-form-item prop="toDeptId" label="调剂区县选择：">
          <el-select
              v-model="tiaoJiXinXi.toDeptId"
              style="width: 300px"
              @change="tiaoJiQuXian"
              clearable
          >
            <el-option
                v-for="item in tiaoJiQuXianList"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="toSchoolId" label="调剂学校选择：">
          <el-select
              v-model="tiaoJiXinXi.toSchoolId"
              style="width: 300px"
              clearable
          >
            <el-option
                v-for="item in tiaoJiSchoolList"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="content" label="调剂原因：">
          <el-input type="textarea" style="width:300px" placeholder="请输入调剂原因" v-model="tiaoJiXinXi.content"></el-input>
        </el-form-item>
      </el-form>
<!--      <div style="display:flex;width: 100%;justify-content: space-between"><span>ID:{{ tiaoJiXinXi.signId }}</span><span>学生姓名:{{tiaoJiXinXi.studentName}}</span><span>报名学校:{{tiaoJiXinXi.schoolName}}</span> </div>-->
<!--      <p>调剂区县选择：</p>-->
<!--      <div>-->
<!--        <el-select-->
<!--            v-model="tiaoJiXinXi.adjustQuXianId"-->
<!--            placeholder="选择调剂区县"-->
<!--            clearable-->
<!--            filterable-->
<!--            @change="tiaoJiQuXian"-->
<!--        >-->
<!--          <el-option-->
<!--              v-for="item in tiaoJiQuXianList"-->
<!--              :key="item.id"-->
<!--              :label="item.deptName"-->
<!--              :value="item.id"-->
<!--          >-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </div>-->
<!--      <p>调剂学校选择：</p>-->
<!--      <div>-->
<!--        <el-select-->
<!--            v-model="tiaoJiXinXi.adjustSchoolId"-->
<!--            placeholder="选择调剂学校"-->
<!--            clearable-->
<!--            filterable-->
<!--        >-->
<!--          <el-option-->
<!--              v-for="item in tiaoJiSchoolList"-->
<!--              :key="item.id"-->
<!--              :label="item.deptName"-->
<!--              :value="item.id"-->
<!--          >-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </div>-->
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="tiaoJi1"
      >确 定</el-button>
    </span>
    </el-dialog>
    <!--流转记录-->
    <el-dialog
        title="流转记录"
        :visible.sync="liuZhuanShow"
        width="60%"
    >
      <el-table
          :data="liuZhuanData"
          style="width: 100%"
      >
          <el-table-column
              v-if="role=='CITY_ADMIN'"
              align="center"
              label="操作账号"
              prop="creatorName"
              width="140"
          ></el-table-column>
          <el-table-column
              v-if="role=='CITY_ADMIN'"
              align="center"
              label="操作人绑定微信昵称"
              prop="nickName"
              width="160"
          ></el-table-column>
          <!--      <el-table-column-->
          <!--          align="center"-->
          <!--          label="绑定微信号"-->
          <!--          prop=""-->
          <!--          width="140"-->
          <!--      ></el-table-column>-->
          <el-table-column
              v-if="role=='CITY_ADMIN'"
              align="center"
              label="操作时间"
              prop="createTime"
              width="140"
          ></el-table-column>
          <el-table-column
              align="center"
              label="报名ID"
              prop="signId"
              width="140"
          ></el-table-column>
          <el-table-column
              align="center"
              label="学生姓名"
              prop="studentName"
              width="120"
          >
          </el-table-column>
          <el-table-column
              align="center"
              label="身份证号"
              prop="idCard"
              width="170"
          ></el-table-column>
          <el-table-column
              align="center"
              label="多胞胎数量"
              prop="tiwnsNum"
              width="85"
          ></el-table-column>
          <el-table-column
              align="center"
              label="原报名学校"
              prop="fromSchoolName"
              width="180"
          >
          </el-table-column>

          <el-table-column
              align="center"
              label="原报名区县"
              prop="fromDeptName"
              width="180"
          ></el-table-column>
          <!--          <el-table-column-->
          <!--            align="center"-->
          <!--            label="调剂状态"-->
          <!--            prop=""-->
          <!--            width="180"-->
          <!--        ></el-table-column>-->
          <!--          <el-table-column-->
          <!--              align="center"-->
          <!--              label="调剂区县"-->
          <!--              prop=""-->
          <!--              width="180"-->
          <!--          ></el-table-column>-->
          <el-table-column
              align="center"
              label="调入学校"
              prop="toSchoolName"
              width="180"
          ></el-table-column>
          <el-table-column
              align="center"
              label="调剂原因"
              prop="content"
              width="180"
          ></el-table-column>
      </el-table>
      <div class="page-container" v-if="liuZhuanTotal > 0">
        <el-pagination
            background
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page.sync="liuZhuanForm.pageNumber"
            layout="total, prev, pager, next, sizes"
            :page-sizes="$pageSizes"
            :total="liuZhuanTotal"
        >
        </el-pagination>
      </div>
      <span
          slot="footer"
          class="dialog-footer"
      >
            <el-button @click="liuZhuanShow = false">取 消</el-button>
          </span>
    </el-dialog>
<!--    &lt;!&ndash; 详情 &ndash;&gt;-->
<!--    <el-dialog :visible.sync="modal.detail"> </el-dialog>-->
<!--    &lt;!&ndash; 详情 &ndash;&gt;-->
<!--    <el-dialog :visible.sync="modal.add"> </el-dialog>-->
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import detail from "@/components/shiWuQuStudentList/detail";
import { getList } from "@/api/enrollment";
import {
  getshiWuQuList,
  baoMingPiliangShenHeTongGuo,
  baoMingShenHeBuTongGuo,
  baoMingShenHeTongGuo,
  studentTiaoJiao,
  gongShiLuQuJieGuo,
  tiaoJiaoXueXiaoList,
  youFuLeiXingList,
  addYouFuLeiXing,
  getFaSongLuQuTongZhiRenShu,
  gongShiLuQuRenShu,
  faSongLuQuTongZhi,
  xuShengBaoDao,
    setDuiKouLeiBie
} from "@/api/shiWuQuBaoMingLieBiao";
import {pref} from "@/utils/common";
import {getDepts} from "@/api/common";
import {getKuaQuBuChong, kuaQuJiLu, shiTiaoChu} from "@/api/tiaoChu";
export default {
  mixins: [ ModalMixin],
  props:{
    deptCode:{
      type:String,
      default:''
    },
    deptId:{
      type:String,
      default: ''
    }
  },
  //${this.deptCode}
  data() {
    return {
      imgQianZhui:`${process.env.VUE_APP_BASE_API}/user-api`,
      baoDaoStatus:'',
      baoDaoShow:false,
      faSongLuQuShow:false,
      liuZhuanData:[],
      liuZhuanTotal:0,
      luQuTongZhi:{
        content:'',
        studentIds:[]
      },
      youFu:{
        content:'',
        id:''
      },
      id:'',//学生ID
      mark:'',//学生审核通过备注
      status:'',//学生驳回类型
      reason:'',
      selectRow:{},
      youFuShow:false,
      tiaoJiSchoolList:[],
      tiaoJiQuXianList:[],
      imgList:[],
      tiaoJiXinXi:{},
      beiZhuShow:false,
      studentIds:[],
      idList:[],
      prefixDeptCode: this.deptCode,
      radio:1,
      tiaoJiShow:false,
      boHuiYuanYin:1,
      dialogVisible:false,
      row:'',
      tableData:{},
      saveLoading: false,
      role: this.$store.getters.role,
      total:'',
      tiaoJiXinXiRules:{
        toDeptId: [{
          required: true,
          message: "请选择调剂区县",
          trigger: "change",
        },],
        toSchoolId: [
          {
            required: true,
            message: "请选择调剂学校",
            trigger: "change",
          },
        ],
        content: [{
          required: true,
          message: "请输入调剂原因",
          trigger: "blur",
        },
          {required: true, min: 2, max: 500, message: '请输入2-500个文字', trigger: 'blur'}
        ]
      },
      search: {
        pageNumber: 1,
        pageSize: 10,
        // type:1,
        // schoolType: 1,
        // nature:2,
        period:2
      },
      schoolList:[],
      youFuLeiXingList:[

      ],
      modal: {
        add: false,
        detail: false,
      },
      baodao:{

      },
      duiKouShow:false,
      duiKouStatus:'',
      buChongShow:false,
      liuZhuanShow:false,
      liuZhuanForm:{
        pageNumber: 1,
        pageSize: 10,
      },
    };
  },
components:{detail},
  created() {
    console.log(this.deptId,"deptId")
     this.getTableData()
    this.getTableData1()
  // if(this.role=="COUNTY_ADMIN"){
  //   youFuLeiXingList({},this.prefixDeptCode).then(res=>{
  //     this.youFuLeiXingList=res
  //     console.log(res,"res")
  //   })
  // }

    // if(this.role!="SCHOOL"){
       this.getSchoolList()
    // }
  },
  methods: {
    handleSizeChange1(size) {
      this.liuZhuanForm.pageSize = size
      this.liuZhuanForm.pageNumber = 1
      this.liuZhuan1()
    },
    handleCurrentChange1(page) {
      this.liuZhuanForm.pageNumber = page
      this.liuZhuan1()
    },
    liuZhuan(row){   this.liuZhuanShow=true
      this.liuZhuanForm.studentId=row.id
      this.liuZhuanForm.deptId=row.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        console.log(res,"res");
        this.liuZhuanData=res.records
         this.liuZhuanTotal=Number(res.total)
      })
    },
    liuZhuan1(row){
      // this.liuZhuanShow=true
      // this.liuZhuanForm.studentId=row.id
      // this.liuZhuanForm.deptId=this.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        console.log(res,"res");
        this.liuZhuanData=res.records
        this.liuZhuanTotal=Number(res.total)
        // this.liuZhuanTotal=res.total
      })
    },
    buChongCaiLiao(row){
      this.buChongShow=true
      // this.form.studentId=row.id
      // this.form.deptId=row.toDeptId deptId: row.toDeptId,
      // this.form.schoolId=row.schoolId
      console.log(row,"row")
      getKuaQuBuChong({studentId: row.id,deptId:row.deptId}).then(res=>{
        // this.$refs.mediaUpload.upload.list=res
        if(res.material!=null){
          this.imgList=res.material.split(',')
        }else{
         this.imgList=[]
        }
        console.log(res, this.imgList,"res")
      })
    },
    tiaoJiQuXian(e){
      console.log(e,"e")
      this.getSchoolList1(e)
      // this.tiaoJiXinXi.toSchoolId=''
    },
    change(e){
      this.$forceUpdate()
    },
    duiKouBiaoJi(row){
      this.id=row.id
      this.duiKouStatus=row.signType
      this.duiKouShow=true
    },
    duiKouBiaoJi1(){
      setDuiKouLeiBie({id:this.id,type:this.duiKouStatus},this.prefixDeptCode).then(res=>{
        if(res){
          this.$message.success("操作成功")
          this.getTableData()
          this.duiKouShow=false
        }
      })
    },
    async getTableData1() {
      // if (this.prefixDeptCode != "0") {
      //   // this.getList();
      // } else {
      let depts = await getDepts({ level: 2 });
      this.tiaoJiQuXianList =depts.filter((item) => item.deptCode!='130426');
      // this.prefixDeptCode = this.deptOptions[0].deptCode;
      // this.getList();
      // }
    },
    getSchoolList1(item){
      console.log(item,"getters")
      getDepts({type:1,period:2,level:3,parentId:item},this.prefixDeptCode).then(res=>{
        this.tiaoJiSchoolList=res
        console.log(res,"res")
      })
    },
    getSchoolList(){
      console.log(this.$store.getters,"getters")
      getDepts({type:1,period:2,deptCode:this.deptCode},this.prefixDeptCode).then(res=>{
      this.schoolList=res
        console.log(res,"res")
      })
    },
    faSongLuQu(){
      this.faSongLuQuShow=true
      this.luQuTongZhi.content=""
      this.luQuTongZhi.studentIds=[]
      let studentIds=[]
      let luqu1= {}
      luqu1=this.search
      this.idList.forEach((item) =>{
        studentIds.push(item.id)
      })
      luqu1.studentIds=studentIds
      getFaSongLuQuTongZhiRenShu(luqu1,this.prefixDeptCode).then(res=>{
        console.log(res.length,"res")
        luqu1.renShu=res.length
        luqu1.studentIds=res
        this.luQuTongZhi=luqu1
      })

    },
    faSongLuQuJieGuo(){
      this.$confirm('确认要为该些学生发送录取通知', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        faSongLuQuTongZhi(this.luQuTongZhi, this.prefixDeptCode).then(res => {
          if (res) {
            this.$message({
              type: 'success',
              message: '录取成功!'
            })
            this.getTableData()
            this.luQuTongZhi = {}
            this.faSongLuQuShow = false
          }
        })
      })
    },
    youfu(row){
      this.youFu.id=row.id;
      this.youFuShow=true;
    },
    youFu1(){
      addYouFuLeiXing(this.youFu,this.prefixDeptCode).then(res=>{
        if(res){
          this.$message.success("添加优抚对象成功")
          this.youFuShow=false
          this.youFu={}
        }
      })
    },
    exportData(){
      let params = this.search;
      this.$download(
          `${pref}${this.prefixDeptCode}/five/export/exportStudent`,
          params,
          "xls",
          "选择拟报名学校统计列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    close(val){
      this.switchModal('add', false)
    },
    searchs(val) {
      this.search=val
      this.getTableData()
    },
    handleSelectionChange(val) {
     this.idList=val
    },
    getTableData() {
      getshiWuQuList(this.search,this.prefixDeptCode).then((res) =>{
        console.log(res,this.search,"res")
        this.total=Number(res.total)
        this.tableData=res

      })
    },
    piLiangShenHeTongGuo(){
      this.$confirm('确认完成审核？所有初审已通过的学生，教育局审核状态都会改为已通过，请慎重使用。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let studentIds=[]
        let shenHe=this.search
        this.idList.forEach((item) =>{
        studentIds.push(item.id)
        })
        shenHe.studentIds=studentIds
        baoMingPiliangShenHeTongGuo(shenHe,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.idList=[]
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    gongShiLuQuJieGuo(){
      // let studentIds=[]
      let gongShi=this.search
      // this.idList.forEach((item) =>{
      //   studentIds.push(item.id)
      // })
      // gongShi.studentIds=studentIds
      gongShiLuQuRenShu(gongShi,this.prefixDeptCode).then(res=>{
      if(res.status){
        this.$confirm(''+res.message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {

          gongShiLuQuJieGuo(gongShi,this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '公布录取结果成功!'
              });
              // this.idList=[]
              this.getTableData()
            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.$alert(''+res.message, '提示', {
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }

      })


    },
    xiuGaiXinXi(){
      this.status=2
      this.boHuiYuanYin=1
    },
    buKeZaiBao(){
      this.status=3
      this.boHuiYuanYin=2
    },
    // tiaoJi(){
    //    this.$refs.detail.tiaoJi()
    // },
    tiaoJi1(){
      this.$confirm('确认要调剂该学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.tiaoJiXinXi.validate((valid)=> {
              if (valid) {
                shiTiaoChu(this.tiaoJiXinXi,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '调剂成功!'
            })
            this.tiaoJiXinXi={}
            this.tiaoJiShow=false
            this.getTableData()
          }
        })
              }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    tongGuo(){
      this.$refs.detail.tongGuo()
    },
    buTongGuo(){
      this.$refs.detail.buTongGuo()
    },
    xiaYiTiao(){
      this.$refs.detail.xiaYiTiao()
    },
    fanHui(){
      this.$refs.detail.fanHui()
      this.switchModal('detail', false)
    },
    save(){
       this.$refs.addForm.save()
       this.getTableData()
      // this.switchModal('add', false)
    },
    add(){
      this.switchModal("add", true)
      this.getTableData()
    },
    // 详情
    detail(row) {
      this.row=row
      this.selectRow=this.search
      console.log(this.search,"search")
      console.log(row);
      this.switchModal('detail', true)
    },
    shenHeBuTongGuo(){
      this.$confirm('确认该学生信息无误，审核不通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeBuTongGuo({id:this.id,status:this.status,reason:this.reason},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.dialogVisible=false
            this.id=''
            this.status=''
            this.reason=''
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });

    },
    beiZhuAdd(){
      this.$confirm('确认该学生信息无误，审核通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeTongGuo({id:this.id,mark:this.mark},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.id=''
            this.mark=''
            this.beiZhuShow=false
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    quXiao(){
      this.duiKouShow=false
      this.baoDaoShow=false
      this.dialogVisible=false
      this.beiZhuShow=false
      this.tiaoJiShow=false
      this.youFu={}
      this.youFuShow=false
      this.tiaoJiXinXi={}
      this.id=''
      this.mark=''
      this.reason=''
      this.status=''
      this.faSongLuQuShow=false
      this.getTableData()
    },
    // 通过
    pass(row) {
      if(this.role == 'COUNTY_ADMIN' || this.role == 'AUDITOR'){
        this.$confirm('确认该学生信息无误，审核通过', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          baoMingShenHeTongGuo({id:row.id,mark:this.mark},this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '审核成功!'
              });
              this.id=''
              this.mark=''
              this.beiZhuShow=false
              this.getTableData()
            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.beiZhuShow=true
        this.id=row.id
      }

    },
    // 不通过
    fail(row) {
      this.dialogVisible=true
      this.id=row.id
    },
    // 调剂
    adjust(row) {
      console.log(row,"row")
      this.tiaoJiXinXi={
        studentId: row.id,
        signId: row.signId,
        idCard:row.idCard,
        studentName:row.studentName,
        schoolName:row.schoolName,
      }
       // tiaoJiaoXueXiaoList({key:row.id},this.prefixDeptCode).then(res=>{
       //   this.tiaoJiSchoolList=res
       // })
      this.tiaoJiShow=true
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
    // 报到
    report(row) {
      console.log(row,"row")
      this.baodao.studentId=row.id
      this.baoDaoShow=true

    },
    baoDao(){
      this.$confirm('确认该学生'+`${this.baodao.status==2?'未报到':'报到'}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
            xuShengBaoDao(this.baodao,this.prefixDeptCode).then(res=>{
                  if(res){
                  this.$message({
                    type: 'success',
                     message: '操作成功!'
                   });
                   }
                   this.baoDaoShow=false
                   this.getTableData()
            })


      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
