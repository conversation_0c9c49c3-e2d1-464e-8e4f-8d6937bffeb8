<template>
  <div>
    <h5>学生基本信息</h5>
    <el-descriptions  title="" >
      <el-descriptions-item label="学生姓名">{{form.studentName}}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{form.idCard}}</el-descriptions-item>
      <el-descriptions-item label="性别">{{form.gender==1?'男':form.gender==2?'女':''}}</el-descriptions-item>
      <el-descriptions-item label="户口性质">{{form.huokouType==1?'市五区':form.huokouType==0?'非市五区':''}}</el-descriptions-item>
      <el-descriptions-item label="出生日期">{{form.birthday}}</el-descriptions-item>
      <el-descriptions-item label="民族">{{form.nation}}</el-descriptions-item>
    </el-descriptions>
    <div v-if="form.hukou">
      <h5>户口信息</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="户主姓名">{{form.hukou.masterName}}</el-descriptions-item>
        <el-descriptions-item label="户主身份证号">{{form.hukou.masterIdCard}}</el-descriptions-item>
        <el-descriptions-item label="户号">{{form.hukou.houseNum}}</el-descriptions-item>
        <el-descriptions-item label="户口本登记地址">{{form.hukou.rangeName!=null?form.hukou.rangeName+form.hukou.address:form.hukou.address}}</el-descriptions-item>
        <el-descriptions-item label="户主与该生关系">{{form.hukou.relation}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.hukou.imgIndex">
          <el-image

              :src="imgQianZhui+form.hukou.imgIndex"
              :preview-src-list="[imgQianZhui+form.hukou.imgIndex]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生所属户口薄首页</p>
        </div>
        <div v-if="form.hukou.imgMaster">
          <el-image

              :src="imgQianZhui+form.hukou.imgMaster"
              :preview-src-list="[imgQianZhui+form.hukou.imgMaster]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生所属户主页</p>
        </div>
        <div v-if="form.hukou.imgStudent">
          <el-image

              :src="imgQianZhui+form.hukou.imgStudent"
              :preview-src-list="[imgQianZhui+form.hukou.imgStudent]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生户口页</p>
        </div>
        <div v-if="form.hukou.imgGuardianOneIndex">
          <el-image

              :src="imgQianZhui+form.hukou.imgGuardianOneIndex"
              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianOneIndex]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>监护人1户口本首页</p>
        </div>
        <div v-if="form.hukou.imgGuardianOneHukou">
          <el-image

              :src="imgQianZhui+form.hukou.imgGuardianOneHukou"
              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianOneHukou]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>监护人1户口页照片</p>
        </div>
        <div v-if="form.hukou.imgGuardianTwoIndex">
          <el-image

              :src="imgQianZhui+form.hukou.imgGuardianTwoIndex"
              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianTwoIndex]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>监护人2户口本首页</p>
        </div>
        <div v-if="form.hukou.imgGuardianTwoHukou">
          <el-image

              :src="imgQianZhui+form.hukou.imgGuardianTwoHukou"
              :preview-src-list="[imgQianZhui+form.hukou.imgGuardianTwoHukou]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>监护人2户口本人页</p>
        </div>
      </div>
    </div>
    <h5 v-if="form.twins!=null">多胞胎</h5>
    <el-table
        v-if="form.twins!=null"
        :data="form.twins"
        style="width: 100%;"
        border
    >
      <el-table-column
          prop="name"
          label="姓名"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="gender"
          label="性别"
          align="center"
      >
        <template slot-scope="scope">
          {{scope.row.gender=='1'?'男':'女'}}
        </template>
      </el-table-column>
    </el-table>

    <h5>监护人信息</h5>
    <el-table
        :data="form.guardians"
        style="width: 100%;"
        border
    >
      <el-table-column
          prop="guardianName"
          label="姓名"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="birthday"
          label="出生日期"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="nation"
          label="民族"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="relation"
          label="关系"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="phone"
          label="联系电话"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="address"
          label="户籍所在地"
          align="center"
      >
      </el-table-column>
    </el-table>
    <div class="pic-box">
      <div v-if="form.guardians[0].imgIdCard">
        <el-image
            :src="imgQianZhui+form.guardians[0].imgIdCard"
            :preview-src-list="[imgQianZhui+form.guardians[0].imgIdCard]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>监护人1身份证照片</p>
      </div>
      <div v-if="form.guardians[1]">
        <el-image
            :src="imgQianZhui+form.guardians[1].imgIdCard"
            :preview-src-list="[imgQianZhui+form.guardians[1].imgIdCard]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>监护人2身份证照片</p>
      </div>
    </div>
    <H5>补充图片</H5>
    <div class="pic-box">
      <!--      <div v-if="form.guardians[0].imgIdCard">-->
      <!--        <el-image-->
      <!--            :src="imgQianZhui+form.guardians[0].imgIdCard"-->
      <!--            :preview-src-list="[imgQianZhui+form.guardians[0].imgIdCard]"-->
      <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--              <i class="el-icon-picture-outline"></i>-->
      <!--            </div> </el-image>-->
      <!--        <p>监护人1身份证照片</p>-->
      <!--      </div>-->
      <!--      <div v-if="form.guardians[1]">-->
      <!--        <el-image-->
      <!--            :src="imgQianZhui+form.guardians[1].imgIdCard"-->
      <!--            :preview-src-list="[imgQianZhui+form.guardians[1].imgIdCard]"-->
      <!--        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">-->
      <!--              <i class="el-icon-picture-outline"></i>-->
      <!--            </div> </el-image>-->
      <!--        <p>监护人2身份证照片</p>-->
      <!--      </div>-->
      <div v-if="form.otherImg.imgBirth">
        <el-image
            :src="imgQianZhui+form.otherImg.imgBirth"
            :preview-src-list="[imgQianZhui+form.otherImg.imgBirth]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>出生证明材料照片</p>
      </div>
      <div v-if="form.otherImg.imgMarry">
        <el-image
            :src="imgQianZhui+form.otherImg.imgMarry"
            :preview-src-list="[imgQianZhui+form.otherImg.imgMarry]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>父母结婚证照片（选填）</p>
      </div>
      <div v-if="form.otherImg.imgVaccines">
        <el-image
            :src="imgQianZhui+form.otherImg.imgVaccines"
            :preview-src-list="[imgQianZhui+form.otherImg.imgVaccines]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>疫苗接种证
        </p>
      </div>
      <div v-if="form.otherImg.imgOne">
        <el-image
            :src="imgQianZhui+form.otherImg.imgOne"
            :preview-src-list="[imgQianZhui+form.otherImg.imgOne]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>补充材料</p>
      </div>
      <div v-if="form.otherImg.imgTwo">
        <el-image
            :src="imgQianZhui+form.otherImg.imgTwo"
            :preview-src-list="[imgQianZhui+form.otherImg.imgTwo]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>补充材料</p>
      </div>
      <div v-if="form.otherImg.imgThree">
        <el-image
            :src="imgQianZhui+form.otherImg.imgThree"
            :preview-src-list="[imgQianZhui+form.otherImg.imgThree]"
        >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
          <i class="el-icon-picture-outline"></i>
        </div> </el-image>
        <p>补充材料</p>
      </div>
    </div>
    <div v-if="form.house.houseType==1">
      <h5>使用《房屋所有权证》信息报名</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="房屋所有权人姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="所有权人与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="所有权证发放时间">{{form.house.documentTime}}</el-descriptions-item>
        <el-descriptions-item label="房权证号（证件编号">{{form.house.documentNum}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.house.imgHouse">
          <el-image
              :src="imgQianZhui+form.house.imgHouse"
              :preview-src-list="[imgQianZhui+form.house.imgHouse]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>房屋所有权证信息页照片（有具体门牌号页）</p>
        </div>
        <div v-if="form.house.imgHouseDoor">
          <el-image
              :src="imgQianZhui+form.house.imgHouseDoor"
              :preview-src-list="[imgQianZhui+form.house.imgHouseDoor]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p> 房屋所有权证信息页照片（有房权号页）</p>
        </div>
        <div v-if="form.house.imgWater">
          <el-image
              :src="imgQianZhui+form.house.imgWater"
              :preview-src-list="[imgQianZhui+form.house.imgWater]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>水电费票据</p>
        </div>
        <div v-if="form.house.imgInvoice">
          <el-image
              :src="imgQianZhui+form.house.imgInvoice"
              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>购房发票</p>
        </div>
        <div v-if="form.house.imgProperty">
          <el-image
              :src="imgQianZhui+form.house.imgProperty"
              :preview-src-list="[imgQianZhui+form.house.imgProperty]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>物业费票据</p>
        </div>
        <div v-if="form.house.imgGas">
          <el-image
              :src="imgQianZhui+form.house.imgGas"
              :preview-src-list="[imgQianZhui+form.house.imgGas]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>燃气费票据</p>
        </div>
        <div v-if="form.house.imgHeating">
          <el-image
              :src="imgQianZhui+form.house.imgHeating"
              :preview-src-list="[imgQianZhui+form.house.imgHeating]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>取暖费票照片</p>
        </div>
        <div v-if="form.house.imgOtherOne">
          <el-image
              :src="imgQianZhui+form.house.imgOtherOne"
              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
        <div v-if="form.house.imgOtherTwo">
          <el-image
              :src="imgQianZhui+form.house.imgOtherTwo"
              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>

      </div>
    </div>
    <div v-if="form.house.houseType==2">
      <h5>房产信息-不动产权证书</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="不动产第XXX号">{{form.house.documentNum}}</el-descriptions-item>
        <el-descriptions-item label="房主姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="房主与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="所有权证发放时间">{{form.house.documentTime}}</el-descriptions-item>
        <!--    <el-descriptions-item label="不动产权单元号（选填）">{{form.house.documentTime}}</el-descriptions-item>-->
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.house.imgHouse">
          <el-image
              :src="imgQianZhui+form.house.imgHouse"
              :preview-src-list="[imgQianZhui+form.house.imgHouse]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>不动产信息图片</p>
        </div>
        <div v-if="form.house.imgWater">
          <el-image
              :src="imgQianZhui+form.house.imgWater"
              :preview-src-list="[imgQianZhui+form.house.imgWater]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>水电费票据</p>
        </div>
        <div v-if="form.house.imgInvoice">
          <el-image
              :src="imgQianZhui+form.house.imgInvoice"
              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>购房发票</p>
        </div>
        <div v-if="form.house.imgProperty">
          <el-image
              :src="imgQianZhui+form.house.imgProperty"
              :preview-src-list="[imgQianZhui+form.house.imgProperty]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>物业费票据</p>
        </div>
        <div v-if="form.house.imgGas">
          <el-image
              :src="imgQianZhui+form.house.imgGas"
              :preview-src-list="[imgQianZhui+form.house.imgGas]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>燃气费票据</p>
        </div>
        <div v-if="form.house.imgHeating">
          <el-image
              :src="imgQianZhui+form.house.imgHeating"
              :preview-src-list="[imgQianZhui+form.house.imgHeating]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>取暖费票照片</p>
        </div>
        <div v-if="form.house.imgOtherOne">
          <el-image
              :src="imgQianZhui+form.house.imgOtherOne"
              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
        <div v-if="form.house.imgOtherTwo">
          <el-image
              :src="imgQianZhui+form.house.imgOtherTwo"
              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
      </div>
    </div>
    <div v-if="form.house.houseType==3">
      <h5>房产信息-购房合同（协议）</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="买受人">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label=" 身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="签约时间">{{form.house.documentTime}}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{form.house.documentNum}}</el-descriptions-item>
        <el-descriptions-item label="买受人与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.house.imgHouse">
          <el-image
              :src="imgQianZhui+form.house.imgHouse"
              :preview-src-list="[imgQianZhui+form.house.imgHouse]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>购房合同（封皮)</p>
        </div>
        <div v-if="form.house.imgBuyOne">
          <el-image
              :src="imgQianZhui+form.house.imgBuyOne"
              :preview-src-list="[imgQianZhui+form.house.imgBuyOne]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>买受人信息所在页</p>
        </div>
        <div v-if="form.house.imgHouseDoor">
          <el-image
              :src="imgQianZhui+form.house.imgHouseDoor"
              :preview-src-list="[imgQianZhui+form.house.imgHouseDoor]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p> 房屋门牌号所在页</p>
        </div>
        <div v-if="form.house.imgBuyTwo">
          <el-image
              :src="imgQianZhui+form.house.imgBuyTwo"
              :preview-src-list="[imgQianZhui+form.house.imgBuyTwo]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>买受人签字盖章页</p>
        </div>
        <div v-if="form.house.imgWater">
          <el-image
              :src="imgQianZhui+form.house.imgWater"
              :preview-src-list="[imgQianZhui+form.house.imgWater]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>水电费票据</p>
        </div>
        <div v-if="form.house.imgInvoice">
          <el-image
              :src="imgQianZhui+form.house.imgInvoice"
              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>购房发票</p>
        </div>
        <div v-if="form.house.imgProperty">
          <el-image
              :src="imgQianZhui+form.house.imgProperty"
              :preview-src-list="[imgQianZhui+form.house.imgProperty]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>物业费票据</p>
        </div>
        <div v-if="form.house.imgGas">
          <el-image
              :src="imgQianZhui+form.house.imgGas"
              :preview-src-list="[imgQianZhui+form.house.imgGas]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>燃气费票据</p>
        </div>
        <div v-if="form.house.imgHeating">
          <el-image
              :src="imgQianZhui+form.house.imgHeating"
              :preview-src-list="[imgQianZhui+form.house.imgHeating]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>取暖费票照片</p>
        </div>
        <div v-if="form.house.imgOtherOne">
          <el-image
              :src="imgQianZhui+form.house.imgOtherOne"
              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
        <div v-if="form.house.imgOtherTwo">
          <el-image
              :src="imgQianZhui+form.house.imgOtherTwo"
              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
      </div>
    </div>
    <div v-if="form.house.houseType==4">
      <h5>小产权房信息报名</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="房主姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="房主与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.house.imgHouse">
          <el-image
              :src="imgQianZhui+form.house.imgHouse"
              :preview-src-list="[imgQianZhui+form.house.imgHouse]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>小产权凭证照片</p>
        </div>
        <div v-if="form.house.imgWater">
          <el-image
              :src="imgQianZhui+form.house.imgWater"
              :preview-src-list="[imgQianZhui+form.house.imgWater]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>水电费票据</p>
        </div>
        <div v-if="form.house.imgInvoice">
          <el-image
              :src="imgQianZhui+form.house.imgInvoice"
              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>购房发票</p>
        </div>
        <div v-if="form.house.imgProperty">
          <el-image
              :src="imgQianZhui+form.house.imgProperty"
              :preview-src-list="[imgQianZhui+form.house.imgProperty]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>物业费票据</p>
        </div>
        <div v-if="form.house.imgGas">
          <el-image
              :src="imgQianZhui+form.house.imgGas"
              :preview-src-list="[imgQianZhui+form.house.imgGas]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>燃气费票据</p>
        </div>
        <div v-if="form.house.imgHeating">
          <el-image
              :src="imgQianZhui+form.house.imgHeating"
              :preview-src-list="[imgQianZhui+form.house.imgHeating]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>取暖费票照片</p>
        </div>
        <div v-if="form.house.imgOtherOne">
          <el-image
              :src="imgQianZhui+form.house.imgOtherOne"
              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
        <div v-if="form.house.imgOtherTwo">
          <el-image
              :src="imgQianZhui+form.house.imgOtherTwo"
              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
      </div>
    </div>
    <div v-if="form.house.houseType==5">
      <h5>使用廉租房/公租房信息报名</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="承租人">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="承租人身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label="  承租人与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.house.imgHouse">
          <el-image
              :src="imgQianZhui+form.house.imgHouse"
              :preview-src-list="[imgQianZhui+form.house.imgHouse]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>房屋租赁证照片</p>
        </div>
        <div v-if="form.house.imgWater">
          <el-image
              :src="imgQianZhui+form.house.imgWater"
              :preview-src-list="[imgQianZhui+form.house.imgWater]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>水电费票据</p>
        </div>
        <div v-if="form.house.imgInvoice">
          <el-image
              :src="imgQianZhui+form.house.imgInvoice"
              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>购房发票</p>
        </div>
        <div v-if="form.house.imgProperty">
          <el-image
              :src="imgQianZhui+form.house.imgProperty"
              :preview-src-list="[imgQianZhui+form.house.imgProperty]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>物业费票据</p>
        </div>
        <div v-if="form.house.imgGas">
          <el-image
              :src="imgQianZhui+form.house.imgGas"
              :preview-src-list="[imgQianZhui+form.house.imgGas]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>燃气费票据</p>
        </div>
        <div v-if="form.house.imgHeating">
          <el-image
              :src="imgQianZhui+form.house.imgHeating"
              :preview-src-list="[imgQianZhui+form.house.imgHeating]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>取暖费票照片</p>
        </div>
        <div v-if="form.house.imgOtherOne">
          <el-image
              :src="imgQianZhui+form.house.imgOtherOne"
              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
        <div v-if="form.house.imgOtherTwo">
          <el-image
              :src="imgQianZhui+form.house.imgOtherTwo"
              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
      </div>
    </div>
    <div v-if="form.house.houseType==6">
      <h5>房产-自然村</h5>
      <el-descriptions  title="" >
        <el-descriptions-item label="房主姓名">{{form.house.ownerName}}</el-descriptions-item>
        <el-descriptions-item label="房主身份证号">{{form.house.idCard}}</el-descriptions-item>
        <el-descriptions-item label=" 与该生关系">{{form.house.relation}}</el-descriptions-item>
        <el-descriptions-item label="房屋详细地址">{{form.house.rangeName+form.house.address}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.house.imgHouse">
          <el-image
              :src="imgQianZhui+form.house.imgHouse"
              :preview-src-list="[imgQianZhui+form.house.imgHouse]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>住房证明</p>
        </div>
        <div v-if="form.house.imgWater">
          <el-image
              :src="imgQianZhui+form.house.imgWater"
              :preview-src-list="[imgQianZhui+form.house.imgWater]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>水电费票据</p>
        </div>
        <div v-if="form.house.imgInvoice">
          <el-image
              :src="imgQianZhui+form.house.imgInvoice"
              :preview-src-list="[imgQianZhui+form.house.imgInvoice]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>购房发票</p>
        </div>
        <div v-if="form.house.imgProperty">
          <el-image
              :src="imgQianZhui+form.house.imgProperty"
              :preview-src-list="[imgQianZhui+form.house.imgProperty]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>物业费票据</p>
        </div>
        <div v-if="form.house.imgGas">
          <el-image
              :src="imgQianZhui+form.house.imgGas"
              :preview-src-list="[imgQianZhui+form.house.imgGas]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>燃气费票据</p>
        </div>
        <div v-if="form.house.imgHeating">
          <el-image
              :src="imgQianZhui+form.house.imgHeating"
              :preview-src-list="[imgQianZhui+form.house.imgHeating]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>取暖费票照片</p>
        </div>
        <div v-if="form.house.imgOtherOne">
          <el-image
              :src="imgQianZhui+form.house.imgOtherOne"
              :preview-src-list="[imgQianZhui+form.house.imgOtherOne]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
        <div v-if="form.house.imgOtherTwo">
          <el-image
              :src="imgQianZhui+form.house.imgOtherTwo"
              :preview-src-list="[imgQianZhui+form.house.imgOtherTwo]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>补充材料</p>
        </div>
      </div>
    </div>
    <div >
      <h5 v-if="form.juzhu[0].juzhuCode">外地户籍学生补充信息</h5>
      <el-descriptions  title="" v-if="form.juzhu[0].juzhuCode">
        <el-descriptions-item label="学生居住证编号">{{form.juzhu[0].juzhuCode}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人姓名">{{form.juzhu[0].juzhuName}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人身份证号">{{form.juzhu[0].idCard}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人详细地址">{{form.juzhu[0].rangeName+form.juzhu[0].address}}</el-descriptions-item>
        <el-descriptions-item label="学生居住人与该生关系">{{form.juzhu[0].relation}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions  title="" v-if="form.juzhu[1].juzhuCode">
        <el-descriptions-item label="监护人1居住证编号">{{form.juzhu[1].juzhuCode}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人姓名">{{form.juzhu[1].juzhuName}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人身份证号">{{form.juzhu[1].idCard}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人详细地址">{{form.juzhu[1].rangeName+form.juzhu[1].address}}</el-descriptions-item>
        <el-descriptions-item label="监护人1居住人与该生关系">{{form.juzhu[1].relation}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions  title="" v-if="form.juzhu[2].juzhuCode">
        <el-descriptions-item label="监护人2居住证编号">{{form.juzhu[2].juzhuCode}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人姓名">{{form.juzhu[2].juzhuName}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人身份证号">{{form.juzhu[2].idCard}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人详细地址">{{form.juzhu[2].rangeName+form.juzhu[2].address}}</el-descriptions-item>
        <el-descriptions-item label="监护人2居住人与该生关系">{{form.juzhu[2].relation}}</el-descriptions-item>
      </el-descriptions>
      <div class="pic-box">
        <div v-if="form.juzhu[1].imgJuzhu">
          <el-image
              :src="imgQianZhui+form.juzhu[1].imgJuzhu"
              :preview-src-list="[imgQianZhui+form.juzhu[1].imgJuzhu]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生监护人1居住证照片</p>
        </div>
        <div v-if="form.juzhu[2].imgJuzhu">
          <el-image
              :src="imgQianZhui+form.juzhu[2].imgJuzhu"
              :preview-src-list="[imgQianZhui+form.juzhu[2].imgJuzhu]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生监护人2居住证照片</p>
        </div>
        <div v-if="form.juzhu[0].imgJuzhu">
          <el-image
              :src="imgQianZhui+form.juzhu[0].imgJuzhu"
              :preview-src-list="[imgQianZhui+form.juzhu[0].imgJuzhu]"
          >  <div slot="error" class="image-slot" style="text-align: center;line-height:100px">
            <i class="el-icon-picture-outline"></i>
          </div> </el-image>
          <p>学生居住证照片</p>
        </div>
      </div>
    </div>

<!--    <h5>审核情况</h5>-->
<!--    <el-table-->
<!--        :data="form.auditDetails"-->
<!--        style="width: 100%"-->
<!--        border-->
<!--    >-->
<!--      <el-table-column-->
<!--          prop="auditUserName"-->
<!--          label="审核人"-->
<!--          width="180"-->
<!--          align="center"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          {{scope.row.auditUserName?scope.row.auditUserName:'&#45;&#45;'}}-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          prop="auditRole"-->
<!--          label="审核角色"-->
<!--          width="180"-->
<!--          align="center"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          {{scope.row.auditRole-->
<!--            ?scope.row.auditRole-->
<!--            :'&#45;&#45;'}}-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          prop="auditStatus"-->
<!--          label="审核状态"-->
<!--          width="180"-->
<!--          align="center"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          {{ scope.row.auditStatus === 0 ? '待审核' :-->
<!--            scope.row.auditStatus === 1 ?'审核通过':-->
<!--                scope.row.auditStatus === 2 ?'驳回补充信息':-->
<!--                    scope.row.auditStatus === 3 ?'驳回不可再报':'' }}-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          prop="reason"-->
<!--          label="驳回原因"-->
<!--          align="center"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          {{scope.row.reason?scope.row.reason:'&#45;&#45;'}}-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--    </el-table>-->
    <el-dialog
        title="调剂"
        :visible.sync="tiaoJiShow"
        width="500px"
        append-to-body
    >
      <div style="display:flex;width: 100%;justify-content: space-between"><span>ID:{{ tiaoJiXinXi.signId }}</span><span>学生姓名:{{tiaoJiXinXi.studentName}}</span><span>报名学校:{{tiaoJiXinXi.schoolName}}</span> </div>
      <p>调剂区县选择：</p>
      <div>
        <el-select
            v-model="tiaoJiXinXi.adjustQuXianId"
            placeholder="选择调剂区县"
            clearable
            filterable
            @change="tiaoJiQuXian"
        >
          <el-option
              v-for="item in tiaoJiQuXianList"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <p>调剂学校选择：</p>
      <div>
        <el-select
            v-model="tiaoJiXinXi.adjustSchoolId"
            placeholder="选择调剂学校"
            clearable
            filterable
        >
          <el-option
              v-for="item in tiaoJiSchoolList"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="tiaoJi1"
      >确 定</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  baoMingShenHeBuTongGuo,
  baoMingShenHeTongGuo,
  getshiWuQuDetail, getshiWuQuList,
  studentTiaoJiao, tiaoJiaoXueXiaoList
} from "@/api/shiWuQuBaoMingLieBiao";
import {pref} from "@/utils/common";
import {getDepts} from "@/api/common";
export default {
  props:{row:[Object], dataRow:[Object],deptCode:{
    type:String,
      default:''
    }},
  name: "detail",
  data(){
    return{
      id:'',//学生ID
      mark:'',//学生审核通过备注
      status:'',//学生驳回类型
      reason:'',
      boHuiYuanYin:'',
      tiaoJiSchoolList:[],
      tiaoJiQuXianList:[],
      tiaoJiXinXi:{},
      tiaoJiShow:false,
      beiZhuShow:false,
      dialogVisible:false,
      prefixCode:this.$store.getters.prefixCode,
      prefixDeptCode: this.deptCode,
      imgQianZhui:`${process.env.VUE_APP_BASE_API}${pref}${this.deptCode}`,
      role: this.$store.getters.role,
      form:{},
      search:{},
      total:0,
      tableData:[],
      studentId:'', //下一条学生id
      index:0,//下一条学生index值
    }
  },
  created() {
    this.getDetail()
    this.search=this.dataRow
    console.log(this.prefixDeptCode,this.search)
    // getshiWuQuList(this.search,this.prefixDeptCode).then(data => {
    //       this.tableData = data.records
    //       this.total=Math.ceil(data.total/this.search.pageSize)
    //           //console.log(this.studentId)
    //
    //         })
    //  this.getTableData1()
     },
  methods:{
    tiaoJiQuXian(e){
      console.log(e,"e")
      this.getSchoolList1(e)
    },
    async getTableData1() {
      // if (this.prefixDeptCode != "0") {
      //   // this.getList();
      // } else {
      let depts = await getDepts({ level: 2 });
      this.tiaoJiQuXianList =depts.filter((item) => item.deptCode!='130426');
      // this.prefixDeptCode = this.deptOptions[0].deptCode;
      // this.getList();
      // }
    },
    getSchoolList1(item){
      console.log(item,"getters")
      getDepts({type:1,period:2,level:3,parentId:item},this.prefixDeptCode).then(res=>{
        this.tiaoJiSchoolList=res
        console.log(res,"res")
      })
    },
    getDetail(){
      getshiWuQuDetail({key:this.row.studentId||this.row.id},this.prefixDeptCode).then(res=>{
        this.form=res
        if(!res.otherImg){
          this.form.otherImg={
            imgBirth:''
          }
        }
        if(!res.juzhu){
          this.form.juzhu=[{},{},{}]
        }else if(res.juzhu.length==1) {
          this.form.juzhu[1]={}
          this.form.juzhu[2]={}
        }else if(res.juzhu.length==2) {
          this.form.juzhu[2]={}
        }
        if(!res.house){
          this.form.house={
          }
        }


      })
    },
    tiaoJi() {
      this.tiaoJiXinXi={
        id: this.form.id,
        signId: this.form.signId,
        studentName:this.form.studentName,
        schoolName:this.form.schoolName,
      }
      // tiaoJiaoXueXiaoList({key:this.form.id},this.prefixDeptCode).then(res=>{
      //   this.tiaoJiSchoolList=res
      // })
      this.tiaoJiShow=true
    },
    tiaoJi1() {
      this.$confirm('确认要调剂该学生', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        studentTiaoJiao(this.tiaoJiXinXi,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '调剂成功!'
            })
            this.getForm(this.tiaoJiXinXi.id)
            this.tiaoJiXinXi={}
            this.tiaoJiShow=false
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    buTongGuo() {
      this.dialogVisible=true
    },
    fanHui(){
      if(this.search.pageNumber>this.total){
        this.search.pageNumber=this.search.pageNumber-1
      }

      this.$emit("searchs",this.search)
      this.index=0
    },
    beiZhuAdd(){
      this.$confirm('确认该学生信息无误，审核通过', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      baoMingShenHeTongGuo({id:this.form.id,mark:this.mark},this.prefixDeptCode).then(res=>{
        if(res){
          this.$message({
            type: 'success',
            message: '审核成功!'
          });
          this.getForm(this.form.id)
          this.form.id=''
          this.mark=''
          this.beiZhuShow=false

        }
      })

    }).catch(() => {
      this.$message({
        type: 'info',
        message: '取消'
      });
    });},
    shenHeBuTongGuo(){
      this.$confirm('确认该学生信息无误，审核不通过', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoMingShenHeBuTongGuo({id:this.form.id,status:this.status,reason:this.reason},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '审核成功!'
            });
            this.dialogVisible=false
            this.getForm(this.form.id)
            this.form.id=''
            this.status=''
            this.reason=''
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    tongGuo() {
      if(this.role == 'COUNTY_ADMIN' || this.role == 'AUDITOR'){
        this.$confirm('确认该学生信息无误，审核通过', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          baoMingShenHeTongGuo({id:this.form.id,mark:this.mark},this.prefixDeptCode).then(res=>{
            if(res){
              this.$message({
                type: 'success',
                message: '审核成功!'
              });
              this.getForm(this.form.id)
              this.form.id=''
              this.mark=''
              this.beiZhuShow=false

            }
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      }else {
        this.beiZhuShow=true
      }

    },
    quXiao(){
      this.dialogVisible=false
      this.beiZhuShow=false
      this.tiaoJiShow=false
      this.tiaoJiXinXi={}
      this.id=''
      this.mark=''
      this.reason=''
      this.status=''
    },
    xiuGaiXinXi(){
      this.status=2
      this.boHuiYuanYin=1
    },
    buKeZaiBao(){
      this.status=3
      this.boHuiYuanYin=2
    },
    xiaYiTiao() {
      // console.log('下一条',this.tableData.length,this.search,this.total)
      if(this.index<this.tableData.length-1&&this.search.pageNumber<=this.total){
        this.tableData.forEach((element,index)=>{
          if(element.id ==this.form.id){
            if(index==this.tableData.length-1){
              this.$message.error("已是最后一条数据")
            }else {
              this.studentId=this.tableData[index+1].id
              this.index=index+1
              this.getForm(this.studentId)
              //console.log(this.studentId)
            }

          }
        })
      }else {
        if(this.search.pageNumber<this.total){
          this.search.pageNumber=this.search.pageNumber+1
          if(this.search.pageNumber>this.total){
            this.$message.error("已是最后一条数据")
          }else {this.index=0
            getshiWuQuList(this.search,this.prefixDeptCode)
                .then(data => {
                      this.tableData = data.records
                      this.studentId=this.tableData[0].id
                      this.index=0
                  this.total=Math.ceil(data.total/this.search.pageSize)
                      this.getForm(this.studentId)
                      //console.log(this.studentId)

                    }
                )
          }
        }else {
          this.$message.error("已是最后一条数据")
        }


      }
    },
    getForm(row){
      this.myWorksImgsArray=[]
      this.FormList=[]
      getshiWuQuDetail({key:row},this.prefixDeptCode).then(res=>{
        this.form=res
        if(!res.otherImg){
          this.form.otherImg={
            imgBirth:''
          }
        }
        if(!res.juzhu){
          this.form.juzhu=[{},{},{}]
        }else if(res.juzhu.length==1) {
          this.form.juzhu[1]={}
          this.form.juzhu[2]={}
        }else if(res.juzhu.length==2) {
          this.form.juzhu[2]={}
        }
        if(!res.house){
          this.form.house={
          }
        }


      })
      // this.$api.baoMingChaXun.xiangQing({key:row}).then(res=>{
      //   if(res){
      //     this.form=res
      //     //console.log(this.myWorksImgsArray,"myworks")
      //     // this.form.studentImg=process.env.VUE_APP_BASE_API+res.studentImg
      //     // this.forms.value=3
      //     // this.forms.key=res.studentId
      //     // this.shenHeShow=true
      //     // //console.log( this.form.studentImg)
      //   }else {
      //     // //console.log("111")
      //     return
      //   }
      //
      //   // this.myWorksImgsArray = ;
      // })
    },
  }
}
</script>

<style scoped lang="scss">
.pic-box {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  .el-image {
    width: 150px;
    height: 100px;
    border: 1px solid #dadada;
    border-radius: 4px;
  }
  p {
    text-align: center;
  }
}
</style>
