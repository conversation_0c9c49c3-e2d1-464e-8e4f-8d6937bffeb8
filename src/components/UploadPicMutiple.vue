<template>
  <div>
    <el-upload
      ref="upload"
      class="upload-box"
      :class="{ hide: isHide }"
      accept=".jpeg, .jpg, .png"
      action=""
      :http-request="submitUpload"
      list-type="picture-card"
      :file-list="fileList"
      :limit="limit"
      :before-upload="beforeUpload"
      :on-change="onChange"
      :on-remove="onRemove"
      :on-exceed="onExceed"
      :on-preview="preview"
    >
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog :visible.sync="isUrl">
      <img width="100%" :src="url" />
    </el-dialog>
    <div class="upload_text">{{ textPic }}</div>
  </div>
</template>

<script>
 import { uploadImage } from "@/api/tiaoChu";
import * as imageConversion from "image-conversion";
 import {pref} from "@/utils/common";
export default {
  name: "uploadPicMutiple",
  props: {
    textPic: String,
  },
  data() {
    return {
      fileList: [],
      upload: {
        list: [],
        _tempUids: [],
      },
      limit: 6,
      isHide: false,
      isUrl: false,
      url: "",
      imgQianZhui:`${process.env.VUE_APP_BASE_API}/user-api`,
    };
  },
  mounted() {
    setTimeout(() => {
      // console.log(this.upload.list)
      if(this.upload.list.length != 0) {
        this.upload.list.forEach(item => {
          let obj = {name: item, url:this.imgQianZhui+ item}
          this.fileList.push(obj)
          this.upload._tempUids.push(obj.name)
        })
      }
    }, 1500)
  },
  methods: {
    // 上传超限
    onExceed() {
      this.$message.warning("上传超限！");
    },
    // 添加文件
    onChange(file, fileList) {
      this.fileList = fileList;
      this.isHide = fileList.length == 6 ? true : false;
    },
    // 删除文件
    onRemove(file, fileList) {
      console.log(file)
      this.fileList = fileList;
      let index = this.upload._tempUids.findIndex((item) => item === file.name);
      if (index != -1) {
        this.upload._tempUids.splice(index, 1);
        this.upload.list.splice(index, 1);
      }
      this.isHide = false;
    },
    // 上传前验证
    beforeUpload(file) {
      let fileName = file.name;
      let suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
      if (/^(jpeg|jpg|png)$/.test(suffix)) {
        if (file.size > 6 * 1024 * 1024) {
          this.$message.warning("图片大小不能超过6M！");
          return false;
        }
      } else {
        this.$message.warning("请上传jpg、jpeg或png格式的图片！");
        return false;
      }

      // 类型为图片且大于200k, 启用压缩到200K
      return new Promise((resolve, reject) => {
        let isInsize = file.size < 200 * 1024;
        if (isInsize) {
          resolve(file);
        }
        imageConversion.compressAccurately(file, 200).then((res) => {
          resolve(res);
        });
      });
    },
    // 上传文件
    submitUpload(f) {
      let formData = new FormData();
      formData.append("file", f.file);
      console.log(f.file)
      uploadImage(formData).then((res) => {
        this.upload._tempUids.push(f.file.name);
        this.upload.list.push( res);
      });
    },
    // 图片预览
    preview(file) {
      this.url = file.url;
      this.isUrl = true;
    },
  },
};
</script>

<style lang="less" scoped>
.upload-box {
  /deep/ .el-upload-list--picture-card .el-upload-list__item {
    margin: 0 36px 8px 0;
  }
}
.upload_text {
  display: inline-block;
  padding: 10px 0;
  text-align: center;
  font-size: 12px;
  width: 150px;
  line-height: 16px;
}
.tip-line {
  line-height: 20px;
  color: #454545;
	font-size: 12px;
	margin-top: 20px;
  span {
    color: #ff0000;
  }
}
</style>
