import request from '@/utils/request';
import { pref } from '@/utils/common';
// 获取报名列表
export const getshiWuQuList = (data, code) => request.post(`${pref + code}/five/student/list`, data)
//获取报名详情
export const getshiWuQuDetail = (data, code) => request.post(`${pref + code}/five/student/studentDetail`, data)
//获取报名详情
export const getshiWuQuDetail1 = (data, code) => request.post(`${pref + code}/five/student/studentDetailNew`, data)
//获取审核详情
export const getshiWuQuShenHeDetail = (data, code) => request.post(`${pref + code}/five/student/studentDetailMongo`, data)
//报名审核通过
export const baoMingShenHeTongGuo = (data, code) => request.post(`${pref + code}/five/student/changePass`, data)
//报名审核不通过
export const baoMingShenHeBuTongGuo = (data, code) => request.post(`${pref + code}/five/student/changeNotPass`, data)
//批量审核通过
export const baoMingPiliangShenHeTongGuo = (data, code) => request.post(`${pref + code}/five/student/changeBatchPass`, data)
//调剂
export const studentTiaoJiao = (data, code) => request.post(`${pref + code}/five/student/adjustSchool`, data)
//添加报名信息
export const addBaoMing = (data, code) => request.post(`${pref + code}/five/student/addStudent`, data)
//公示录取结果
export const gongShiLuQuJieGuo = (data, code) => request.post(`${pref + code}/five/student/publicStudent`, data)
//调剂学校列表
export const tiaoJiaoXueXiaoList = (data, code) => request.post(`${pref + code}/five/student/adjustSchoolList`, data)
//获取招生范围
export const zhaoShengFanWei = (data, code) => request.post(`/user-api/center/schoolRang/list`, data)
//优抚类型列表
export const youFuLeiXingList = (data, code) => request.post(`${pref + code}/five/student/careList`, data)
//添加学生优抚类别
export const addYouFuLeiXing = (data, code) => request.post(`${pref + code}/five/student/setStudentCareType`, data)
//分配学校
export const fenPeiXueXiao = (data, code) => request.post(`${pref + code}/five/student/distributeSchool`, data)
//获取发送录取通知人数
export const getFaSongLuQuTongZhiRenShu = (data, code) => request.post(`${pref + code}/five/student/getNoticeStudentIds`, data)
//发送录取通知
export const faSongLuQuTongZhi = (data, code) => request.post(`${pref + code}/five/student/setNotice`, data)
//公示录取人数
export const gongShiLuQuRenShu = (data, code) => request.post(`${pref + code}/five/student/publicStudentBefore`, data)
//学生报到
export const xuShengBaoDao=(data,code)=>request.post(`${pref + code}/five/student/setSignStatus`,data)
//分配人数
export const fenPeiRenShu=(data,code)=>request.post(`${pref + code}/five/student/distributeSchoolBefore`,data)
//设置学生对口类别
export const setDuiKouLeiBie=(data,code)=>request.post(`${pref + code}/five/student/setSignType`,data)
//流转记录
export const tiaoJiJiLu=(data,code)=>request.post(`${pref + code}/five/student/schoolLogList`,data)
//重新分配
export const chongXinFenPei=(data,code)=>request.post(`${pref + code}/five/student/resetDistributeSchool`,data)
//撤销公示分配
export const cheXiaoGongShiFenPei=(data,code)=>request.post(`${pref + code}/five/student/resetPublicStudent`,data)
