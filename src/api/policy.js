import request from '@/utils/request'
import { pref } from '@/utils/common';

// 列表
export const policyList = (data, code) => request.post(`${pref + code}/biz/messagePolicy/list`, data)
// 新增
export const addPolicy = (data, code) => request.post(`${pref + code}/biz/messagePolicy/create`, data)
// 修改
export const updatePolicy = (data, code) => request.post(`${pref + code}/biz/messagePolicy/update`, data)
// 删除
export const delPolicy = (data, code) => request.post(`${pref + code}/biz/messagePolicy/delete`, data)
// 详情
export const policyDetail = (data, code) => request.post(`${pref + code}/biz/messagePolicy/detail`, data)