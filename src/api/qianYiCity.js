import request from '@/utils/request'
import { pref } from '@/utils/common';

// 获取组织 - 区县 - 学校
export const getDepts = (data) => request.post('/user-api/center/dept/list', data)
// 列表
export const getEnrollList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/page`, data)
//学校深
export const getSchoolAuditorList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/schoolAuditorPage`, data)

// 查询报名类别
export const getTypeList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/type`, data)
/*********** 用户管理 **********/
// 列表
export const getUserList = (data) => request.post('/user-api/center/user/list', data)
// 新增
export const create = (data) => request.post('/user-api/center/user/create', data)
// 编辑
export const update = (data) => request.post('/user-api/center/user/update', data)
// 详情
export const detail = (data) => request.post('/user-api/center/user/detail', data)
// 重置密码
export const resetPwd = (data) => request.post('/user-api/center/user/changePasswordDefault', data)
// 启用
export const enableUser = (data) => request.post('/user-api/center/user/changeStatusNormal', data)
// 禁用
export const disableUser = (data) => request.post('/user-api/center/user/changeStatusDisable', data)
// 删除
export const delUser = (data) => request.post('/user-api/center/user/delete', data)
// 批量导入
export const importUser = data => request.post('/user-api/center/user/importUser', data)

/*********** 删除报名信息 **********/
// 清除学生报名
export const deleteEnrollInfo = (data, code) => request.post(`${pref + code}/biz/enrollment/deleteEnrollInfo`, data)
// 恢复清除的学生报名信息
export const recoverEnrollInfo = (data, code) => request.post(`${pref + code}/biz/enrollment/recoverEnrollInfo`, data)
// 批量清除学生报名数据
export const clearStuBatch = (data, code) => request.post(`${pref + code}/biz/enrollment/importBatchClearStuExcel`, data)
// 获取清除报名列表
export const deleteStudentPageList = data => request.post('/user-api/center/deleteStudent/pageList', data)
// 获取清除学生详情
export const getStudentInfo = data => request.post('/user-api/center/deleteStudent/getStudentInfo', data)

/*********** 公办功能开关设置 **********/
export const permissionList = (data, code) => request.post(`${pref + code}/biz/permission/list`, data)
export const permissionSave = (data, code) => request.post(`${pref + code}/biz/permission/save`, data)

