import request from '@/utils/request'
import {pref} from '@/utils/common';

// 列表
export const getEnrollList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/page`, data)
export const getLotteryEnrollList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/lotteryPage`, data)

// 学校审核员查看列表
export const getSchoolAuditorList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/schoolAuditorPage`, data)
// 查询报名类别
export const getTypeList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/type`, data)
// 教育局、学校 - 审核通过
export const passAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/passAudit`, data)

// 审核驳回
export const rejectAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/noPass`, data)

//学校审核员驳回
export const schoolRejectAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/schoolNoPass`, data)
//分配学校审核员
export const allocateSchoolAuditor = (code) => request.post(`${pref + code}/biz/recruitStudent/allocateSchoolAuditor`)
// 调剂
export const adjust = (data, code) => request.post(`${pref + code}/biz/recruitStudent/adjust`, data)
// 报到
export const report = (data, code) => request.post(`${pref + code}/biz/recruitStudent/report`, data)
// 公示录取结果 - 查询提示信息
export const publicity = (data, code) => request.post(`${pref + code}/biz/recruitStudent/publicity`, data)
// 公示录取结果 - 确认
export const publicAdmissionResults = (data, code) => request.post(`${pref + code}/biz/recruitStudent/publicAdmissionResults`, data)
// 发送录取通知书
export const sendRequisition = (data, code) => request.post(`${pref + code}/biz/enrollQuery/sendRequisition`, data)

export const sendPwRequisition = (data, code) => request.post(`${pref + code}/biz/enrollQuery/sendPwRequisition`, data)
// 设置优抚类型
export const updateEntitledGroup = (data, code) => request.post(`${pref + code}/biz/recruitStudent/updateEntitledGroup`, data)
// 教育局 - 批量审核通过
export const batchAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/batchAudit`, data)
// 公安房管部门 - 审核通过
export const passSecurityAndHouseAudit = (data, code) => request.post(`${pref + code}/biz/security/passSecurityAndHouseAudit`, data)
// 公安部门 - 审核不通过
export const notPassSecurityAndHouseAudit = (data, code) => request.post(`${pref + code}/biz/security/notPassSecurityAndHouseAudit`, data)
// 报名详情
export const adFormDetail = (data, code) => request.post(`${pref + code}/biz/enrollment/getStuEnrollInfo`, data)
// 报名详情 - 审核情况（废弃）
export const getAuditStatus = (data, code) => request.post(`${pref + code}/biz/recruitStudent/auditStatus`, data)
// 报名详情 - 审核情况
export const getAuditRecord = (data, code) => request.post(`${pref + code}/biz/studentLog/getStudentReviewLog`, data)

/*** 邱县 ***/
// 列表
export const getEnrollListQiuXian = (data, code) => request.post(`${pref + code}/biz/recruitStudent/getVillagesJuniorPage`, data)
// 派位成功列表
export const getAssignJuniorPage = (data, code) => request.post(`${pref + code}/biz/recruitStudent/getAssignJuniorPage`, data)

// 公示录取结果 - 查询提示信息
export const villagesJuniorPublicity = (data, code) => request.post(`${pref + code}/biz/recruitStudent/villagesJuniorPublicity`, data)

/*** 峰峰矿区 ***/
// 随迁子女列表
// export const migrantChildrenPage = (data, code) => request.post(`${pref + code}/biz/recruitStudent/migrantChildrenPage`, data)
// 随迁子女列表 - 分配学校
// export const migrantChildrenAssignedSchool = (data, code) => request.post(`${pref + code}/biz/recruitStudent/migrantChildrenAssignedSchool`, data)
// 随迁子女列表 - 发送现场验证通知书
// export const fieldValidation = (data, code) => request.post(`${pref + code}/biz/recruitStudent/fieldValidation`, data)
// 随迁子女列表 - 学校驳回教育局的分配
// export const schoolBackDistribution = (data, code) => request.post(`${pref + code}/biz/recruitStudent/schoolBackDistribution`, data)
// 特殊群体列表
// export const specialPage = (data, code) => request.post(`${pref + code}/biz/recruitStudent/specialPage`, data)


// 获取报名入口1
export const getSetupSaveIds = (data, code) => request.post(`${pref + code}/biz/enrollment/getSetupSaveIds`, data)

// 获取报名入口2
export const getSetupSaveDetail = (data, code) => request.post(`${pref + code}/biz/enrollment/getSetupSaveDetail`, data)

// 获取报名入口3
export const getEntryBySchool = (data, code) => request.post(`${pref + code}/biz/enrollment/getEnrollRoute`, data)

// 检查报名入口是否报名时间内1
export const isInAdTimeRange1 = (code) => request.post(`${pref + code}/biz/registrationTime/homePageVerify`)

// 检查报名入口是否报名时间内2
export const isInAdTimeRange2 = (data, code) => request.post(`${pref + code}/biz/registrationTime/getVerifyBySetUpId`, data)

// 查询报名表单
export const qryAdFormByEntry = (data, code) => request.post(`${pref + code}/biz/enrollment/getEnrollFieldConfig`, data)
//民办添加报名
export const privateAddAd = (data, code) => request.post(`${pref + code}/privatee/biz/service/organizationSubmitEnrollInfo`, data)
// 添加报名
export const addAd = (data, code) => request.post(`${pref + code}/biz/enrollment/organizationSubmitEnrollInfo`, data)
// 区县教育局 - 删除报名
export const deleteEnroll = (data, code) => request.post(`${pref + code}/biz/enrollment/deleteEnrollInfo`, data)
// 区县教育局 - 撤销公示结果
export const revokePublic = (data, code) => request.post(`${pref + code}/biz/recruitStudent/resetPublicStudent`, data)

/***鸡泽***/
export const daoRuWuXuPaiWei=(data,code)=>request.post(`${pref+code}/biz/jzImportStudent/import`,data)

// 获取审核情况中指定的报名详情
export const getEnrollRecord = (data, code) => request.post(`${ pref + code }/biz/studentLog/getStudentInfo`, data)

// 小学学籍入口查询对口学校 - 峰峰专用
export const exclusiveSchoolList = (data, code) => request.post(`${ pref + code }/biz/enrollment/getMatchJuniorList`, data)

// 录取查询 - 检查当前身份证是否已经报名了。
export const checkIdCard = (data, code) => request.post(`${pref+code}/biz/enrollment/checkIdCard`, data)

// 报名查询 - 根据身份证号查询
export const searchByIdName = (params) => request.post('/user-api/center/studentCode/searchIdCardAndName', params)

// 襄都-获取绑定区域
export const getBindArea = (params) => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/getBindArea', params)

//襄都-是否发送线下审核通知
export const schoolReviewAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/schoolReviewAudit`, data)

//襄都-是否发送线下审核通知
export const schoolReviewStatus = (data, code) => request.post(`${pref + code}/biz/recruitStudent/schoolReviewStatus`, data)


// 信度-幼儿园派位列表
export const getAllocationList = (data, code) => request.post(`${pref + code}/biz/KindergartenAllocation/page`, data)

//信度-幼儿园派位表导入
export const importAllocation = (data, code) => request.post(`${pref + code}/biz/KindergartenAllocation/importStudent`, data)

//信度-幼儿园派位表删除信息
export const deleteKinStudent = (data, code) => request.post(`${pref + code}/biz/KindergartenAllocation/delete`, data)