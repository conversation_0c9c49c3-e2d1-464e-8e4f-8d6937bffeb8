import request from '@/utils/request';
import { pref } from '@/utils/common';
export const getStudentList=(data,code)=>request.post(`${pref + code}/biz/student/list`,data)
export const shenHeStudent=(data,code)=>request.post(`${pref + code}/biz/student/changePass`,data)
export const shenHeNoStudent=(data,code)=>request.post(`${pref + code}/biz/student/changeNotPass`,data)
export const studentDetail=(data,code)=>request.post(`${pref + code}/biz/student/studentDetailNew`,data)
export const piLiangShenHeStudent=(data,code)=>request.post(`${pref + code}/biz/student/changeBatchPass`,data)
//公示录取结果
export const gongShiLuQuJieGuo = (data, code) => request.post(`${pref + code}/biz/student/publicStudent`, data)
//取消公示录取结果
export const quXiaoGongShiLuQuJieGuo = (data, code) => request.post(`${pref + code}/biz/student/resetPublicStudent`, data)
//调剂学生
export const tiaoJiStudent= (data, code) => request.post(`${pref + code}/biz/student/adjustSchool`, data)
//发送录取通知书
export const faSongLuQuTongZhiShu=(data,code)=>request.post(`${pref + code}/biz/student/setNotice`,data)
//学生报到
export const xueShengBaoDao=(data,code)=>request.post(`${pref + code}/biz/student/setSignStatus`,code)
//添加学生
export const addStudent=(data,code)=>request.post(`${pref + code}/biz/student/addStudent`,data)
//获取毕业小学
export const getBiYeSchoolList=(data,code)=>request.post(`${pref + code}/biz/parent/getGraduationList`,data)
//招生范围
export const zhaoShengFanWei=(data,code)=>request.post(`/user-api/center/schoolRang/list`,data)
//获取详情
export const studentDetailMongo=(data,code)=>request.post(`${pref + code}/biz/student/studentDetailMongo`,data)
