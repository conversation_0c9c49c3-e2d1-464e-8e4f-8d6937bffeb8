import request from '@/utils/request'
import { pref } from '@/utils/common';

// 学校列表
export const pwSchoolList = (data, code) => request.post(`${pref + code}/biz/assign/getSchoolInfo`, data)

// 根据学校获取待派位学生
export const getPwStuBySchool = (data, code) => request.post(`${pref + code}/biz/assign/getSchoolEnrollStudent`, data)

// 结束派位
export const endPw = (data, code) => request.post(`${pref + code}/biz/assign/startAssign`, data)

// 派位结果
export const pwResultBySchool = (data, code) => request.post(`${pref + code}/biz/assign/getAssignResult`, data)

// 同步派位结果
export const syncPwResult = (data, code) => request.post(`${pref + code}/biz/assign/syncAssignResult`, data)