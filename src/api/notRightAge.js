import request from '@/utils/request';
import { pref } from '@/utils/common';

// 非适龄儿童报名信息 - 列表
export const nonProperAgeStuList = (data, code) => request.post(`${pref + code}/biz/NonSchoolAge/getPage`, data)

// 非适龄儿童报名信息 - 新增
export const addNonProperAgeStu = (data, code) => request.post(`${pref + code}/biz/NonSchoolAge/add`, data)

// 非适龄儿童报名信息 - 删除
export const delNonProperAgeStu = (data, code) => request.post(`${pref + code}/biz/NonSchoolAge/delete`, data)
// 市五区非适龄儿童报名信息 - 列表
export const shiWuQuNonProperAgeStuList = (data, code) => request.post(`${pref + code}/five/idCard/list`, data)
// 市五区非适龄儿童报名信息 - 新增
export const shiWuQuAddNonProperAgeStu = (data, code) => request.post(`${pref + code}/five/idCard/create`, data)

// 市五区非适龄儿童报名信息 - 删除
export const shiWuQuDelNonProperAgeStu = (data, code) => request.post(`${pref + code}/five/idCard/delete`, data)
//涉县非适龄儿童报名信息列表
export const shexianNonProperAgeStuList=(data,code)=>request.post(`${pref + code}/biz/idCard/list`,data)
// 市五区非适龄儿童报名信息 - 新增
export const sheXianAddNonProperAgeStu = (data, code) => request.post(`${pref + code}/biz/idCard/create`, data)

// 市五区非适龄儿童报名信息 - 删除
export const sheXianDelNonProperAgeStu = (data, code) => request.post(`${pref + code}/biz/idCard/delete`, data)
