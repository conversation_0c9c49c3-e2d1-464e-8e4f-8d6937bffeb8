import request from '@/utils/request'
import { pref } from '@/utils/common';
export const getPWSchoolList = (data,code) => request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getList`, data)
export const updateSchoolaJiHuaShu = (data,code) => request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/saveOrUpdate`, data)
export const zhiYuanZhuangTai = (data,code) => request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getZyStatus`, data)
export const daiPaiWeiStudent=(data,code)=>request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getStudentByStepAndSchoolId`, data)
export const paiWeiChengGongStudent=(data,code)=>request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getStudentByPwResult`, data)
export const queRenPaiWei=(data,code)=>request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/savePwResult`, data)
export const resetPw=(data,code)=>request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/resetPw`, data)
export const huoQuChengGongPaiWei=(data,code)=>request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getPwResult`, data)
export const getPwSchoolStudentList=(code)=>request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getPwSchoolStudentList`)
export const getStudentByStepAndSchoolList=(data,code)=>request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getStudentByStepAndSchoolList`, data)
export const getPxPWSchoolList = (data,code) => request.post(`${pref+code}/biz/TbPaiWeiSchoolSettingController/getPwSchoolList`, data)
