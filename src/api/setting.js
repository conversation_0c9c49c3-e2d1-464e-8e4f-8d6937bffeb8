import request from '@/utils/request';
import { pref } from '@/utils/common';

// 获取时间设置
export const getTimeSetting = (data, code) => request.post(`${pref + code}/biz/registrationTime/list`, data)

// 获取民办时间设置
export const getTimePrivate = (data, code) => request.post(`${pref + code}/privatee/biz/time/detail`, data)

// 获取民办时间设置
export const updateTimePrivate = (data, code) => request.post(`${pref + code}/privatee/biz/time/update`, data)

// 保存时间设置
export const saveTimeSetting = (data, code) => request.post(`${pref + code}/biz/registrationTime/create`, data)

// 获取截止修改报名时间
export const getDeadline = (data, code) => request.post(`${pref + code}/biz/registrationTime/deadline`, data)

// 保存截止修改报名时间
export const deadlineCreate = (data, code) => request.post(`${pref + code}/biz/registrationTime/deadlineCreate`, data)

// 学校 - 列表
export const schoolList = data => request.post('/user-api/center/dept/pageList', data)

// 学校 - 更新
export const uptSchool = data => request.post('/user-api/center/dept/updateSchool', data)

// 学校 - 新增
export const addSchool = data => request.post('/user-api/center/dept/createSchool', data)

// 学校 - 删
export const delSchool = data => request.post('/user-api/center/dept/delete', data)

// 学校 - 启
export const enableSchool = data => request.post('/user-api/center/dept/changeStatusNormal', data)

// 学校 - 禁
export const disableSchool = data => request.post('/user-api/center/dept/changeStatusDisabled', data)

// 学校 - 详情
export const schoolDetail = data => request.post('/user-api/center/dept/detail', data)

// 学校 - 批量导入
export const importSchool = data => request.post('/user-api/center/dept/importSchool', data)

//学校 内丘摇号导入
export const importLotterySchool = (data,code) => request.post(`${pref + code}/biz/excelConfig/importLotterySchool`, data)
//学校 平乡摇号设置新增
export const lotterySetSchool = (data,code) => request.post(`${pref + code}/biz/lottery/createLottery`, data)
export const delLotterySetSchool = (data,code) => request.post(`${pref + code}/biz/lottery/delLottery`, data)
//学校 平乡摇号设置更新
export const lotteryUpdateSchool = (data,code) => request.post(`${pref + code}/biz/lottery/updateLottery`, data)
//学校 平乡摇号列表
export const lotterySchoolList = (data,code) => request.post(`${pref + code}/biz/lottery/lotteryPageList`, data)
//学校 平乡摇号详情
export const lotterySchoolDetail = (data,code) => request.post(`${pref + code}/biz/lottery/lotterySchoolDetail`, data)
//学校 平乡摇号指标设置
export const lotterySetSchoolDetail = (data,code) => request.post(`${pref + code}/biz/lottery/lotterySetSchoolDetail`, data)
//学校 平乡摇号指标设置保存
export const lotterySetSchoolDetailSave = (data,code) => request.post(`${pref + code}/biz/lottery/lotterySetSchoolDetailSave`, data)
// 学校编辑 - 按学校查询招生类别
export const enrollTpBySchool = data => request.post('/user-api/center/dept/getEnrollType', data)

//获取学生信息
export const getStudentData = data => request.post('/user-api/center/studentCode/studentInfo', data)


// 学校新增 - 按学校查询招生类别
export const enrollTpBySchoolAdd = data => request.post('/user-api/center/dept/getEnrollType2AddSchool', data)

// 毕业小学学籍管理 - 列表
export const getList = data => request.post('/user-api/center/studentCode/pageList', data)
//添加修改学生学籍
export const addOrEdit =data=>request.post('/user-api/center/studentCode/saveStudentCode',data)
//删除学生学籍
export const del=data=>request.post('/user-api/center/studentCode/delete',data)
// 毕业小学学籍管理 - 批量导入
export const importStudentCode = data => request.post('/user-api/center/studentCode/importStudentCode', data)

// 招生范围 - 列表
export const getSchoolRangeList = data => request.post('/user-api/center/schoolRang/pageList', data)

// 招生范围 - 添加
export const createSchoolRange = data => request.post('/user-api/center/schoolRang/createSchoolRang', data)

// 招生范围 - 修改
export const updateSchoolRange = data => request.post('/user-api/center/schoolRang/updateSchoolRang', data)

// 招生范围 - 删除
export const deleteSchoolRange = data => request.post('/user-api/center/schoolRang/delete', data)

// 招生范围 - 批量导入
export const importSchoolRange = data => request.post('/user-api/center/schoolRang/importSchoolRang', data)

// 报名类别详情设置 - 列表
export const signUpSortDetailSetting = (data, code) => request.post(`${pref + code}/biz/fieldConfig/signUpSortDetailSetting`, data)

// 报名类别详情设置 - 编辑
export const signUpSortDetailSettingUpdate = (data, code) => request.post(`${pref + code}/biz/fieldConfig/signUpSortDetailSettingUpdate`, data)

//市五区报名时间获取详情
export const getBaoMingShiJianDetail = (data, code) => request.post(`${pref + code}/five/enrollTime/detail`, data)

//市五区修改报名时间
export  const getBaoMingShiJianDetailUpdate = (data, code) => request.post(`${pref + code}/five/enrollTime/update`, data)
//涉县报名时间获取详情
export const getSheXianBaoMingShiJianDetail = (data, code) => request.post(`${pref + code}/biz/enrollTime/detail`, data)

//涉县修改报名时间
export  const getSheXianBaoMingShiJianDetailUpdate = (data, code) => request.post(`${pref + code}/biz/enrollTime/update`, data)
//获取市五区学校新增报名截止时间
export  const getBaoMingShiJianCheck = (data, code) => request.post(`${pref + code}/five/enrollTime/check`, data)

//获取非市五区学校新增报名截止时间
export  const getAddSignCheck = (data, code) => request.post(`${pref + code}/biz/registrationTime/verifyDeadline`, data)
export  const getPrivateAddSignCheck = (data, code) => request.post(`${pref + code}/privatee/biz/time/check`, data)

//隆尧 补录名单
export  const getSupplementList = (data, code) => request.post(`${pref + code}/biz/supplementInfo/pageList`, data)
export  const updateSupplementInfo = (data, code) => request.post(`${pref + code}/biz/supplementInfo/updateSupplementInfo`, data)
export  const deleteSupplementInfo = (data, code) => request.post(`${pref + code}/biz/supplementInfo/delete`, data)
export  const importSupplementInfo = (data, code) => request.post(`${pref + code}/biz/supplementInfo/importSupplementInfo`, data)


// 招生范围
export const getSchoolRange = data => request.post('/user-api/center/schoolRang/list', data)
//涉县设置学校单独报名时间
export const  getTimeDetail=(data)=>request.post(`/user-api/center/schoolTime/detail`,data)
export const  saveSchoolTime=(data,code)=>request.post(`/user-api/center/schoolTime/saveSchoolTime`,data)
//涉县报名类别详情设置
export const getLeiBieList=(data,code)=>request.post(`${pref + code}/biz/signType/list`,data)
export const editLeiBie=(data,code)=>request.post(`${pref + code}/biz/signType/update`,data)
//涉县小学报名年龄设置
export const ageUpdatae=(data,code)=>request.post(`${pref + code}/biz/ageSet/update`,data)
export const ageDetail=(data,code)=>request.post(`${pref + code}/biz/ageSet/detail`,data)
//涉县学校关联
export const getGuanLianList=(data)=>request.post(`/user-api/center/schoolRelation/pageList`,data)
export const addGuanLian=(data)=>request.post("/user-api/center/schoolRelation/createSchoolRelation",data)
export const deleteJuniorGuanLian=(data)=>request.post("/user-api/center/schoolRelation/deleteJunior",data)
export const deletePrimaryGuanLian=(data)=>request.post("/user-api/center/schoolRelation/deletePrimary",data)
//小学对口初中 - 列表（峰峰矿区）- 暂存
// export const getSchoolRelation = data => request.post('/user-api/center/schoolRelation/list', data)

//小学对口初中 - 添加（峰峰矿区）- 暂存
// export const createSchoolRelation = data => request.post('/user-api/center/schoolRelation/createSchoolRelation', data)

//小学对口初中 - 删除小学（峰峰矿区）- 暂存
// export const deletePrimary = data => request.post('/user-api/center/schoolRelation/deletePrimary', data)

//小学对口初中 - 删除初中（峰峰矿区）- 暂存
// export const deleteJunior = data => request.post('/user-api/center/schoolRelation/deleteJunior', data)

// 功能开关
export const permissionList = (data, code) => request.post(`${pref + code}/biz/permission/list`, data)

// 功能开关更新
export const permissionSave = (data, code) => request.post(`${pref + code}/biz/permission/save`, data)

//宁晋单独接口
//宁晋-招生范围
export const getEnrollScope = data => request.post('/user-api/center/enrollmentScope/pageList', data)
//宁晋-新增招生范围
export const creatEnrollScope = data => request.post('/user-api/center/enrollmentScope/createEnrollScope', data)
//宁晋-更新招生范围
export const updateEnrollScope = data => request.post('/user-api/center/enrollmentScope/updateEnrollScope', data)
//宁晋-删除招生范围
export const deleteEnrollScope = data => request.post('/user-api/center/enrollmentScope/delete', data)
//宁晋-批量导入--招生范围
export const importEnrollScope = data => request.post('/user-api/center/enrollmentScope/importEnrollScope', data)
//宁晋-获取学校招生范围
export const getSchoolEnrollRang = data => request.post('/user-api/center/schoolEnrollRang/pageList', data)
//宁晋-获取学校招生类别
export const getSchoolEnrollType = data => request.post('/user-api/center/schoolEnrollRang/getEnrollType', data)
//宁晋-添加学校招生范围
export const addSchoolEnrollRang = data => request.post('/user-api/center/schoolEnrollRang/createSchoolEnrollRange', data)
//宁晋-获取招生区域
export const getEnrollArea = data => request.post('/user-api/center/enrollmentScope/list', data)
//宁晋-更新学校招生范围
export const updateSchoolEnrollRang = data => request.post('/user-api/center/schoolEnrollRang/updateSchoolEnrollRange', data)
//宁晋-删除学校招生范围
export const deleteSchoolEnrollRang = data => request.post('/user-api/center/schoolEnrollRang/delete', data)
//宁晋-批量导入--学校招生范围
export const importSchoolEnrollRang = data => request.post('/user-api/center/schoolEnrollRang/importSchoolEnrollRang', data)
//广宗-获取乡镇列表
export const getTownList = data => request.post('/user-api/center/town/getTownList', data)


//襄都-获取招生范围列表
export const getEnrollScopeList = data => request.post('/user-api/center/enrollmentScopeConfiguration/pageList', data)

//襄都-新增招生范围
export const creatEnrollScopeInfo = data => request.post('/user-api/center/enrollmentScopeConfiguration/createEnrollScope', data)
//襄都-更新招生范围
export const updateEnrollScopeInfo = data => request.post('/user-api/center/enrollmentScopeConfiguration/updateEnrollScope', data)
//襄都-删除招生范围
export const deleteEnrollScopeInfo = data => request.post('/user-api/center/enrollmentScopeConfiguration/removeEnrollScope', data)
//襄都-批量导入--招生范围
export const importEnrollScopeInfo = data => request.post('/user-api/center/enrollmentScopeConfiguration/importEnrollScope', data)
//襄都-获取招生范围列表
export const getSchoolEnrollScopeList = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/pageList', data)
//襄都-获取学校招生范围
export const getSchoolEnrollScope = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/getSchoolScope', data)
//襄都-获取街/镇列表
export const getStreetList = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/getStreetList', data)
//襄都-获取子级列表
export const getSubList = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/getSubList', data)
//襄都-添加学校招生范围
export const addSchoolScope = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/addSchoolScope', data)
//襄都-更新学校招生范围
export const updateSchoolScope = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/updateSchoolScope', data)
//襄都-校验学校招生范围
export const updateSchoolScopeCheck = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/updateSchoolScopeCheck', data)
//襄都-删除学校招生范围
export const deleteSchoolScope = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/removeSchoolScope', data)
//襄都-批量导入--学校招生范围
export const importSchoolScope = data => request.post('/user-api/center/schoolEnrollmentScopeConfiguration/importSchoolScope', data)
