import request from '@/utils/request'
import { pref } from '@/utils/common';


//清河—获取民办学校招生时间
export const getPrivateEnrollTime = (data, code) => request.post(`${pref + code}/biz/paiWeiSchool/enrollTimeIsEnd`, data)
//清河-获取民办派位学校列表
export const getPrivatePawWeiSchoolList = (data, code) => request.post(`${pref + code}/api/assignment/schoolList`, data)
//清河-公示派位结果
export const publicPawWeiSchoolList = (data, code) => request.post(`${pref + code}/api/assignment/publicAllocation`, data)

//清河-获取派位学生列表
export const assignmentStudentListApi = (schoolId, code) => request.post(`${pref + code}/api/assignment/studentList?schoolId=` + schoolId);
//清河-获取学校对象实例
export const schoolGetApi = (data, code) => request.post(`${pref + code}/api/assignment/getSchool`, data)
//清河-派位列表初始化
export const assignmentInitAssignmentApi = (id, code) => request.post(`${pref + code}/api/assignment/initAssignment`, {id});
// 派位用户用，派位完成。
export const assignmentConfirmationApi = (schoolId, assignmentId, agmtSchool, sourceType, successList, failList, code) => request.post(`${pref + code}/api/assignment/confirmation`, {
    schoolId,
    assignmentId,
    agmtSchool,
    sourceType,
    successList,
    failList
});
//重置派位状态
export const assignmentResetApi = (schoolId, code) => request.post(`${pref + code}/api/assignment/resetAssignment`, {schoolId})
//派位查看对口小学
export const assignmentPrimaryInfoApi = (data, code) => request.post(`${pref + code}/api/assignment/getPrimaryInfo`, data)
// 派位用户用，派位结果查看。
export const assignmentEndListApi = (school, status, pageNum, agmtSchool, sourceType, stuName, idCard, code) => request.post(`${pref + code}/api/assignment/endList?schoolId=` + school + '&pageNum=' + pageNum + '&status=' + status + "&agmtSchool=" + agmtSchool + "&sourceType=" + sourceType + "&stuName=" + stuName + "&idCard=" + idCard);
// 派位用户用，直接录取。
export const assignmentAutoPassApi = (data,code) => request.post(`${pref + code}/api/assignment/autoPass`, data);

// 派位用户用，派位结果导出。
export const assignmentDownloadEndListApi = (school, stuName, idCard, agmtSchool, sourceType, code) => request.post(`${pref + code}/api/assignment/downloadEndList?schoolId=` + school + "&agmtSchool=" + agmtSchool + "&sourceType=" + sourceType + "&stuName=" + stuName + "&idCard=" + idCard, {responseType: 'blob'});