import { login, logout } from '@/api/login'

const getDefaultState = () => ({
  token: null,
  userInfo: null,
  role: null,
  isFive: null,
  isBind: null,
  isDefaultPassword: null,
  isLoginConfirm: null,
  securityKey: null,// 微信扫码确认登录后获取
  deptCode: null,   // 区县编码
  period: null, // 学段
  schoolDetail:  null
})
const state = getDefaultState()

const mutations = {
  SET_TOKEN(state, val) {
    state.token = val
  },
  SET_USERINFO(state, val) {
    state.userInfo = val
  },
  SET_ROLE(state, val) {
    state.role = val
  },
  SET_ISFIVE(state, val) {
    state.isFive = val
  },
  SET_ISBIND(state, val) {
    state.isBind = val
  },
  SET_ISDEFAULTPASSWORD(state, val) {
    state.isDefaultPassword = val
  },
  SET_ISLOGINCONFIRM(state, val) {
    state.isLoginConfirm = val
  },
  SET_SECURITYKEY(state, val) {
    state.securityKey = val
  },
  SET_DEPTCODE(state, val) {
    state.deptCode = val
  },
  SET_PERIOD(state, val) {
    state.period = val
  },
  SET_SCHOOL_DETAIL(state, val){
    state.schoolDetail = val
  },
  SET_RESETSTATE(state) {
    state = Object.assign(state, getDefaultState())
  }
}
const actions = {
  login({ commit }, form) {
    return new Promise((resolve, reject) => {
      login(form).then(data => {
        // console.log(JSON.stringify(data,null,2))
        commit('SET_TOKEN', data.token)
        commit('SET_USERINFO', data)
        commit('SET_ROLE', data.roleCode)
        commit('SET_ISFIVE', data.isFive)
        commit('SET_ISBIND', data.binding)
        commit('SET_ISDEFAULTPASSWORD', data.defaultPasswordFlag)
        commit('SET_DEPTCODE', data.deptCode)
        commit('SET_PERIOD', data.schoolType ? data.schoolType : '0')
        commit('SET_SCHOOL_DETAIL', data)
        resolve(data)
      }).catch(err => {
        reject(err)
      })
    })
  },
  async logout({ commit }) {
    return new Promise((resolve) => {
      logout()
        .finally(() => {
          commit('SET_RESETSTATE', null)
          resolve()
        })
    })

  },
  clearState({ commit }) {
    commit('SET_RESETSTATE', null)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}