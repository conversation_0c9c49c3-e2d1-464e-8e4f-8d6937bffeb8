import VueRouter from 'vue-router'
import Vue from 'vue'
import Layout from '@/layouts/index.vue'

Vue.use(VueRouter)
// 峰峰矿区
// const fengfeng = ['130406']
// 临漳
// const linzhang = ['130423']
// 标准版
//    馆陶     武安      广平    大名     磁县     魏县    峰峰矿区   临漳     曲周     成安
// ['130433','130481','130432','130425','130427','130434','130406','130423','130435','130424']
// 非标准版
// 邱县 ['130430']

const routes = [
    {
        path: '/',
        component: Layout,
        redirect: '/index',
        meta: {
            hidden: false,
            title: '首页',
            roles: ['SUPER_ADMIN', 'COUNTY_ADMIN'],
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/home'),
                meta: {
                    hidden: false,
                    title: '首页',
                    roles: ['SUPER_ADMIN', 'COUNTY_ADMIN']
                }
            }
        ],
    },
    {
        path: '/sysConfig',
        component: Layout,
        meta: {
            hidden: false,
            title: '系统设置',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'clearSignData',
                component: () => import('@/views/sysConfig/clearSignData'),
                meta: {
                    hidden: false,
                    title: '清除学生报名数据',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'sysLog',
                component: () => import('@/views/sysConfig/sysLog'),
                meta: {
                    hidden: false,
                    title: '操作日志',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'sysAccounts',
                component: () => import('@/views/sysConfig/user/user.vue'),
                meta: {
                    hidden: false,
                    title: '账号管理',
                    roles: ['SUPER_ADMIN']
                }
            }
        ],
    },
    {
        path: '/signConfig',
        component: Layout,
        redirect: '/signConfig/index',
        meta: {
            hidden: false,
            title: '报名设置',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/signConfig/index'),
                meta: {
                    hidden: false,
                    title: '报名设置',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'signFieldSet',
                name: 'signFieldSet',
                component: () => import('@/views/signConfig/signFieldSet.vue'),
                meta: {
                    hidden: true,
                    title: '报名字段设置',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'signFieldSetDetail',
                name: 'signFieldSetDetail',
                component: () => import('@/views/signConfig/signFieldSetDetail'),
                meta: {
                    hidden: true,
                    title: '报名字段设置',
                    roles: ['SUPER_ADMIN']
                }
            }
        ],
    },
    {
        path: '/signFields',
        component: Layout,
        meta: {
            hidden: false,
            title: '报名填写字段',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'baseInfo',
                component: () => import('@/views/signConfig/baseInfo'),
                meta: {
                    hidden: false,
                    title: '基础信息',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'houseInfo',
                component: () => import('@/views/signConfig/houseInfo'),
                meta: {
                    hidden: false,
                    title: '房产信息',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'imageInfo',
                component: () => import('@/views/signConfig/imageInfo'),
                meta: {
                    hidden: false,
                    title: '照片管理',
                    roles: ['SUPER_ADMIN']
                }
            }
        ],
    },
    {
        path: '/signEntrance',
        component: Layout,
        redirect: '/signEntrance/index',
        meta: {
            hidden: false,
            title: '报名入口管理',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/signConfig/signEntrance'),
                meta: {
                    hidden: false,
                    title: '报名入口管理',
                    roles: ['SUPER_ADMIN']
                }
            },
        ],
    },
    {
        path: '/signExportSet',
        component: Layout,
        redirect: '/signExportSet/index',
        meta: {
            hidden: false,
            title: '导出报名表格配置',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/signExportSet'),
                meta: {
                    hidden: false,
                    title: '导出报名表格配置',
                    roles: ['SUPER_ADMIN']
                }
            },
        ],
    },
    {
        path: '/enrollment',
        component: Layout,
        meta: {
            hidden: false,
            title: '招生',
            roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL', 'SCHOOL_AUDITOR'],
        },
        children: [
            {
                path: 'kindergarten',
                component: () => import('@/views/enrollment/kindergarten'),
                meta: {
                    hidden: false,
                    title: '幼儿园报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    period: ['0', '1'],
                    privateeStatus: 3,
                    depts: ['130503','130502']
                }
            },
            {
                path: 'kindergartenAllocationList',
                component: () => import('@/views/enrollment/kindergartenAllocationList'),
                meta: {
                    hidden: false,
                    title: '幼儿园派位列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    period: ['0', '1'],
                    privateeStatus: 3,
                    depts: ['130503','130502']
                }
            },
            {
                path: 'primary',
                component: () => import('@/views/enrollment/primary'),
                meta: {
                    hidden: false,
                    title: '小学报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    period: ['0', '2'],
                    privateeStatus: 3,
                    depts: ['130525', '130581', '130528', '130532', '130527', '130530', '130524', '130526', '130503','130502', '130531','130582','130522','130534','130523', '130529', '130535']
                }
            },
            // 区县专用 (沙河130582)学校审核员
            {
                path: 'schoolAuditor',
                component: () => import('@/views/enrollment/schoolAuditor/index.vue'),
                meta: {
                    hidden: false,
                    title: '招生列表',
                    roles: ['SCHOOL_AUDITOR'],
                    period: ['0', '2', '3'],
                    privateeStatus: 3,
                    depts: ['130582','130532','130522']
                }
            },
            // {
            //     path: 'shaHe',
            //     component: () => import('@/views/enrollment/shaHe/index.vue'),
            //     meta: {
            //         hidden: false,
            //         title: '招生列表',
            //         roles: ['SCHOOL',],
            //         period: ['0', '2', '3'],
            //         depts: ['130582',]
            //     }
            // },
            {
                path: 'junior',
                component: () => import('@/views/enrollment/junior'),
                meta: {
                    hidden: false,
                    title: '初中报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    period: ['0', '3'],
                    privateeStatus: 3,
                    depts: ['130581', '130528', '130532', '130527', '130530', '130524', '130526', '130503', '130502', '130531','130582','130522','130534', '130535']
                }
            },
            // 区县专用 (隆尧130430)
            {
                path: 'junior_longyao',
                component: () => import('@/views/counties/qiuxian/enrollment/junior'),
                meta: {
                    hidden: false,
                    title: '城区初中报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR'],
                    isFive: false,
                    period: ['0', '3'],
                    depts: ['130525','130529']
                }
            },
            {
                path: 'junior_xzlongyao',
                component: () => import('@/views/counties/qiuxian/enrollment/xzjunior'),
                meta: {
                    hidden: false,
                    title: '乡镇初中报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR'],
                    depts: ['130525',]
                }
            },
            {
                path: 'junior_schoollongyao',
                component: () => import('@/views/counties/qiuxian/enrollment/schoolJunior'),
                meta: {
                    hidden: false,
                    title: '初中报名列表',
                    roles: ['SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    depts: ['130525','130529']
                }
            },
            {
                path: 'assign_longyao',
                component: () => import('@/views/counties/qiuxian/enrollment/assign'),
                meta: {
                    hidden: false,
                    title: '派位成功学生列表',
                    roles: ['SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    depts: ['130525']
                }
            },
            // 区县专用 (隆尧130430)
            {
                path: 'addAd_longyao',
                component: () => import('@/views/counties/qiuxian/enrollment/addAd/index'),
                meta: {
                    hidden: true,
                    title: '添加报名',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    privateeStatus: 3,
                    depts: ['130525', '130581', '130528', '130532', '130530', '130524', '130526', '130503', '130502','130531','130582','130529']
                }
            },
            {
                path: 'addAd_julu',
                component: () => import('@/views/counties/qiuxian/enrollment/addAd/julu'),
                meta: {
                    hidden: true,
                    title: '添加报名',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    privateeStatus: 3,
                    depts: ['130529']
                }
            },
            //内丘
            {
                path: 'junior',
                component: () => import('@/views/enrollment/junior'),
                meta: {
                    hidden: false,
                    title: '初中报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    period: ['0', '3'],
                    privateeStatus: 3,
                    depts: ['130523']
                }
            },
            {
                path: 'juniorYaoHao',
                component: () => import('@/views/enrollment/neiQiu/index.vue'),
                meta: {
                    hidden: false,
                    title: '初中摇号报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    period: ['0', '3'],
                    privateeStatus: 3,
                    depts: ['130523']
                }
            },
            {
                path: 'juniorPingXiang',
                component: () => import('@/views/enrollment/pingXiang/index.vue'),
                meta: {
                    hidden: false,
                    title: '初中摇号报名列表',
                    roles: ['COUNTY_ADMIN','SCHOOL'],
                    period: ['0', '3'],
                    privateeStatus: 3,
                    depts: ['130532']
                }
            },
            {
                path: 'privatePrimary',
                component: () => import('@/views/privateEnrollment/primary/list'),
                meta: {
                    hidden: false,
                    title: '民办小学报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus: 2,
                    depts: [ '130532', '130503', '130502', '130582','130523','130532','130527','130502','130522']
                }
            },
            {
                path: 'privatePrimaryAllocationList',
                component: () => import('@/views/privateEnrollment/primaryAllocationList/list.vue'),
                meta: {
                    hidden: false,
                    title: '民办小学派位列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus: 2,
                    depts: ['130582']
                }
            },
            {
                path: 'privateJunior',
                component: () => import('@/views/privateEnrollment/junior/list'),
                meta: {
                    hidden: false,
                    title: '民办初中报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    privateeStatus: 2,
                    depts: ['130532', '130503', '130502','130534','130532','130527','130502']
                }
            },
            {
                path: 'privateAddAd',
                component: () => import('@/views/privateEnrollment/addAd/index'),
                meta: {
                    hidden: true,
                    title: '添加报名',
                    roles: ['COUNTY_ADMIN', 'SCHOOL'],
                    isFive: false
                }
            },
            {
                path: 'addAd',
                component: () => import('@/views/enrollment/addAd/index'),
                meta: {
                    hidden: true,
                    title: '添加报名',
                    roles: ['COUNTY_ADMIN', 'SCHOOL'],
                }
            },
            {
                path: 'adjust',
                component: () => import('@/views/enrollment/adjust'),
                meta: {
                    hidden: false,
                    period: ['0', '2','3'],
                    title: '调剂到本校学生',
                    roles: ['SCHOOL'],
                    depts: ['130525', '130581', '130528', '130530', '130524', '130526', '130503','130529','130523','130535','130522']
                }
            },
            {
                path: 'adjust_private',
                component: () => import('@/views/enrollment/adjust'),
                meta: {
                    hidden: false,
                    period: ['0', '2','3'],
                    title: '调剂到本校学生',
                    privateeStatus: 3,
                    roles: ['SCHOOL'],
                    depts: ['130582']
                }
            },
            // 区县专用 (内丘130523)
            {
                path: 'addAdNeiQiu',
                component: () => import('@/views/enrollment/neiQiu/addAd/index.vue'),
                meta: {
                    hidden: true,
                    title: '添加报名',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    privateeStatus: 3,
                    depts: ['130523']
                }
            },
        ],
    },
    {
        path: '/dataStatistics',
        component: Layout,
        meta: {
            hidden: false,
            title: '数据统计',
            roles: ['COUNTY_ADMIN'],
        },
        children: [
            {
                path: 'kindergarten',
                component: () => import('@/views/dataStatistics/kindergarten'),
                meta: {
                    hidden: false,
                    title: '幼儿园统计',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130503','130502']
                }
            },
            {
                path: 'primaryTown',
                component: () => import('@/views/dataStatistics/primaryTown'),
                meta: {
                    hidden: false,
                    title: '乡镇小学统计',
                    roles: ['COUNTY_ADMIN'],
                }
            },
            {
                path: 'primaryArea',
                component: () => import('@/views/dataStatistics/primaryArea'),
                meta: {
                    hidden: false,
                    title: '城区小学统计',
                    roles: ['COUNTY_ADMIN'],
                }
            },
            {
                path: 'juniorTown',
                component: () => import('@/views/dataStatistics/juniorTown'),
                meta: {
                    hidden: false,
                    title: '乡镇初中统计',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130524', '130531','130522', '130535', '130525', '130581','130527','130523','130528','130532','130534','130526','130582','130533','130502','130503','130530']
                }
            },
            {
                path: 'juniorArea',
                component: () => import('@/views/dataStatistics/juniorArea'),
                meta: {
                    hidden: false,
                    title: '城区初中统计',
                    roles: ['COUNTY_ADMIN'],
                }
            },
            {
                path: 'privatePrimary',
                component: () => import('@/views/dataStatistics/privatePrimary'),
                meta: {
                    hidden: false,
                    title: '民办小学统计',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    privateeStatus: 2,
                    depts: ['130532', '130503', '130502','130582','130523','130532','130527','130522']
                }
            },
            {
                path: 'privateJunior',
                component: () => import('@/views/dataStatistics/privateJunior'),
                meta: {
                    hidden: false,
                    title: '民办初中统计',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    privateeStatus: 2,
                    depts: [ '130532', '130502', '130503','130534','130532','130527']
                }
            },
        ],
    },
    {
        path: '/dataStatisticsSchool',
        component: Layout,
        redirect: '/dataStatisticsSchool/index',
        meta: {
            hidden: false,
            title: '数据统计',
            roles: ['SCHOOL']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/dataStatistics/school'),
                meta: {
                    hidden: false,
                    title: '数据统计',
                    roles: ['SCHOOL']
                }
            }
        ],
    },
    {
        path: '/policy',
        component: Layout,
        meta: {
            hidden: false,
            title: '政策公告',
            roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL']
        },
        children: [
            {
                path: 'policyList',
                component: () => import('@/views/policy/policy'),
                meta: {
                    hidden: false,
                    title: '政策公告',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL']
                }
            },
            {
                path: 'homeSetting',
                component: () => import('@/views/policy/homeSetting.vue'),
                meta: {
                    hidden: false,
                    title: '首页设置',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL']
                }
            }
        ],
    },
    {
        path: '/dataVerify',
        component: Layout,
        meta: {
            hidden: false,
            title: '数据核对',
            roles: ['COUNTY_ADMIN']
        },
        children: [
            {
                path: 'police',
                component: () => import('@/views/dataVerify/police.vue'),
                meta: {
                    hidden: false,
                    title: '公安信息核对-户口',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'policeResidencePermit',
                component: () => import('@/views/dataVerify/policeResidencePermit.vue'),
                meta: {
                    hidden: false,
                    title: '公安信息核对-居住证',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'property',
                component: () => import('@/views/dataVerify/property.vue'),
                meta: {
                    hidden: false,
                    title: '房管数据核对',
                    roles: ['COUNTY_ADMIN']
                }
            }
        ],
    },
    {
        path: '/unitedPolice',
        component: Layout,
        meta: {
            hidden: false,
            title: '报名信息',
            roles: ['POLICE'],
            depts: ['130433', '130481', '130432', '130425', '130427', '130434',
                '130406', '130423', '130431', '130407', '130408', '130430']
        },
        children: [
            {
                path: 'police',
                component: () => import('@/views/unitedDept/police.vue'),
                meta: {
                    hidden: false,
                    title: '户口',
                    roles: ['POLICE'],
                    depts: ['130433', '130481', '130432', '130425', '130427', '130434',
                        '130406', '130423', '130431', '130407', '130408', '130430']
                }
            },
            {
                path: 'policeResidencePermit',
                component: () => import('@/views/unitedDept/policeResidencePermit.vue'),
                meta: {
                    hidden: false,
                    title: '居住证',
                    roles: ['POLICE'],
                    depts: ['130433', '130481', '130432', '130425', '130427', '130434',
                        '130406', '130423', '130431', '130407', '130408', '130430']
                }
            },
        ],
    },
    {
        path: '/unitedProperty',
        component: Layout,
        meta: {
            hidden: false,
            title: '报名信息',
            isFive: false,
            roles: ['HOUSE']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/unitedDept/property.vue'),
                meta: {
                    hidden: false,
                    title: '报名信息',
                    roles: ['HOUSE']
                }
            },
        ],
    },
    {
        path: '/setting',
        component: Layout,
        meta: {
            hidden: false,
            title: '设置',
            roles: ['COUNTY_ADMIN', 'CITY_ADMIN']
        },
        children: [
            {
                path: 'studentStatus',
                component: () => import('@/views/setting/studentStatusManage'),
                meta: {
                    hidden: false,
                    title: '毕业小学学籍管理',
                    roles: ['COUNTY_ADMIN'],
                }
            },
            {
                path:'graduationPrimarySchoolManagement',
                component: () => import('@/views/setting/graduationPrimarySchoolManagement'),
                meta: {
                    hidden: false,
                    title: '毕业小学学校管理',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130528','130534','130526','130532','130502']
                }
            },
            {
                path: 'categoryDetail',
                component: () => import('@/views/setting/categoryDetail'),
                meta: {
                    hidden: false,
                    title: '报名类别详情设置',
                    roles: ['COUNTY_ADMIN'],
                }
            },
            {
                path: 'schoolManage',
                component: () => import('@/views/setting/schoolManage/index.vue'),
                meta: {
                    hidden: false,
                    title: '学校管理',
                    roles: ['COUNTY_ADMIN']
                }
            },

            {
                path: 'schoolManageAcct',
                component: () => import('@/views/setting/schoolManage/acct.vue'),
                meta: {
                    hidden: true,
                    title: '学校账号管理',
                    roles: ['COUNTY_ADMIN']
                },
                beforeEnter: (to, from, next) => {
                    if (from.fullPath == '/setting/schoolManage') {
                        next()
                    } else {
                        // 其它路由强制跳转至列表页
                        next('/setting/schoolManage')
                    }
                },
            },
            {
                path: 'eduAudit',
                component: () => import('@/views/setting/eduAuditAcct/index.vue'),
                meta: {
                    hidden: false,
                    title: '教育局审核账号',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'enrollmentScope',
                component: () => import('@/views/setting/enrollmentScope/index.vue'),
                meta: {
                    hidden: false,
                    title: '招生范围配置',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130528']
                }
            },
            {
                path: 'enrollRangeSchool',
                component: () => import('@/views/setting/enrollRangeSchool/index.vue'),
                meta: {
                    hidden: false,
                    title: '学校招生范围',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130528']
                }
            },
            {
                path: 'enrollRangeDetailSchool',
                component: () => import('@/views/setting/enrollRangeSchool/detail.vue'),
                meta: {
                    hidden: true,
                    title: '学校招生范围详情',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130528']
                }
            },
            // 襄都区 独有招生范围
            {
                path: 'enrollmentScopeConfiguration',
                component: () => import('@/views/setting/enrollmentScopeConfiguration/index.vue'),
                meta: {
                    hidden: false,
                    title: '招生范围配置',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130502']
                }
            },
              {
                path: 'schoolenrollmentscopeconfiguration',
                component: () => import('@/views/setting/schoolenrollmentscopeconfiguration/index.vue'),
                meta: {
                    hidden: false,
                    title: '学校招生范围',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130502']
                }
            },
            {
                path: 'schoolenrollmentscopeconfigurationEdit',
                component: () => import('@/views/setting/schoolenrollmentscopeconfiguration/detail.vue'),
                meta: {
                    hidden: true,
                    title: '学校招生范围详情',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130502']
                }
            },
            {
                path: 'enrollRange',
                component: () => import('@/views/setting/enrollRange/index.vue'),
                meta: {
                    hidden: false,
                    title: '学校招生范围',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130526', '130582', '130530', '130503', '130533', '130532',
                        '130523', '130527', '130581', '130525', '130535', '130522', '130524', '130531', '130529', '130524','130534']
                }
            },
            {
                path: 'enrollRangeDetail',
                component: () => import('@/views/setting/enrollRange/detail.vue'),
                meta: {
                    hidden: true,
                    title: '学校招生范围详情',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130526', '130582', '130530', '130502', '130503', '130533', '130532',
                        '130523', '130527', '130581', '130525', '130535', '130522', '130524', '130531', '130529', '130524','130534']
                }
            },
            {
                path: 'supplementInfo',
                component: () => import('@/views/setting/supplementInfo/index.vue'),
                meta: {
                    hidden: false,
                    title: '补录名单',
                    roles: ['COUNTY_ADMIN'],
                    depts: []
                }
            },
            {
                path: 'timeSettingCounty',
                component: () => import('@/views/setting/timeSetting/county.vue'),
                meta: {
                    hidden: false,
                    title: '乡镇报名时间',
                    roles: ['COUNTY_ADMIN'],
                }
            },
            {
                path: 'timeSettingCity',
                component: () => import('@/views/setting/timeSetting/city.vue'),
                meta: {
                    hidden: false,
                    title: '城区报名时间',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'timeSettingExpiration',
                component: () => import('@/views/setting/timeSetting/expiration.vue'),
                meta: {
                    hidden: false,
                    title: '截止修改报名时间',
                    roles: ['COUNTY_ADMIN']
                }
            },
						{
						    path: 'privateTime',
						    component: () => import('@/views/setting/timeSetting/privateTime.vue'),
						    meta: {
						        hidden: false,
						        title: '民办报名时间',
						        isFive: false,
						        roles: ['COUNTY_ADMIN'],
						        privateeStatus:2,
						        depts: ['130503', '130502', '130532','130582', '130534','130532','130522', '130523','130527']
						    }
						},
						{
						    path: 'privateExpiration',
						    component: () => import('@/views/setting/timeSetting/privateExpiration.vue'),
						    meta: {
						        hidden: false,
						        title: '民办截止修改报名时间',
						        isFive: false,
						        roles: ['COUNTY_ADMIN'],
						        privateeStatus:2,
						        depts: ['130503', '130502', '130532', '130582','130532','130522', '130523','130527']
						    }
						},
            {
                path: 'areaRiZhi',
                component: () => import('@/views/setting/areaRiZhi/index'),
                meta: {
                    hidden: false,
                    title: '操作日志',
                    roles: ['COUNTY_ADMIN']
                }
            },
            /*{
                path: 'shiWuQuBaoMingShiJian',
                component: () => import('@/views/shiWuQu/setting/shiWuQuBaoMingShiJian/index'),
                meta: {
                    hidden: false,
                    title: '报名时间',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130526', '130528', '130530',  '130533',
                        '130523', '130527', '130535', '130522', '130524',  '130529', '130524']

                }
            },*/
            {
                path: 'notRightAge',
                component: () => import('@/views/setting/notRightAge'),
                meta: {
                    hidden: false,
                    title: '非适龄儿童报名信息',
                    roles: ['COUNTY_ADMIN'],
                }
            },
						{
						    path: 'adSwitchSet',
						    component: () => import('@/views/setting/adSwitchSet'),
						    meta: {
						        hidden: false,
						        title: '报名开关设置',
						        roles: ['CITY_ADMIN'],
						    }
						},
            {
                path: 'yaoHaoPeiZhi',
                component: () => import('@/views/setting/pingXiang/index.vue'),
                meta: {
                    hidden: false,
                    title: '二次报名摇号配置',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130532']
                }
            },
        ],
    },
    {
        path: '/index',
        component: Layout,
        redirect: '/shiJiShouYe/index',
        meta: {
            hidden: false,
            title: '首页',
            roles: ['CITY_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/shiTongJi/index'),
                meta: {
                    hidden: false,
                    title: '首页',
                    roles: ['CITY_ADMIN']
                }
            },]
    },
    {
        path: '/studentList',
        component: Layout,
        redirect: '/studentList/index',
        meta: {
            hidden: false,
            title: '学生列表',
            roles: ['CITY_ADMIN','KE_FU']
        },
        children: [
            {
                path: 'youErYuan',
                component: () => import('@/views/cityEdu/studentList/youErYuan.vue'),
                meta: {
                    hidden: false,
                    title: '幼儿园学生列表',
                    period: ['0', '1'],
                    roles: ['CITY_ADMIN','KE_FU']
                }
            },
            {
                path: 'index',
                component: () => import('@/views/cityEdu/studentList/index'),
                meta: {
                    hidden: false,
                    title: '小学学生列表',
                    period: ['0', '2'],
                    roles: ['CITY_ADMIN','KE_FU']
                }
            },
            {
                path: 'chuZhongStudentList',
                component: () => import('@/views/cityEdu/studentList/chuZhongStudentList'),
                meta: {
                    hidden: false,
                    title: '初中学生列表',
                    period: ['0', '3'],
                    roles: ['CITY_ADMIN','KE_FU']
                }
            }
        ],
    },
    {
        path: '/tiaoJiJiLuList',
        component: Layout,
        redirect: '/tiaoJiJiLu/index',
        meta: {
            hidden: false,
            period: ['0', '2','3'],
            title: '调出记录',
            roles: ['CITY_ADMIN', 'COUNTY_ADMIN', 'SCHOOL']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/tiaoJiJiLu/index'),
                meta: {
                    hidden: false,
                    title: '调出记录列表',
                    roles: ['CITY_ADMIN', 'COUNTY_ADMIN', 'SCHOOL']
                }
            },
        ],
    },
    {
        path: '/quXianRiZhi',
        component: Layout,
        redirect: '/quXianRiZhi/index',
        meta: {
            hidden: false,
            title: '区县操作日志',
            roles: ['SUPER_ADMIN', 'CITY_ADMIN']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/RiZhi/index'),
                meta: {
                    hidden: false,
                    title: '区县操作日志',
                    roles: ['SUPER_ADMIN', 'CITY_ADMIN']
                }
            },
        ],
    },
    {
        path: '/tiaoJiList',
        component: Layout,
        redirect: '/tiaoJiList/index',
        meta: {
            hidden: false,
            title: '调入列表',
            period: ['0', '2','3'],
            roles: ['COUNTY_ADMIN', 'SCHOOL']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/tiaoJiLieBiao/xiaoXueTiaoJiLieBiao/index'),
                meta: {
                    hidden: false,
                    title: '小学调入列表',
                    roles: ['COUNTY_ADMIN', 'SCHOOL']
                }
            },
            // {
            //   path: 'chuZhongStudentList',
            //   component: () => import('@/views/tiaoJiLieBiao/chuZhongTiaoJiLieBiao/index'),
            //   meta: {
            //     hidden: false,
            //     title: '初中调入列表',
            //     roles: [ 'COUNTY_ADMIN', 'SCHOOL']
            //   }
            // }
        ],
    },
    {
        path: '/dispatch',
        component: Layout,
        redirect: '/dispatch/assignmentIndex',
        meta: {
            hidden: false,
            title: '电脑派位',
            depts: ['130430', '130423', '130434', '130408', '130435','130525','130534','130529','130582','130531','130533','130532','130526','130581'],
            roles: ['LOCATION']
        },
        //, 'SCHOOL'
        children: [{
            path: 'assignmentIndex',
            component: () => import('@/views/dispatch/qiuXian/assignmentIndex'),
            meta: {
                hidden: false,
                title: '电脑派位',
                depts: ['130430','130525','130529'],
                roles: ['LOCATION']
            }
        },
            {
                path: 'luQUJieGuo',
                component: () => import('@/views/dispatch/qiuXian/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130430','130525','130529'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwjhs',
                component: () => import('@/views/dispatch/qiuXian/pwjhs.vue'),
                meta: {
                    hidden: false,
                    title: '派位设置',
                    depts: ['130430','130525','130529'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentLocal',
                component: () => import('@/views/dispatch/qiuXian/assignmentLocal'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430','130525','130529'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentSchoolList',
                component: () => import('@/views/dispatch/qiuXian/assignmentSchoolList'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430','130525','130529'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment',
                component: () => import('@/views/dispatch/qiuXian/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430','130525','130529'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentEndList',
                component: () => import('@/views/dispatch/qiuXian/assignmentEndList'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430','130525','130529'],
                    roles: ['LOCATION']
                }
            },
            //平乡 派位
            {
                path: 'assignmentIndex_pingXiang',
                component: () => import('@/views/dispatch/pingXiang/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130532'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuo_pingXiang',
                component: () => import('@/views/dispatch/pingXiang/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130532'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwjhs_pingXiang',
                component: () => import('@/views/dispatch/pingXiang/pwjhs.vue'),
                meta: {
                    hidden: false,
                    title: '派位设置',
                    depts: ['130532'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentLocal_pingXiang',
                component: () => import('@/views/dispatch/pingXiang/assignmentLocal'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130532'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentSchoolList_pingXiang',
                component: () => import('@/views/dispatch/pingXiang/assignmentSchoolList'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130532'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_pingXiang',
                component: () => import('@/views/dispatch/pingXiang/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130532'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentEndList_pingXiang',
                component: () => import('@/views/dispatch/pingXiang/assignmentEndList'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430','130525','130529'],
                    roles: ['LOCATION']
                }
            },
            //临漳,魏县电脑派位
            {
                path: 'assignmentIndex_linzhang',
                component: () => import('@/views/dispatch/linZhang/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130423', '130434'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_linzhang',
                component: () => import('@/views/dispatch/linZhang/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130423', '130434'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoLinZhang',
                component: () => import('@/views/dispatch/linZhang/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130423', '130434'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwIndexYN',
                component: () => import('@/views/dispatch/yongNian/index'),
                meta: {
                    hidden: false,
                    title: '派位首页',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwSchoolListYN',
                component: () => import('@/views/dispatch/yongNian/school'),
                meta: {
                    hidden: false,
                    title: '派位学校',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwOperateYN',
                component: () => import('@/views/dispatch/yongNian/operate'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwResultYN',
                component: () => import('@/views/dispatch/yongNian/result'),
                meta: {
                    hidden: true,
                    title: '派位结果',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwIndexQZ',
                component: () => import('@/views/dispatch/quZhou/index'),
                meta: {
                    hidden: false,
                    title: '派位首页',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwSchoolListQZ',
                component: () => import('@/views/dispatch/quZhou/school'),
                meta: {
                    hidden: false,
                    title: '派位学校',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwOperateQZ',
                component: () => import('@/views/dispatch/quZhou/operate'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwResultQZ',
                component: () => import('@/views/dispatch/quZhou/result'),
                meta: {
                    hidden: true,
                    title: '派位结果',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwResultNJ',
                component: () => import('@/views/dispatch/ningJin/assignmentIndex'),
                meta:{
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130534'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'primaryPwResultNJ',
                component: () => import('@/views/dispatch/ningJin/assignmentSchoolList'),
                meta: {
                    hidden: true,
                    title: '电脑派位学校列表',
                    depts: ['130534'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'primaryPwSchoolListNJ',
                component: () => import('@/views/dispatch/ningJin/assignmentEndList'),
                meta: {
                    hidden: true,
                    title: '录取结果',
                    depts: ['130534'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'PwAssignmentNJ',
                component: () => import('@/views/dispatch/ningJin/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位录取工作',
                    depts: ['130534'],
                    roles: ['LOCATION']
                }
            },
            //沙河派位
            {
                path: 'assignmentIndex_shahe',
                component: () => import('@/views/dispatch/shahe/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130582'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_shahe',
                component: () => import('@/views/dispatch/shahe/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130582'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoShaHe',
                component: () => import('@/views/dispatch/shahe/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130582'],
                    roles: ['LOCATION']
                }
            },
            //广宗 派位
            {
                path: 'assignmentIndex_guangZong',
                component: () => import('@/views/dispatch/guangZong/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130531'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_guangZong',
                component: () => import('@/views/dispatch/guangZong/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130531'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoGuangZong',
                component: () => import('@/views/dispatch/guangZong/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130531'],
                    roles: ['LOCATION']
                }
            },
            //任泽 派位
            {
                path: 'assignmentIndex_renZe',
                component: () => import('@/views/dispatch/renZe/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130526'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_renZe',
                component: () => import('@/views/dispatch/renZe/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130526'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoRenZe',
                component: () => import('@/views/dispatch/renZe/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130526'],
                    roles: ['LOCATION']
                }
            },
            //威县 派位
            {
                path: 'assignmentIndex_weiXian',
                component: () => import('@/views/dispatch/weiXian/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130533'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_weiXian',
                component: () => import('@/views/dispatch/weiXian/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130533'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoWeiXian',
                component: () => import('@/views/dispatch/weiXian/luQuJieGuoWeiXian.vue'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130533'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoSchoolWeiXian',
                component: () => import('@/views/dispatch/weiXian/luQuJieGuo.vue'),
                meta: {
                    hidden: false,
                    title: '学校录取结果',
                    depts: ['1305331'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'paiWeiSetting',
                component: () => import('@/views/dispatch/weiXian/pwjhs.vue'),
                meta: {
                    hidden: false,
                    title: '参与摇号初中学校配置',
                    depts: ['130533'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'paiWeiStudentManage',
                component: () => import('@/views/dispatch/weiXian/studentManage.vue'),
                meta: {
                    hidden: false,
                    title: '参与摇号学生管理',
                    depts: ['130533'],
                    roles: ['LOCATION']
                }
            },
            //南宫 派位
            {
                path: 'assignmentIndex_nanGong',
                component: () => import('@/views/dispatch/nanGong/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130581'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_nanGong',
                component: () => import('@/views/dispatch/nanGong/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130581'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoNanGong',
                component: () => import('@/views/dispatch/nanGong/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130581'],
                    roles: ['LOCATION']
                }
            },
        ]
    },
    {
        path: '/my',
        component: Layout,
        redirect: '/my/index',
        meta: {
            hidden: false,
            title: '个人中心',
            roles: ['SUPER_ADMIN', 'LOCATION', 'COUNTY_ADMIN', 'AUDITOR', 'SCHOOL', 'POLICE', 'CITY_ADMIN',
                'ZHU_JIAN', 'ZI_GUI', 'SI_FA', 'DIAN', 'SHEN_PI', 'SHUI', 'REN_SHE', 'SHI_CHANG', 'SCHOOL_AUDITOR','KE_FU','HOUSE']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/my/index.vue'),
                meta: {
                    hidden: false,
                    title: '个人中心',
                    roles: ['LOCATION', 'SUPER_ADMIN', 'COUNTY_ADMIN', 'AUDITOR', 'SCHOOL', 'POLICE', 'HOUSE',
                        'ZHU_JIAN', 'ZI_GUI', 'SI_FA', 'DIAN', 'SHEN_PI', 'SHUI', 'REN_SHE', 'SHI_CHANG', 'CITY_ADMIN',  'SCHOOL_AUDITOR','KE_FU'
                    ]
                }
            }
        ],
    },
    {
        path: '/gongAn',
        component: Layout,
        redirect: '/gongAn/index',
        meta: {
            hidden: false,
            title: '公安局审核',
            roles: ['COUNTY_ADMIN', 'POLICE'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/gongAnShenHe/index'),
                meta: {
                    hidden: false,
                    title: '公安局审核',
                    roles: ['COUNTY_ADMIN', 'POLICE'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/zhuJian',
        component: Layout,
        redirect: '/zhuJian/index',
        meta: {
            hidden: false,
            title: '住建局审核',
            roles: ['COUNTY_ADMIN', 'ZHU_JIAN'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/zhuJianJuShenHe/index'),
                meta: {
                    hidden: false,
                    title: '住建局审核',
                    roles: ['COUNTY_ADMIN', 'ZHU_JIAN'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/ziGui',
        component: Layout,
        redirect: '/ziGui/index',
        meta: {
            hidden: false,
            title: '资规局审核',
            roles: ['COUNTY_ADMIN', 'ZI_GUI'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/ziGuiShenHe/index'),
                meta: {
                    hidden: false,
                    title: '资规局审核',
                    roles: ['COUNTY_ADMIN', 'ZI_GUI'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/siFa',
        component: Layout,
        redirect: '/siFa/index',
        meta: {
            hidden: false,
            title: '司法局审核',
            roles: ['COUNTY_ADMIN', 'SI_FA'],
            depts: ['130424']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/siFaShenHe/index'),
                meta: {
                    hidden: false,
                    title: '司法局审核',
                    roles: ['COUNTY_ADMIN', 'SI_FA'],
                    depts: ['130424']
                }
            }
        ],
    },
    {
        path: '/dianLiJu',
        component: Layout,
        redirect: '/dianLiJu/index',
        meta: {
            hidden: false,
            title: '电力局审核',
            roles: ['COUNTY_ADMIN', 'DIAN'],
            depts: ['130424']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/dianLiJuShenHe/index'),
                meta: {
                    hidden: false,
                    title: '电力局审核',
                    roles: ['COUNTY_ADMIN', 'DIAN'],
                    depts: ['130424']
                }
            }
        ],
    },
    {
        path: '/shuiLi',
        component: Layout,
        redirect: '/shuiLi/index',
        meta: {
            hidden: false,
            title: '水利局审核',
            roles: ['COUNTY_ADMIN', 'SHUI'],
            depts: ['130424']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/shuLiJuShenHe/index'),
                meta: {
                    hidden: false,
                    title: '水利局审核',
                    roles: ['COUNTY_ADMIN', 'SHUI'],
                    depts: ['130424']
                }
            }
        ],
    },
    {
        path: '/xingZheng',
        component: Layout,
        redirect: '/xingZheng/index',
        meta: {
            hidden: false,
            title: '行政审批局审核',
            roles: ['COUNTY_ADMIN', 'SHEN_PI'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/xingZhengShenHe/index'),
                meta: {
                    hidden: false,
                    title: '行政审批局审核',
                    roles: ['COUNTY_ADMIN', 'SHEN_PI'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/renShe',
        component: Layout,
        redirect: '/renShe/index',
        meta: {
            hidden: false,
            title: '人社局审核',
            roles: ['COUNTY_ADMIN', 'REN_SHE'],
            depts: ['130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/quZhou/renSheJu/index'),
                meta: {
                    hidden: false,
                    title: '人社局审核',
                    roles: ['COUNTY_ADMIN', 'REN_SHE'],
                    depts: ['130435']
                }
            }
        ],
    },
    {
        path: '/shiChang',
        component: Layout,
        redirect: '/shiChang/index',
        meta: {
            hidden: false,
            title: '市场监督管理局审核',
            roles: ['COUNTY_ADMIN', 'SHI_CHANG'],
            depts: ['130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/quZhou/shiChangJianDuGuanLiJu/index'),
                meta: {
                    hidden: false,
                    title: '市场监督管理局审核',
                    roles: ['COUNTY_ADMIN', 'SHI_CHANG'],
                    depts: ['130435']
                }
            }
        ],
    },
   /* {
        path: '/schoolBaoMing',
        component: Layout,
        meta: {
            hidden: false,
            title: '学校报名信息',
            roles: ['COUNTY_ADMIN', 'SCHOOL', 'AUDITOR'],
        },
        children: [
            {
                path: 'list',
                component: () => import('@/views/shiWuQu/shiWuQuYiXueXiaoBaoMing/list'),
                meta: {
                    hidden: false,
                    title: '选择拟报名学校',
                    roles: ['COUNTY_ADMIN', 'SCHOOL', 'AUDITOR'],
                }
            },
            {
                path: 'zhengJianBaoMing',
                component: () => import('@/views/shiWuQu/shiWuQuYiZhengJianBaoMing/list'),
                meta: {
                    hidden: false,
                    title: '不确定拟报名学校',
                    roles: ['COUNTY_ADMIN', 'SCHOOL', 'AUDITOR'],
                }
            },
            //, 'SCHOOL'
            {
                path: 'luquList',
                component: () => import('@/views/shiWuQu/luqu/list'),
                meta: {
                    hidden: false,
                    title: '录取报名列表',
                    roles: ['COUNTY_ADMIN'],
                }
            },
        ],
    },

    },*/
    {path: '/schoolAuditor',
        component: Layout,
        meta: {
            hidden: false,
            title: '账号管理',
            roles: ['SCHOOL'],
            depts: ['130582','130532']
        },
        children: [
            {
                path: 'acct',
                component: () => import('@/views/setting/schoolAuditor/acct.vue'),
                meta: {
                    hidden: false,
                    title: '账号管理',
                    roles: ['SCHOOL'],
                    depts: ['130582','130532'],
                }
            },
        ],

    },
    // {
    //     path: '/shuJuHeDui',
    //     component: Layout,
    //     meta: {
    //         hidden: false,
    //         title: '联合部门审核',
    //         roles: ['COUNTY_ADMIN'],
    //     },
    //     children: [
    //         {
    //             path: 'police',
    //             component: () => import('@/views/shiWuQu/shuJuHeDui/police'),
    //             meta: {
    //                 hidden: false,
    //                 title: '公安-户口审核',
    //                 roles: ['COUNTY_ADMIN'],
    //             }
    //         },
    //         {
    //             path: 'juZhuZhengShenHe',
    //             component: () => import('@/views/shiWuQu/shuJuHeDui/juZhuZhengShenHe'),
    //             meta: {
    //                 hidden: false,
    //                 title: '公安-居住证审核',
    //                 roles: ['COUNTY_ADMIN'],
    //             }
    //         },
    //         {
    //             path: 'property',
    //             component: () => import('@/views/shiWuQu/shuJuHeDui/property'),
    //             meta: {
    //                 hidden: false,
    //                 title: '房产部门',
    //                 roles: ['COUNTY_ADMIN'],
    //             }
    //         }
    //     ],
    // },
    // {
    //     path: '/unitedProperty',
    //     component: Layout,
    //     meta: {
    //         hidden: false,
    //         title: '报名信息',
    //         roles: ['HOUSE']
    //     },
    //     children: [
    //         {
    //             path: 'shiWuIndex',
    //             component: () => import('@/views/shiWuQu/unitedDept/property.vue'),
    //             meta: {
    //                 hidden: false,
    //                 title: '报名信息',
    //                 roles: ['HOUSE']
    //             }
    //         },
    //     ],
    // },

    {
        path: '/login',
        component: () => import('@/views/login/login.vue'),
        meta: {
            hidden: true
        }
    },
    {
        path: "/bind-wechat",
        component: () => import("@/views/login/bind-wechat.vue"),
        meta: {title: '绑定微信'}
    },
    {
        path: "/bind-wechat-auth",
        component: () => import("@/views/login/bind-wechat-auth.vue"),
        meta: {title: '绑定微信'}
    },
    {
        path: "/login-confirm",
        component: () => import("@/views/login/login-confirm.vue"),
    },
    {
        path: "/login-confirm-auth",
        component: () => import("@/views/login/login-confirm-auth.vue"),
    },
    {
        path: '/forgetPwd',
        name: 'forgetPwd',
        component: () => import('../views/login/forgetPwd'),
        meta: {title: '忘记密码'}
    },
    {
        path: '/editPwd',
        name: 'editPwd',
        component: () => import('../views/login/editPwd'),
        meta: {title: '修改密码'}
    },
    {
        path: '/weiXinTp',
        component: () => import('@/views/weiXinTp'),
        meta: {
            hidden: true,
        }
    },
    {
        path: '/404',
        component: () => import('@/views/404.vue'),
        meta: {
            hidden: true,
            title: '404'
        }
    },
    {
        path: '*',
        redirect: '/404'
    }
]

/**
 * 设置路由名称
 * @param {*} route
 */
const setRouteName = (route) => {
    if (!route.name) {
        route.name = route.meta?.title || route.path.replace('/', '-') || 'Index'
    }
    if (route.children && route.children.length > 0) {
        for (const item of route.children) {
            setRouteName(item)
        }
    }
}

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location, resolve, reject) {
    if (resolve || reject) {
        return originalPush.call(this, location, resolve, reject)
    }
    return originalPush.call(this, location).catch(() => {
    })
}

for (const route of routes) {
    setRouteName(route)
}


const router = new VueRouter({
    mode: 'hash',
    base: '/admin',
    routes
})

export default router
