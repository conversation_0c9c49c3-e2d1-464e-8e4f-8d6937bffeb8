import store from '@/store'
// 接口前缀,由 “pref+区县deptCode” 组成, 例 /shida-region-biz-service-130406/xxx/...
const pref = '/shida-region-biz-service-'
// 账号角色
const roleOptions = [
  // {
  //   label: '市教育局教育局管理员',
  //   value: '1'
  // },
  {
    label: '区县教育局管理员',
    value: '2'
  },
  {
    label: '区县教育局审核员',
    value: '3'
  },
  // {
  //   label: '学校管理员',
  //   value: '4'
  // },
  {
    label: '公安部门',
    value: '6'
  },
  {
    label: '房管部门',
    value: '7'
  },
  {
    label: '派位管理员',
    value: '8'
  },
  {
    label: '住建局',
    value: '9'
  },
  {
    label: '资规局',
    value: '10'
  },
  {
    label: '司法局',
    value: '11'
  },
  {
    label: '电力局',
    value: '12'
  },
  {
    label: '水利局',
    value: '13'
  },
  {
    label: '行政审批局',
    value: '14'
  },
  {
    label: '人社局',
    value: '15'
  },
  {
    label: '市场监督管理局',
    value: '16'
  },
  // {
  //     label: '学校审核员',
  //     value: '17'
  // },

]
// 学段
const schoolTypeOptions = [
  {
    label: '小学',
    value: '2'
  },
  {
    label: '初中',
    value: '3'
  }
]
// 学段
const xdSchoolTypeOptions = [
  {
    label: '幼儿园',
    value: '1'
  },
  {
    label: '小学',
    value: '2'
  },
  {
    label: '初中',
    value: '3'
  }
]
// 优抚类型
const priorityTypeList = [
  { value: '优抚对象' },
  { value: '教师子女' },
  { value: '人才引进' },
  { value: '优秀企业家子女' },
  { value: '其他-填写其他类型' }
]

// 丰南优抚类型
export const tangShanPriorityTypeList = [
  { value: '军人子女' },
  { value: '烈士子女' },
  { value: '公安英模' },
  { value: '因公牺牲伤残警察子女' },
  { value: '高层次人才子女' },
  { value: '其他-填写其他类型' }
]

// 学校审核状态
export const schoolReviewStatusEnum = [
  {value: 1, label: '待审核'},
  {value: 2, label: '通过'},
  {value: 3, label: '驳回-修改信息'},
  {value: 4, label: '驳回-不可再报'},
]
// 教育局审核状态
export const educationReviewStatusEnum = [
  {value: 1, label: '待审核'},
  {value: 2, label: '通过'},
  {value: 3, label: '驳回-修改信息'},
  {value: 4, label: '驳回-不可再报'},
]
// 是否本区毕业
export const graduateTypeEnum = [
  {value: 0, label: '本区毕业',name:'是',type:'primary'},
  {value: 1, label: '非本区毕业',name:'否',type:'info'}
]

// 唐山户籍所在地枚举
export const identifyTypeEnum = [
  {value: 0, label: '唐山各区户籍'},
  {value: 1, label: '唐山各县及外省市户籍'}
]
// 首页验证码字符串集合
const identifyCodes = [
  "0",
  "1",
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9",
  "a",
  "b",
  "c",
  "d",
  "e",
  "f",
  "g",
  "h",
  "i",
  "j",
  "k",
  "l",
  "m",
  "n",
  "o",
  "p",
  "q",
  "r",
  "s",
  "t",
  "u",
  "v",
  "w",
  "x",
  "y",
  "z",
]

// 图片前缀
const imgPrefix = () => {
	let prefix = `${ process.env.VUE_APP_BASE_API }${ pref }${ store.getters.deptCode }`
	return prefix
}

export {
  pref,
  roleOptions,
  schoolTypeOptions,
  xdSchoolTypeOptions,
  priorityTypeList,
  identifyCodes,
  imgPrefix
}
