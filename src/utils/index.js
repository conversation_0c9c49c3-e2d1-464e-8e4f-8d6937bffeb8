const deepClone = (value) => {
    // 值类型
    if (typeof value !== 'object' || value === null) {
        return value;
    }
    // 对象类型
    const result = Array.isArray(value) ? [] : {};

    Object.setPrototypeOf(result, Object.getPrototypeOf(value));

    for (const key in value) {
        if (Object.prototype.hasOwnProperty.call(value, key)) {
            result[key] = deepClone(value[key]);
        }
        // if (value.hasOwnProperty(key)) {
        //     result[key] = deepClone(value[key]);
        // }
    }
    return result;
}


export default {
    install(Vue) {
        Vue.prototype.deepClone = deepClone
    }
}