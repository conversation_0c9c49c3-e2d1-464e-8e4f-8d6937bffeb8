import axios from "axios";
import store from "@/store";
import router from "@/router";
import { Message, MessageBox } from "element-ui";

let instance = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 30000
})

instance.interceptors.request.use((config) => {
  config.headers['client-id'] = 'admin'
  if (store.getters.token) {
    config.headers.Authorization = `${store.getters.token}`
    config.headers.state = store.getters.securityKey
  }
  return config
})

instance.interceptors.response.use((response) => {
  if (response.status === 200) {
    // blob
    if (response.request.responseType == 'blob') {
      return response
    } else {
      return response.data.data
    }

  } else if (response.status === 401) {
    return Promise.reject(response.data)
  } else {
    return Promise.reject(response.data || { code: 500, message: '未知错误' })
  }
}, (error) => {
  const statusCode = error?.response?.status || error?.code || 500
  // console.log(statusCode)
  if (statusCode === 'ECONNABORTED') {
    Message.error({
      title: '提示',
      message: '网络异常，请稍后重试。',
      type: 'error',
    })
    return Promise.reject({ code: 500, message: '网络异常，请稍后重试。' })
  }
  const errorData = error.response?.data || { code: statusCode, message: '未知错误，请稍后重试。' }
  if (statusCode === 404) {
    errorData.message = '请求的资源不存在'
    Message.error({
      title: '提示',
      message: errorData.message,
      type: 'error',
    })
  } else if (statusCode === 401) {
    errorData.message = '您尚未登录，请登录后继续操作'
    MessageBox.confirm(errorData.message, '登录', {
      confirmButtonText: '登录',
      cancelButtonText: '取消',
      type: 'error'
    }).then(async () => {
      // 跳转登录
      await store.dispatch('user/clearState')
      await store.dispatch('settings/clearState')
      await router.push('/login')
    }).catch(async () => {
      // 跳转登录
      await store.dispatch('user/clearState')
      await store.dispatch('settings/clearState')
      await router.push('/login')
    })
  } else if (statusCode === 403) {
    MessageBox.confirm(errorData.message, '重新登录', {
      confirmButtonText: '重新登录',
      showCancelButton: false,
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: false,
      type: 'error'
    }).then(async () => {
      // 跳转登录
      await store.dispatch('user/clearState')
      await store.dispatch('settings/clearState')
      await router.push('/login')
    })
  }
  else if (statusCode === 500) {
    if(errorData.message){
      Message.error({
        title: '提示',
        message: errorData.message,
        type: 'error',
      })
    }else {
      errorData.message = '服务器内部错误'
      Message.error({
        title: '提示',
        message: errorData.message,
        type: 'error',
      })
    }

  } else if (statusCode === 701) {
    if (error.response.data.message) {
      Message.error({
        title: '提示',
        message: error.response.data.message,
        type: 'error',
      })
    }
    else {
      Message.error({
        title: '提示',
        message: decodeURI(error.response.headers.errmsg),
        type: 'error',
      })
    }
  } else {
    Message.error({
      title: '提示',
      message: errorData.message || '未知错误',
      type: 'error',
    })
  }
  return Promise.reject(errorData)
})

/*
 * 公共下载方法，已挂载到Vue.prototype，可在页面用this.$download访问
 * @param {*} url: 请求地址
 * @param {*} data: 请求参数
 * @param {*} fileType: 下载文件的类型
 * @param {*} fileName: 下载文件的名称
 * @param {*} timeout: 超时时长
 * @return Promise
 */
export const download = (url, data, fileType, fileName, timeout = 180000) => {
  return new Promise((resolve, reject) => {
    instance.post(url, data, {
      responseType: 'blob',
      headers:['Content-Disposition:attachment;filename=data.xls'],
      timeout: timeout
    }).then(response => {
      let resTp = {
        pdf: 'application/pdf',
        xls: 'application/x-xls',
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
        zip: 'application/zip'
      }
      // console.log(response.headers['content-disposition'].substr(20,response.headers['content-disposition'].length))
      let blob = new Blob([response.data], { type: resTp[fileType] })
      let downloadElement = document.createElement('a');
      let href = window.URL.createObjectURL(blob); //创建下载的链接
      downloadElement.href = href;
      // downloadElement.download = fileName //下载后文件名
      downloadElement.download =decodeURIComponent(response.headers['content-disposition'].substr(20,response.headers['content-disposition'].length))  //下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); //点击下载
      document.body.removeChild(downloadElement); //下载完成移除元素
      window.URL.revokeObjectURL(href)
      resolve("ok")
    }).catch(err => {
      reject(err)
    })
  })
}

export default instance
