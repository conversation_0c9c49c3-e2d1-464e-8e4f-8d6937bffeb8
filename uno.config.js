import { defineConfig, presetUno, presetAttributify, transformerDirectives } from 'unocss'

export default defineConfig({
    presets: [
        presetAttributify({ /* preset options */ }),
        presetUno() // 添加 UnoCSS 的默认样式预设
    ],
    transformers: [
        transformerDirectives()
    ],
    rules: [
        // 自定义宽度
        [/^sd-w-(\d+)$/, ([, d]) => ({ width: `${d}px` })],
        // 自定义高度
        [/^sd-h-(\d+)$/, ([, d]) => ({ height: `${d}px` })],
        // 自定义行高
        [/^sd-l-h-(\d+)$/, ([, d]) => ({ lineHeight: `${d}px` })],
        [/^sd-p-(\d+)$/, ([, d]) => ({ 'padding': `${d}px` })],
        [/^sd-p-t-(\d+)$/, ([, d]) => ({ 'padding-top': `${d}px` })],
        [/^sd-p-b-(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d}px` })],
        [/^sd-p-l-(\d+)$/, ([, d]) => ({ 'padding-left': `${d}px` })],
        [/^sd-p-r-(\d+)$/, ([, d]) => ({ 'padding-right': `${d}px` })],
        [/^sd-m-(\d+)$/, ([, d]) => ({ 'margin': `${d}px` })],
        [/^sd-m-t-(\d+)$/, ([, d]) => ({ 'margin-top': `${d}px` })],
        [/^sd-m-b-(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d}px` })],
        [/^sd-m-l-(\d+)$/, ([, d]) => ({ 'margin-left': `${d}px` })],
        [/^sd-m-r-(\d+)$/, ([, d]) => ({ 'margin-right': `${d}px` })],

        // 自定义背景色
        [/^sd-bg-(.+)$/, ([, d]) => ({ background: `#${d}` })],
        // 自定义字体颜色
        [/^sd-c-(.+)$/, ([, d]) => ({ color: `#${d}` })],
        [/^sd-color-(.+)$/, ([, d]) => ({ color: `#${d}` })],
        // 自定义flex-{d}
        [/^sd-flex-(\d+)$/, ([, d]) => ({ flex: `${d}` })],
        // 自定义border
        [/^(?:border|b)-(\d+)-(?:style-)?(.+)$/, ([, d, s]) => ({ border: `${d}px ${s}` })],
        // 自定义border-color
        [/^(?:border|b)-(\d+)-(?:style-)?(.+)-(.+)$/, ([, d, s, c]) => ({ border: `${d}px ${s} #${c}` })],
    ],
    shortcuts: [{
        // 横向居中布局
        'flex-center': 'flex items-center justify-center',
        // 右对齐布局
        'flex-end': 'flex items-center justify-end',
				// 左对齐布局
				'flex-start': 'flex items-center justify-start',
        // 垂直居中布局
        'flex-col-center': 'flex flex-col items-center'
    }]
})